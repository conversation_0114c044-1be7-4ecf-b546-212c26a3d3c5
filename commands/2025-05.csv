SessionID,EventTime,EventType,<PERSON>rna<PERSON>,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,Result
7367afe568124f22000000000000003a,2025-05-01T00:26:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ldapsearch -H ldap://************:9389 -x -D "cn=<EMAIL>,dc=avenir-test,dc=com" -W -b "dc=avenir-test,dc=com" "(&(objectClass=user)(|(st=av*)(displayName=se*)))",success
7367afe568124f22000000000000003a,2025-05-01T00:28:18+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ldapsearch -H ldap://************:9389 -x -D "cn=<EMAIL>,dc=avenir-test,dc=com" -W -b "dc=avenir-test,dc=com" "(&(objectClass=user)(|(mailnickname=se*)(displayName=se*)))",success
7367afe568124f22000000000000003a,2025-05-01T00:30:54+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ldapsearch -H ldap://************:9389 -x -D "cn=<EMAIL>,dc=avenir-test,dc=com" -W -b "dc=avenir-test,dc=com" "(&(objectClass=user)(|(cn=seth*)(displayName=se*)))",success
7973f5cc6812ce0a000000000000003a,2025-05-01T09:27:38+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '.';cd '.';cd '.';pwd;ls -la,success
b81394376812ce0a000000000000003a,2025-05-01T09:27:38+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,while true; do sleep 1;head -v -n 8 /proc/meminfo; head -v -n 2 /proc/stat /proc/version /proc/uptime /proc/loadavg /proc/sys/fs/file-nr /proc/sys/kernel/hostname; tail -v -n 32 /proc/net/dev;echo '==> /proc/df <==';df -l;echo '==> /proc/who <==';who;echo '==> /proc/end <==';echo '##Moba##'; done,success
7367afe568124f22000000000000003a,2025-05-01T09:28:08+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ldapsearch -H ldap://************:9389 -x -D "cn=<EMAIL>,dc=avenir-test,dc=com" -W -b "dc=avenir-test,dc=com" "(&(objectClass=user)(|(mailnickname=se*)(displayName=se*)))",success
7367afe568124f22000000000000003a,2025-05-01T09:28:50+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ldapsearch -H ldap://************:9389 -x -D "cn=<EMAIL>,dc=avenir-test,dc=com" -W -b "dc=avenir-test,dc=com" "(&(objectClass=user)(|(test=se*)(displayName=se*)))",success
9cb52ce368145404000000000000003a,2025-05-02T13:11:41+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,nslookup sider.ai,success
16ddcbac681461d50000000000000013,2025-05-02T14:11:17+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,screen 0ls,success
16ddcbac681461d50000000000000013,2025-05-02T14:11:28+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,screen -Dr f ,success
16ddcbac681461d50000000000000013,2025-05-02T14:11:47+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,  ls,success
10716fd96814a472000000000000003a,2025-05-02T18:54:42+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '.';cd '.';cd '.';pwd;ls -la,success
44cb88b06814a472000000000000003a,2025-05-02T18:54:42+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,while true; do sleep 1;head -v -n 8 /proc/meminfo; head -v -n 2 /proc/stat /proc/version /proc/uptime /proc/loadavg /proc/sys/fs/file-nr /proc/sys/kernel/hostname; tail -v -n 32 /proc/net/dev;echo '==> /proc/df <==';df -l;echo '==> /proc/who <==';who;echo '==> /proc/end <==';echo '##Moba##'; done,success
b4eff6d768198e30000000000000003a,2025-05-06T12:21:04+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '.';cd '.';cd '.';pwd;ls -la,success
9103b27368198e30000000000000003a,2025-05-06T12:21:04+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,while true; do sleep 1;head -v -n 8 /proc/meminfo; head -v -n 2 /proc/stat /proc/version /proc/uptime /proc/loadavg /proc/sys/fs/file-nr /proc/sys/kernel/hostname; tail -v -n 32 /proc/net/dev;echo '==> /proc/df <==';df -l;echo '==> /proc/who <==';who;echo '==> /proc/end <==';echo '##Moba##'; done,success
743c3d4768199074000000000000003a,2025-05-06T12:37:26+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ssh -p 9389 -v <EMAIL>,success
743c3d4768199074000000000000003a,2025-05-06T12:38:13+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ldapsearch -H ldap://iam-ldap.docsl.com:9389 -x -D "cn=<EMAIL>,dc=avenirapollo,dc=com" -W -b "dc=avenirapollo,dc=com" "(&(objectClass=user)(|(name=se*)(displayName=se*)))",success
c2c03ae16819a013000000000000003a,2025-05-06T13:37:23+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,while true; do sleep 1;head -v -n 8 /proc/meminfo; head -v -n 2 /proc/stat /proc/version /proc/uptime /proc/loadavg /proc/sys/fs/file-nr /proc/sys/kernel/hostname; tail -v -n 32 /proc/net/dev;echo '==> /proc/df <==';df -l;echo '==> /proc/who <==';who;echo '==> /proc/end <==';echo '##Moba##'; done,success
743c3d4768199074000000000000003a,2025-05-06T13:37:35+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ldapsearch -H ldap://iam-ldap.docsl.com:9389 -x -D "cn=<EMAIL>,dc=avenirapollo,dc=com" -W -b "dc=avenirapollo,dc=com" "(&(objectClass=user)(|(name=set*)(displayName=set*)))",success
df87f5326819a22b000000000000003a,2025-05-06T13:46:19+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,while true; do sleep 1;head -v -n 8 /proc/meminfo; head -v -n 2 /proc/stat /proc/version /proc/uptime /proc/loadavg /proc/sys/fs/file-nr /proc/sys/kernel/hostname; tail -v -n 32 /proc/net/dev;echo '==> /proc/df <==';df -l;echo '==> /proc/who <==';who;echo '==> /proc/end <==';echo '##Moba##'; done,success
f71df53b681ab8d30000000000000013,2025-05-07T09:35:17+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,ls,success
f71df53b681ab8d30000000000000013,2025-05-07T09:35:18+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,ls,success
f71df53b681ab8d30000000000000013,2025-05-07T09:35:19+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,ll,success
f71df53b681ab8d30000000000000013,2025-05-07T09:35:22+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,cd forensic_etl/,success
f71df53b681ab8d30000000000000013,2025-05-07T09:35:23+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,ll,success
f71df53b681ab8d30000000000000013,2025-05-07T09:35:24+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,cd ..,success
f71df53b681ab8d30000000000000013,2025-05-07T09:35:24+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,ls,success
f71df53b681ab8d30000000000000013,2025-05-07T09:35:28+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,cd _be,success
f71df53b681ab8d30000000000000013,2025-05-07T09:35:28+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,ls,success
f71df53b681ab8d30000000000000013,2025-05-07T09:35:35+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,cd forensic_be/,success
f71df53b681ab8d30000000000000013,2025-05-07T09:35:35+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,cd forensic_be/,success
f71df53b681ab8d30000000000000013,2025-05-07T09:35:36+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,ls,success
f71df53b681ab8d30000000000000013,2025-05-07T09:35:41+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,ps,success
f71df53b681ab8d30000000000000013,2025-05-07T09:35:43+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,tree,success
f71df53b681ab8d30000000000000013,2025-05-07T09:35:50+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,tree --help,success
f71df53b681ab8d30000000000000013,2025-05-07T09:36:00+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,tread,success
f71df53b681ab8d30000000000000013,2025-05-07T09:36:04+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,tree -ad,success
f71df53b681ab8d30000000000000013,2025-05-07T09:36:07+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,tree -a,success
f71df53b681ab8d30000000000000013,2025-05-07T09:36:14+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,tree -a,success
f71df53b681ab8d30000000000000013,2025-05-07T09:36:17+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,tree -d,success
f71df53b681ab8d30000000000000013,2025-05-07T09:36:33+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,tjls,success
f71df53b681ab8d30000000000000013,2025-05-07T09:36:34+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,ls,success
f71df53b681ab8d30000000000000013,2025-05-07T09:36:45+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,tree -help,success
f71df53b681ab8d30000000000000013,2025-05-07T09:36:54+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,tree --help,success
f71df53b681ab8d30000000000000013,2025-05-07T09:37:02+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,tree -L 1,success
f71df53b681ab8d30000000000000013,2025-05-07T09:41:02+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,ls,success
f71df53b681ab8d30000000000000013,2025-05-07T09:41:04+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,cd。。,success
f71df53b681ab8d30000000000000013,2025-05-07T09:41:09+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,cd ..,success
f71df53b681ab8d30000000000000013,2025-05-07T09:41:10+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,ls,success
f71df53b681ab8d30000000000000013,2025-05-07T09:41:16+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,cd forensic_etl/,success
f71df53b681ab8d30000000000000013,2025-05-07T09:41:17+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,ls,success
f71df53b681ab8d30000000000000013,2025-05-07T09:41:20+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,ll,success
f71df53b681ab8d30000000000000013,2025-05-07T09:41:25+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,tree -L 1,success
f71df53b681ab8d30000000000000013,2025-05-07T09:47:58+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,cd template/,success
f71df53b681ab8d30000000000000013,2025-05-07T09:47:58+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,ls,success
f71df53b681ab8d30000000000000013,2025-05-07T09:47:59+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,ll,success
f71df53b681ab8d30000000000000013,2025-05-07T09:50:38+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,ll,success
f71df53b681ab8d30000000000000013,2025-05-07T09:50:44+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,cd ..,success
f71df53b681ab8d30000000000000013,2025-05-07T09:50:44+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,ls,success
f71df53b681ab8d30000000000000013,2025-05-07T09:50:47+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,cd gen/,success
f71df53b681ab8d30000000000000013,2025-05-07T09:50:47+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,ll,success
f71df53b681ab8d30000000000000013,2025-05-07T09:50:51+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,tre  -L 1,success
f71df53b681ab8d30000000000000013,2025-05-07T09:55:24+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,docker ps,success
f71df53b681ab8d30000000000000013,2025-05-07T09:55:35+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,docker volume ls,success
f71df53b681ab8d30000000000000013,2025-05-07T09:56:09+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,docker volume inspect soc_volume,success
f71df53b681ab8d30000000000000013,2025-05-07T09:56:22+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,docker volume inspect soc_tmp,success
f71df53b681ab8d30000000000000013,2025-05-07T09:56:39+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,fdisk -l,success
f71df53b681ab8d30000000000000013,2025-05-07T09:57:30+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,docker volume ls,success
f71df53b681ab8d30000000000000013,2025-05-07T09:57:34+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,cd ..,success
f71df53b681ab8d30000000000000013,2025-05-07T09:57:36+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,ag soc_volume,success
f71df53b681ab8d30000000000000013,2025-05-07T09:57:49+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,ag soc_tmp,success
f71df53b681ab8d30000000000000013,2025-05-07T09:58:17+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,cd ..,success
f71df53b681ab8d30000000000000013,2025-05-07T09:58:20+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,ag soc_tmp,success
f71df53b681ab8d30000000000000013,2025-05-07T09:58:27+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,ag soc_volume,success
f71df53b681ab8d30000000000000013,2025-05-07T09:59:52+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,cd /tmp/,success
f71df53b681ab8d30000000000000013,2025-05-07T09:59:53+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,ll,success
af02d2dc681abfa20000000000000019,2025-05-07T10:04:26+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
af02d2dc681abfa20000000000000019,2025-05-07T10:04:56+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_state in(4,3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
af02d2dc681abfa20000000000000019,2025-05-07T10:04:56+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
af02d2dc681abfa20000000000000019,2025-05-07T10:05:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,group by f_currency;,success
af02d2dc681abfa20000000000000019,2025-05-07T10:05:15+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use proamsdwdb;,success
af02d2dc681abfa20000000000000019,2025-05-07T10:05:16+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w  where f_state in(4,3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01'  group by f_currency;,success
af02d2dc681abfa20000000000000019,2025-05-07T10:20:53+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
af02d2dc681abfa20000000000000019,2025-05-07T10:20:55+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
d9d4cd53681d9afe000000000000003a,2025-05-09T14:04:53+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,nslookup apifox.docsl.com,success
324f9060681dd8bd000000000000003a,2025-05-09T18:28:13+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '.';cd '.';cd '.';pwd;ls -la,success
8e8fbf04681dd8bd000000000000003a,2025-05-09T18:28:13+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,while true; do sleep 1;head -v -n 8 /proc/meminfo; head -v -n 2 /proc/stat /proc/version /proc/uptime /proc/loadavg /proc/sys/fs/file-nr /proc/sys/kernel/hostname; tail -v -n 32 /proc/net/dev;echo '==> /proc/df <==';df -l;echo '==> /proc/who <==';who;echo '==> /proc/end <==';echo '##Moba##'; done,success
d114703f68216f3d0000000000000019,2025-05-12T11:47:17+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
d114703f68216f3d0000000000000019,2025-05-12T11:47:56+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use proamsdwdb;,success
d114703f68216f3d0000000000000019,2025-05-12T11:48:14+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
d114703f68216f3d0000000000000019,2025-05-12T11:48:14+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_state in(4,3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
d114703f68216f3d0000000000000019,2025-05-12T11:48:15+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,group by f_currency;,success
30a8518a6821c9a3000000000000003a,2025-05-12T18:12:59+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,nslookup kibana.yorkapp.com,success
427d7bfb6821ce37000000000000003a,2025-05-12T18:32:23+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '.';cd '.';cd '.';pwd;ls -la,success
43e428976821ce37000000000000003a,2025-05-12T18:32:23+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,while true; do sleep 1;head -v -n 8 /proc/meminfo; head -v -n 2 /proc/stat /proc/version /proc/uptime /proc/loadavg /proc/sys/fs/file-nr /proc/sys/kernel/hostname; tail -v -n 32 /proc/net/dev;echo '==> /proc/df <==';df -l;echo '==> /proc/who <==';who;echo '==> /proc/end <==';echo '##Moba##'; done,success
d8d28e8e6822ce38000000000000003a,2025-05-13T12:44:55+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,curl-v https://wiki.docsl.com,success
d8d28e8e6822ce38000000000000003a,2025-05-13T12:45:28+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,crul -v https://wiki.docsl.com,success
d8d28e8e6822ce38000000000000003a,2025-05-13T12:45:49+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,apt install curl -y,success
d8d28e8e6822ce38000000000000003a,2025-05-13T12:46:00+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,curl -v https://wiki.docsl.com,success
d8d28e8e6822ce38000000000000003a,2025-05-13T12:46:42+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,curl -v https://wiki.docsl.com,success
d8d28e8e6822ce38000000000000003a,2025-05-13T12:47:05+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,curl -v https://bitbucket.docsl.com,success
c1558c796822f8dd000000000000003a,2025-05-13T15:46:48+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,nslookup paz.int.refinitiv.com,success
89abc1ab6823134f0000000000000013,2025-05-13T17:39:38+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,screen -DR f ,success
25f1a03a6823186c0000000000000013,2025-05-13T18:01:24+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,screen -Dr f,success
1beb51496823982a000000000000003a,2025-05-14T03:08:26+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,  -H "Authorization: Bearer 53GB5S378H9SQV373PGC_ASGFSA" \,success
1beb51496823982a000000000000003a,2025-05-14T03:08:26+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,  -H "Accept: application/json; charset=utf-8",success
1beb51496823982a000000000000003a,2025-05-14T03:10:37+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,  -H "Authorization: Bearer EUFJQ1H5Y3SYJV1QM114_ASGFSA",success
2514bcbd68239956000000000000003a,2025-05-14T03:11:18+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '.';cd '.';cd '.';pwd;ls -la,success
2b83802c68239956000000000000003a,2025-05-14T03:11:18+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,while true; do sleep 1;head -v -n 8 /proc/meminfo; head -v -n 2 /proc/stat /proc/version /proc/uptime /proc/loadavg /proc/sys/fs/file-nr /proc/sys/kernel/hostname; tail -v -n 32 /proc/net/dev;echo '==> /proc/df <==';df -l;echo '==> /proc/who <==';who;echo '==> /proc/end <==';echo '##Moba##'; done,success
55ed184a682402b30000000000000019,2025-05-14T10:40:58+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
55ed184a682402b30000000000000019,2025-05-14T10:41:20+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use proamsdwdb;,success
55ed184a682402b30000000000000019,2025-05-14T10:41:29+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
55ed184a682402b30000000000000019,2025-05-14T10:41:29+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_state in(4,3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
55ed184a682402b30000000000000019,2025-05-14T10:41:30+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,group by f_currency;,success
55ed184a682402b30000000000000019,2025-05-14T10:48:08+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date ,success
55ed184a682402b30000000000000019,2025-05-14T10:48:08+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from t_withdraw_virtual  ,success
55ed184a682402b30000000000000019,2025-05-14T10:48:12+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_state=8 and f_currency = 'btc';,success
55ed184a682402b30000000000000019,2025-05-14T10:48:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date  from t_withdraw_virtual   where f_state in(4,3,6,8) and f_currency = 'btc';,success
55ed184a682402b30000000000000019,2025-05-14T11:16:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
55ed184a682402b30000000000000019,2025-05-14T11:16:08+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
5865411768244335000000000000003a,2025-05-14T15:27:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,-H "Content-Type: application/json" \,success
5865411768244335000000000000003a,2025-05-14T15:27:37+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,  "type": "message",,success
5865411768244335000000000000003a,2025-05-14T15:27:37+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "$schema": "http://adaptivecards.io/schemas/adaptive-card.json",,success
5865411768244335000000000000003a,2025-05-14T15:27:37+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,  "attachments": [,success
5865411768244335000000000000003a,2025-05-14T15:27:37+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,    {,success
5865411768244335000000000000003a,2025-05-14T15:27:37+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,      "contentType": "application/vnd.microsoft.card.adaptive",,success
5865411768244335000000000000003a,2025-05-14T15:27:37+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,      "content": {,success
5865411768244335000000000000003a,2025-05-14T15:27:37+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "type": "AdaptiveCard",,success
5865411768244335000000000000003a,2025-05-14T15:27:37+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "body": [,success
5865411768244335000000000000003a,2025-05-14T15:27:37+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,          {,success
5865411768244335000000000000003a,2025-05-14T15:27:37+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            "type": "TextBlock",,success
5865411768244335000000000000003a,2025-05-14T15:27:37+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            "text": "这里是你的消息内容",success
5865411768244335000000000000003a,2025-05-14T15:27:37+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,          },success
5865411768244335000000000000003a,2025-05-14T15:27:37+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,-d '{,success
5865411768244335000000000000003a,2025-05-14T15:27:37+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        ],,success
5865411768244335000000000000003a,2025-05-14T15:27:37+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "version": "1.0",success
5865411768244335000000000000003a,2025-05-14T15:27:37+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,      },success
5865411768244335000000000000003a,2025-05-14T15:27:37+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,    },success
5865411768244335000000000000003a,2025-05-14T15:27:37+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,  ],success
5865411768244335000000000000003a,2025-05-14T15:27:38+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,}',success
5865411768244335000000000000003a,2025-05-14T15:34:09+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,-H "Content-Type: application/json" \,success
5865411768244335000000000000003a,2025-05-14T15:34:09+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,-d '{,success
5865411768244335000000000000003a,2025-05-14T15:34:09+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,  "type": "message",,success
5865411768244335000000000000003a,2025-05-14T15:34:09+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,  "attachments": [,success
5865411768244335000000000000003a,2025-05-14T15:34:09+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,    {,success
5865411768244335000000000000003a,2025-05-14T15:34:09+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,      "contentType": "application/vnd.microsoft.card.adaptive",,success
5865411768244335000000000000003a,2025-05-14T15:34:09+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,      "content": {,success
5865411768244335000000000000003a,2025-05-14T15:34:09+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "type": "AdaptiveCard",,success
5865411768244335000000000000003a,2025-05-14T15:34:09+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "body": [,success
5865411768244335000000000000003a,2025-05-14T15:34:09+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,          {,success
5865411768244335000000000000003a,2025-05-14T15:34:09+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            "type": "TextBlock",,success
5865411768244335000000000000003a,2025-05-14T15:34:09+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            "text": "这里是你的消息内容",success
5865411768244335000000000000003a,2025-05-14T15:34:09+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,          },success
5865411768244335000000000000003a,2025-05-14T15:34:09+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        ],,success
5865411768244335000000000000003a,2025-05-14T15:34:09+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "$schema": "http://adaptivecards.io/schemas/adaptive-card.json",,success
5865411768244335000000000000003a,2025-05-14T15:34:09+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "version": "1.0",success
5865411768244335000000000000003a,2025-05-14T15:34:09+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,      },success
5865411768244335000000000000003a,2025-05-14T15:34:09+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,}',success
5865411768244335000000000000003a,2025-05-14T15:34:09+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,  ],success
5865411768244335000000000000003a,2025-05-14T15:34:09+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,    },success
5865411768244335000000000000003a,2025-05-14T15:56:21+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,-H "Content-Type: application/json" \,success
5865411768244335000000000000003a,2025-05-14T15:56:21+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,-d '{,success
5865411768244335000000000000003a,2025-05-14T15:56:21+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,  "type": "message",,success
5865411768244335000000000000003a,2025-05-14T15:56:21+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,  "attachments": [,success
5865411768244335000000000000003a,2025-05-14T15:56:21+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,    {,success
5865411768244335000000000000003a,2025-05-14T15:56:21+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,      "contentType": "application/vnd.microsoft.card.adaptive",,success
5865411768244335000000000000003a,2025-05-14T15:56:21+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            "type": "Action.OpenUrl",,success
5865411768244335000000000000003a,2025-05-14T15:56:21+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "type": "AdaptiveCard",,success
5865411768244335000000000000003a,2025-05-14T15:56:21+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "body": [,success
5865411768244335000000000000003a,2025-05-14T15:56:21+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,          {,success
5865411768244335000000000000003a,2025-05-14T15:56:21+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            "type": "TextBlock",,success
5865411768244335000000000000003a,2025-05-14T15:56:21+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            "text": "这里是你的消息内容",success
5865411768244335000000000000003a,2025-05-14T15:56:21+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,          },success
5865411768244335000000000000003a,2025-05-14T15:56:21+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        ],,success
5865411768244335000000000000003a,2025-05-14T15:56:21+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,    "actions": [,success
5865411768244335000000000000003a,2025-05-14T15:56:21+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        {,success
5865411768244335000000000000003a,2025-05-14T15:56:21+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,      "content": {,success
5865411768244335000000000000003a,2025-05-14T15:56:22+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            "title": "点击此处打开谷歌",,success
5865411768244335000000000000003a,2025-05-14T15:56:22+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            "url": "https://www.google.com",success
5865411768244335000000000000003a,2025-05-14T15:56:22+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        },success
5865411768244335000000000000003a,2025-05-14T15:56:22+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,    ],success
5865411768244335000000000000003a,2025-05-14T15:56:22+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "$schema": "http://adaptivecards.io/schemas/adaptive-card.json",,success
5865411768244335000000000000003a,2025-05-14T15:56:22+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "version": "1.0",success
5865411768244335000000000000003a,2025-05-14T15:56:22+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,      },success
5865411768244335000000000000003a,2025-05-14T15:56:22+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,    },success
5865411768244335000000000000003a,2025-05-14T15:56:22+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,  ],success
5865411768244335000000000000003a,2025-05-14T15:56:22+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,}',success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            {,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,-d '{,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,  "type": "message",,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,  "attachments": [,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,    {,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,      "contentType": "application/vnd.microsoft.card.adaptive",,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,      "content": {,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "$schema": "https://adaptivecards.io/schemas/adaptive-card.json",,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "type": "AdaptiveCard",,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "version": "1.5",,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "body": [,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            {,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "type": "TextBlock",,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "text": "editor.js",,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "style": "heading",success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            },,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,-H "Content-Type: application/json" \,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "type": "TextBlock",,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "text": "Lines 61 - 76",success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            },,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            {,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "type": "CodeBlock",,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "codeSnippet": "/**\n* <AUTHOR> Smith <<EMAIL>>\n*/\npackage l2f.gameserver.model;\n\npublic abstract strictfp class L2Char extends L2Object {\n  public static final Short ERROR = 0x0001;\n\n  public void moveTo(int x, int y, int z) {\n    _ai = null;\n    log(\"Shouldn't be called\");\n    if (1 > 5) { // what!?\n      return;\n    }\n  }\n}",,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "language": "java",,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "startLineNumber": 61,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            },success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        ],,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "actions": [,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            {,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "type": "Action.OpenUrl",,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "title": "View in Azure Repos",,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "url": "https://azure.microsoft.com/en-us/products/devops/repos/",success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            },,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            {,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "type": "Action.OpenUrl",,success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,}',success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "url": "https://vscode.dev/",success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            },success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        ],success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,    },success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,    },success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,  ],success
5865411768244335000000000000003a,2025-05-14T15:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "title": "Edit in vscode.dev",,success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,}',success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,-d '{,success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,  "type": "message",,success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,  "attachments": [,success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,    {,success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,      "contentType": "application/vnd.microsoft.card.adaptive",,success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,      "content": {,success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "$schema": "https://adaptivecards.io/schemas/adaptive-card.json",,success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "type": "AdaptiveCard",,success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "version": "1.5",,success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "body": [,success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            {,success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "type": "TextBlock",,success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "text": "editor.js",,success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,-H "Content-Type: application/json" \,success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            },,success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            {,success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "type": "TextBlock",,success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "text": "Lines 61 - 76",success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            },success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        ],,success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "actions": [,success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            {,success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "type": "Action.OpenUrl",,success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "title": "View in Azure Repos",,success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "url": "https://azure.microsoft.com/en-us/products/devops/repos/",success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            },,success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            {,success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "type": "Action.OpenUrl",,success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "title": "Edit in vscode.dev",,success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "url": "https://vscode.dev/",success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            },success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        ],success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,    },success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,    },success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,  ],success
5865411768244335000000000000003a,2025-05-14T16:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "style": "heading",success
5865411768244335000000000000003a,2025-05-14T16:02:22+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,-H "Content-Type: application/json" \,success
5865411768244335000000000000003a,2025-05-14T16:02:22+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,-d '{,success
5865411768244335000000000000003a,2025-05-14T16:02:22+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,  "type": "message",,success
5865411768244335000000000000003a,2025-05-14T16:02:22+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,  "attachments": [,success
5865411768244335000000000000003a,2025-05-14T16:02:22+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,    {,success
5865411768244335000000000000003a,2025-05-14T16:02:22+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,      "contentType": "application/vnd.microsoft.card.adaptive",,success
5865411768244335000000000000003a,2025-05-14T16:02:22+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,      "content": {,success
5865411768244335000000000000003a,2025-05-14T16:02:22+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "$schema": "https://adaptivecards.io/schemas/adaptive-card.json",,success
5865411768244335000000000000003a,2025-05-14T16:02:22+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "type": "AdaptiveCard",,success
5865411768244335000000000000003a,2025-05-14T16:02:23+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "text": "editor.js",success
5865411768244335000000000000003a,2025-05-14T16:02:23+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "body": [,success
5865411768244335000000000000003a,2025-05-14T16:02:23+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            {,success
5865411768244335000000000000003a,2025-05-14T16:02:23+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "type": "TextBlock",,success
5865411768244335000000000000003a,2025-05-14T16:02:23+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "version": "1.5",,success
5865411768244335000000000000003a,2025-05-14T16:02:23+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            },success
5865411768244335000000000000003a,2025-05-14T16:02:23+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        ],,success
5865411768244335000000000000003a,2025-05-14T16:02:23+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "actions": [,success
5865411768244335000000000000003a,2025-05-14T16:02:23+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            {,success
5865411768244335000000000000003a,2025-05-14T16:02:23+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,}',success
5865411768244335000000000000003a,2025-05-14T16:02:23+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "title": "View in Azure Repos",,success
5865411768244335000000000000003a,2025-05-14T16:02:23+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "url": "https://azure.microsoft.com/en-us/products/devops/repos/",success
5865411768244335000000000000003a,2025-05-14T16:02:23+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            },success
5865411768244335000000000000003a,2025-05-14T16:02:23+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        ],success
5865411768244335000000000000003a,2025-05-14T16:02:23+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,    },success
5865411768244335000000000000003a,2025-05-14T16:02:23+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,    },success
5865411768244335000000000000003a,2025-05-14T16:02:23+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,  ],success
5865411768244335000000000000003a,2025-05-14T16:02:23+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "type": "Action.OpenUrl",,success
5865411768244335000000000000003a,2025-05-14T16:34:11+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "actions": [,success
5865411768244335000000000000003a,2025-05-14T16:34:11+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,-d '{,success
5865411768244335000000000000003a,2025-05-14T16:34:11+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,  "type": "message",,success
5865411768244335000000000000003a,2025-05-14T16:34:11+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,  "attachments": [,success
5865411768244335000000000000003a,2025-05-14T16:34:11+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,    {,success
5865411768244335000000000000003a,2025-05-14T16:34:11+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,      "contentType": "application/vnd.microsoft.card.adaptive",,success
5865411768244335000000000000003a,2025-05-14T16:34:11+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,      "content": {,success
5865411768244335000000000000003a,2025-05-14T16:34:11+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "$schema": "https://adaptivecards.io/schemas/adaptive-card.json",,success
5865411768244335000000000000003a,2025-05-14T16:34:11+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "type": "AdaptiveCard",,success
5865411768244335000000000000003a,2025-05-14T16:34:11+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "version": "1.5",,success
5865411768244335000000000000003a,2025-05-14T16:34:11+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "body": [,success
5865411768244335000000000000003a,2025-05-14T16:34:11+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            {,success
5865411768244335000000000000003a,2025-05-14T16:34:11+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "type": "TextBlock",,success
5865411768244335000000000000003a,2025-05-14T16:34:11+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "text": "请按时填写周报。",success
5865411768244335000000000000003a,2025-05-14T16:34:11+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            },success
5865411768244335000000000000003a,2025-05-14T16:34:11+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        ],,success
5865411768244335000000000000003a,2025-05-14T16:34:11+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,-H "Content-Type: application/json" \,success
5865411768244335000000000000003a,2025-05-14T16:34:12+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,  ],success
5865411768244335000000000000003a,2025-05-14T16:34:12+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "type": "Action.OpenUrl",,success
5865411768244335000000000000003a,2025-05-14T16:34:12+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            {,success
5865411768244335000000000000003a,2025-05-14T16:34:12+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "url": "https://docsl.sharepoint.com/:x:/r/sites/IT/_layouts/15/Doc.aspx?sourcedoc=%7B48B9E907-4D64-44C8-BA9B-32733994C518%7D&file=IT%E7%BB%84%E5%91%A8%E6%8A%A5-2025.xlsx&action=default&mobileredirect=true",success
5865411768244335000000000000003a,2025-05-14T16:34:12+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            },success
5865411768244335000000000000003a,2025-05-14T16:34:12+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        ],success
5865411768244335000000000000003a,2025-05-14T16:34:12+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,                "title": "点击打开周报",,success
5865411768244335000000000000003a,2025-05-14T16:34:12+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,    },success
5865411768244335000000000000003a,2025-05-14T16:34:12+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,    },success
5865411768244335000000000000003a,2025-05-14T16:34:13+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,}',success
5865411768244335000000000000003a,2025-05-14T16:35:05+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,pwd,success
5865411768244335000000000000003a,2025-05-14T16:35:08+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ls,success
5865411768244335000000000000003a,2025-05-14T16:35:48+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,mkdir script,success
5865411768244335000000000000003a,2025-05-14T16:35:51+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cd script/,success
5865411768244335000000000000003a,2025-05-14T16:36:18+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,touch send_teams_message.sh,success
5865411768244335000000000000003a,2025-05-14T16:36:22+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,pwd,success
5865411768244335000000000000003a,2025-05-14T16:36:45+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,vi send_teams_message.sh ,success
5865411768244335000000000000003a,2025-05-14T16:37:03+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,chmod +x send_teams_message.sh ,success
5865411768244335000000000000003a,2025-05-14T16:37:08+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,./send_teams_message.sh ,success
5865411768244335000000000000003a,2025-05-14T16:38:37+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,crontab -l,success
5865411768244335000000000000003a,2025-05-14T16:38:41+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,crontab -e,success
5865411768244335000000000000003a,2025-05-14T16:38:48+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,2,success
5865411768244335000000000000003a,2025-05-14T16:39:33+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,crontab -l,success
5865411768244335000000000000003a,2025-05-14T16:40:33+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,vi send_teams_message.sh ,success
5865411768244335000000000000003a,2025-05-14T16:42:09+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,/bin/bash /root/script/send_teams_message.sh,success
846c1932682468fd0000000000000013,2025-05-14T17:57:26+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,screen -DR f ,success
5d50e81b68247cbc000000000000003a,2025-05-14T19:21:32+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,while true; do sleep 1;head -v -n 8 /proc/meminfo; head -v -n 2 /proc/stat /proc/version /proc/uptime /proc/loadavg /proc/sys/fs/file-nr /proc/sys/kernel/hostname; tail -v -n 32 /proc/net/dev;echo '==> /proc/df <==';df -l;echo '==> /proc/who <==';who;echo '==> /proc/end <==';echo '##Moba##'; done,success
80e3061768247cbc000000000000003a,2025-05-14T19:21:32+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '.';cd '.';cd '.';pwd;ls -la,success
a72af1cc6824b30d000000000000003a,2025-05-14T23:13:17+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '.';cd '.';cd '.';pwd;ls -la,success
9cc48fd66824b30d000000000000003a,2025-05-15T00:03:58+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,sudo iotop,success
ec82347368254011000000000000003a,2025-05-15T09:15:07+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,sudo iotop,success
ec82347368254011000000000000003a,2025-05-15T09:17:33+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,sudo iotop,success
3080e32468255b64000000000000003a,2025-05-15T11:11:32+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '.';cd '.';cd '.';pwd;ls -la,success
053b81ef68255b64000000000000003a,2025-05-15T11:11:32+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,while true; do sleep 1;head -v -n 8 /proc/meminfo; head -v -n 2 /proc/stat /proc/version /proc/uptime /proc/loadavg /proc/sys/fs/file-nr /proc/sys/kernel/hostname; tail -v -n 32 /proc/net/dev;echo '==> /proc/df <==';df -l;echo '==> /proc/who <==';who;echo '==> /proc/end <==';echo '##Moba##'; done,success
efd60efa68255d6c000000000000003a,2025-05-15T11:20:12+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,while true; do sleep 1;head -v -n 8 /proc/meminfo; head -v -n 2 /proc/stat /proc/version /proc/uptime /proc/loadavg /proc/sys/fs/file-nr /proc/sys/kernel/hostname; tail -v -n 32 /proc/net/dev;echo '==> /proc/df <==';df -l;echo '==> /proc/who <==';who;echo '==> /proc/end <==';echo '##Moba##'; done,success
636d1c126825c4fe000000000000003a,2025-05-15T18:42:06+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,while true; do sleep 1;head -v -n 8 /proc/meminfo; head -v -n 2 /proc/stat /proc/version /proc/uptime /proc/loadavg /proc/sys/fs/file-nr /proc/sys/kernel/hostname; tail -v -n 32 /proc/net/dev;echo '==> /proc/df <==';df -l;echo '==> /proc/who <==';who;echo '==> /proc/end <==';echo '##Moba##'; done,success
ef3d038668269a2c0000000000000019,2025-05-16T09:51:48+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
ef3d038668269a2c0000000000000019,2025-05-16T09:52:03+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use proamsdwdb;,success
ef3d038668269a2c0000000000000019,2025-05-16T09:52:17+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
ef3d038668269a2c0000000000000019,2025-05-16T09:52:17+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_state in(4,3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
ef3d038668269a2c0000000000000019,2025-05-16T09:52:17+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,group by f_currency;,success
ef3d038668269a2c0000000000000019,2025-05-16T10:06:26+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date ,success
ef3d038668269a2c0000000000000019,2025-05-16T10:06:26+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from t_withdraw_virtual  ,success
ef3d038668269a2c0000000000000019,2025-05-16T10:06:33+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_state=8 and f_currency = 'usdt';,success
ef3d038668269a2c0000000000000019,2025-05-16T10:06:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date  from t_withdraw_virtual   where f_state in(3,4,6,8) and f_currency = 'usdt';,success
ef3d038668269a2c0000000000000019,2025-05-16T10:07:48+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit;,success
ef3d038668269a2c0000000000000019,2025-05-16T10:07:49+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
fa3e0403682a905a0000000000000019,2025-05-19T09:58:58+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
fa3e0403682a905a0000000000000019,2025-05-19T09:59:18+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use proamsdwdb;,success
fa3e0403682a905a0000000000000019,2025-05-19T09:59:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
fa3e0403682a905a0000000000000019,2025-05-19T09:59:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_state in(4,3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
fa3e0403682a905a0000000000000019,2025-05-19T09:59:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,group by f_currency;,success
fa3e0403682a905a0000000000000019,2025-05-19T10:22:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use proamsdwdb;,success
fa3e0403682a905a0000000000000019,2025-05-19T10:22:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
fa3e0403682a905a0000000000000019,2025-05-19T10:22:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
fa3e0403682a905a0000000000000019,2025-05-19T10:22:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
fa3e0403682a905a0000000000000019,2025-05-19T10:22:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
fa3e0403682a905a0000000000000019,2025-05-19T10:22:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
fa3e0403682a905a0000000000000019,2025-05-19T10:22:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
fa3e0403682a905a0000000000000019,2025-05-19T10:22:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
fa3e0403682a905a0000000000000019,2025-05-19T10:22:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
fa3e0403682a905a0000000000000019,2025-05-19T10:22:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
fa3e0403682a905a0000000000000019,2025-05-19T10:22:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
fa3e0403682a905a0000000000000019,2025-05-19T10:22:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
fa3e0403682a905a0000000000000019,2025-05-19T10:22:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-02',success
fa3e0403682a905a0000000000000019,2025-05-19T10:22:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where amount >= 100;,success
fa3e0403682a905a0000000000000019,2025-05-19T10:27:01+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-02'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 1000;,success
fa3e0403682a905a0000000000000019,2025-05-19T10:27:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-02'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 10000;,success
fa3e0403682a905a0000000000000019,2025-05-19T10:28:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
fa3e0403682a905a0000000000000019,2025-05-19T10:28:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
fa3e0403682a905a0000000000000019,2025-05-19T10:28:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
fa3e0403682a905a0000000000000019,2025-05-19T10:28:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
fa3e0403682a905a0000000000000019,2025-05-19T10:28:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
fa3e0403682a905a0000000000000019,2025-05-19T10:28:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
fa3e0403682a905a0000000000000019,2025-05-19T10:28:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
fa3e0403682a905a0000000000000019,2025-05-19T10:28:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
fa3e0403682a905a0000000000000019,2025-05-19T10:28:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-09',success
fa3e0403682a905a0000000000000019,2025-05-19T10:28:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
fa3e0403682a905a0000000000000019,2025-05-19T10:28:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
fa3e0403682a905a0000000000000019,2025-05-19T10:28:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
fa3e0403682a905a0000000000000019,2025-05-19T10:28:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where amount >= 100;,success
fa3e0403682a905a0000000000000019,2025-05-19T10:29:25+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-09'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 1000;,success
fa3e0403682a905a0000000000000019,2025-05-19T10:30:04+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-09'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 10000;,success
fa3e0403682a905a0000000000000019,2025-05-19T10:30:52+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
fa3e0403682a905a0000000000000019,2025-05-19T10:30:52+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
fa3e0403682a905a0000000000000019,2025-05-19T10:30:52+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
fa3e0403682a905a0000000000000019,2025-05-19T10:30:52+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
fa3e0403682a905a0000000000000019,2025-05-19T10:30:52+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
fa3e0403682a905a0000000000000019,2025-05-19T10:30:52+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
fa3e0403682a905a0000000000000019,2025-05-19T10:30:52+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
fa3e0403682a905a0000000000000019,2025-05-19T10:30:52+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
fa3e0403682a905a0000000000000019,2025-05-19T10:30:52+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-16',success
fa3e0403682a905a0000000000000019,2025-05-19T10:30:52+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
fa3e0403682a905a0000000000000019,2025-05-19T10:30:52+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
fa3e0403682a905a0000000000000019,2025-05-19T10:30:52+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
fa3e0403682a905a0000000000000019,2025-05-19T10:30:53+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where amount >= 100;,success
fa3e0403682a905a0000000000000019,2025-05-19T10:31:32+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-16'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 1000;,success
fa3e0403682a905a0000000000000019,2025-05-19T10:32:08+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-16'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 10000;,success
fa3e0403682a905a0000000000000019,2025-05-19T10:32:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
fa3e0403682a905a0000000000000019,2025-05-19T10:32:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
fa3e0403682a905a0000000000000019,2025-05-19T10:32:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
fa3e0403682a905a0000000000000019,2025-05-19T10:32:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
fa3e0403682a905a0000000000000019,2025-05-19T10:32:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
fa3e0403682a905a0000000000000019,2025-05-19T10:32:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
fa3e0403682a905a0000000000000019,2025-05-19T10:32:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
fa3e0403682a905a0000000000000019,2025-05-19T10:32:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) > '2025-04-18' ,success
fa3e0403682a905a0000000000000019,2025-05-19T10:32:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-04-25',success
fa3e0403682a905a0000000000000019,2025-05-19T10:32:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
fa3e0403682a905a0000000000000019,2025-05-19T10:32:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
fa3e0403682a905a0000000000000019,2025-05-19T10:32:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
fa3e0403682a905a0000000000000019,2025-05-19T10:32:52+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where amount >= 100;,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) > '2025-04-25' ,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-02',success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amountwhere amount >= 100;,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:34+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:34+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:34+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:34+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-09',success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:34+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:34+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:34+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:34+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) > '2025-05-02' ,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:34+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:34+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:34+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:34+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:35+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where amount >= 100;,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:53+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:53+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:53+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:53+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:53+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:53+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:53+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:53+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) > '2025-05-09' ,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:53+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-16',success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:53+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:53+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:53+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
fa3e0403682a905a0000000000000019,2025-05-19T10:33:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where amount >= 100;,success
fa3e0403682a905a0000000000000019,2025-05-19T10:52:08+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
fa3e0403682a905a0000000000000019,2025-05-19T10:52:10+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
d804bb5f682aaa69000000000000003a,2025-05-19T11:50:07+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ifconfig,success
d804bb5f682aaa69000000000000003a,2025-05-19T11:50:17+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,iftop,success
8e388c9b682c12e4000000000000001e,2025-05-20T13:29:19+08:00,cmd.Command,liulonglong,*************,yigeyun-vpn-node-sz-01,************,scp /home/<USER>/data/L1_proxy/conf/l4apps.config chris@10.172.104.134:/Users/<USER>/Desktop/,success
8e388c9b682c12e4000000000000001e,2025-05-20T13:29:59+08:00,cmd.Command,liulonglong,*************,yigeyun-vpn-node-sz-01,************,ls -l  /home/<USER>/data/L1_proxy/conf/l4apps.config,success
8e388c9b682c12e4000000000000001e,2025-05-20T13:30:07+08:00,cmd.Command,liulonglong,*************,yigeyun-vpn-node-sz-01,************,scp /home/<USER>/data/L1_proxy/conf/l4apps.config chris@10.172.104.134:/Users/<USER>/Desktop/,success
e2c9c14a682c1e98000000000000001e,2025-05-20T14:18:00+08:00,cmd.Command,liulonglong,**************,yigeyun-vpn-node-sz-01,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; echo $$;ps -ef,success
51da8097682c1e98000000000000001e,2025-05-20T14:18:00+08:00,cmd.Command,liulonglong,**************,yigeyun-vpn-node-sz-01,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 15055; ls -lad /proc/15055/cwd,success
b2b5d9e7682c1e99000000000000001e,2025-05-20T14:18:01+08:00,cmd.Command,liulonglong,**************,yigeyun-vpn-node-sz-01,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '.';cd '/';cd '/';pwd;ls -la,success
cc56b43b682c1e92000000000000001e,2025-05-20T14:18:46+08:00,cmd.Command,liulonglong,**************,yigeyun-vpn-node-sz-01,************,cd /home/<USER>/data/L1_proxy/conf/,success
c366c708682c1ec7000000000000001e,2025-05-20T14:18:47+08:00,cmd.Command,liulonglong,**************,yigeyun-vpn-node-sz-01,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 15055; ls -lad /proc/15055/cwd,success
99335271682c1ec8000000000000001e,2025-05-20T14:18:48+08:00,cmd.Command,liulonglong,**************,yigeyun-vpn-node-sz-01,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/';cd '/home/<USER>/data/L1_proxy/conf';cd '/home/<USER>/data/L1_proxy/conf';pwd;ls -la,success
743501f0682c1f09000000000000001e,2025-05-20T14:19:53+08:00,cmd.Command,liulonglong,**************,yigeyun-vpn-node-sz-01,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/home/<USER>/data/L1_proxy/conf/';cd '/home/<USER>/data/L1_proxy/conf/';cd '/home/<USER>/data/L1_proxy/conf/';pwd;ls -la,success
cfab3a0a682c1f0b000000000000001e,2025-05-20T14:19:55+08:00,file.Download,liulonglong,**************,yigeyun-vpn-node-sz-01,************,l4apps.config,success
be03c986682c1f0c000000000000001e,2025-05-20T14:19:56+08:00,cmd.Command,liulonglong,**************,yigeyun-vpn-node-sz-01,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/home/<USER>/data/L1_proxy/conf/';cd '/home/<USER>/data/L1_proxy/conf/';cd '/home/<USER>/data/L1_proxy/conf/';pwd;ls -la,success
651eb700682c1f20000000000000001e,2025-05-20T14:20:16+08:00,cmd.Command,liulonglong,**************,yigeyun-vpn-node-sz-01,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/home/<USER>/data/L1_proxy/conf/';cd '/home/<USER>/data/L1_proxy/conf/';cd '/home/<USER>/data/L1_proxy/conf/';pwd;ls -la,success
1bf4e6c9682c1f21000000000000001e,2025-05-20T14:20:17+08:00,cmd.Command,liulonglong,**************,yigeyun-vpn-node-sz-01,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/home/<USER>/data/L1_proxy/conf/';cd '/home/<USER>/data/L1_proxy/conf/';cd '/home/<USER>/data/L1_proxy/conf/';pwd;ls -la,success
93f5dab3682c1f21000000000000001e,2025-05-20T14:20:17+08:00,file.Download,liulonglong,**************,yigeyun-vpn-node-sz-01,************,ia_dlp_cfg.config,success
fa98f73c682c1fe6000000000000001e,2025-05-20T14:23:34+08:00,cmd.Command,liulonglong,**************,yigeyun-vpn-node-sz-01,************,while true; do sleep 1;head -v -n 8 /proc/meminfo; head -v -n 2 /proc/stat /proc/version /proc/uptime /proc/loadavg /proc/sys/fs/file-nr /proc/sys/kernel/hostname; tail -v -n 32 /proc/net/dev;echo '==> /proc/df <==';df -l;echo '==> /proc/who <==';who;echo '==> /proc/end <==';echo '##Moba##'; done,success
ecd683af682c37420000000000000046,2025-05-20T16:04:16+08:00,cmd.Command,fwsys,**************,oa-E10-db,************,exit,success
275c2fd4682c37840000000000000046,2025-05-20T16:04:29+08:00,cmd.Command,fwsys,**************,oa-E10-db,************,cd /data/monitor/,success
275c2fd4682c37840000000000000046,2025-05-20T16:04:31+08:00,cmd.Command,fwsys,**************,oa-E10-db,************,ll,success
ac02b274682d29c10000000000000019,2025-05-21T09:18:00+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
ac02b274682d29c10000000000000019,2025-05-21T09:18:26+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use proamsdwdb;,success
ac02b274682d29c10000000000000019,2025-05-21T09:18:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
ac02b274682d29c10000000000000019,2025-05-21T09:18:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_state in(4,3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
ac02b274682d29c10000000000000019,2025-05-21T09:18:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,group by f_currency;,success
ac02b274682d29c10000000000000019,2025-05-21T09:23:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_state in (3,6,8),success
ac02b274682d29c10000000000000019,2025-05-21T09:23:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from t_withdraw_virtual  ,success
ac02b274682d29c10000000000000019,2025-05-21T09:23:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_d,success
ac02b274682d29c10000000000000019,2025-05-21T09:23:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********, and from_unixtime(f_updated_at/1000) > '2022-11-01',success
ac02b274682d29c10000000000000019,2025-05-21T09:23:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********, and f_currency in('btc','eth') ,success
ac02b274682d29c10000000000000019,2025-05-21T09:23:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,order by f_currency;,success
ac02b274682d29c10000000000000019,2025-05-21T09:24:18+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date  from t_withdraw_virtual   where f_state in (3,4,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01'  and f_currency in('btc','eth')  order by f_currency;,success
ac02b274682d29c10000000000000019,2025-05-21T09:53:47+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
ac02b274682d29c10000000000000019,2025-05-21T09:53:49+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
faf27ea8682e8ffb000000000000003a,2025-05-22T10:46:29+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,你是lookup,success
faf27ea8682e8ffb000000000000003a,2025-05-22T10:46:33+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,nslookup quantbase.yorkapp.com,success
889a6bdd682fd5210000000000000019,2025-05-23T09:53:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
889a6bdd682fd5210000000000000019,2025-05-23T09:54:00+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use proamsdwdb;,success
889a6bdd682fd5210000000000000019,2025-05-23T09:54:11+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
889a6bdd682fd5210000000000000019,2025-05-23T09:54:11+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_state in(4,3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
889a6bdd682fd5210000000000000019,2025-05-23T09:54:12+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,group by f_currency;,success
889a6bdd682fd5210000000000000019,2025-05-23T10:02:50+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
889a6bdd682fd5210000000000000019,2025-05-23T10:02:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:33:25+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:33:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use proamsdwdb;,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:33:52+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:33:52+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_state in(4,3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
0a60a6a96833d2e90000000000000019,2025-05-26T10:33:53+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,group by f_currency;,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:40:09+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date ,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:40:09+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from t_withdraw_virtual  ,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:40:09+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_state in (4,3,6,8),success
0a60a6a96833d2e90000000000000019,2025-05-26T10:40:09+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********, and from_unixtime(f_updated_at/1000) > '2022-11-01',success
0a60a6a96833d2e90000000000000019,2025-05-26T10:40:09+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********, and f_currency in('btc','eth') ,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:40:11+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,order by f_currency;,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:55:46+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use hbg_trade;,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:12+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:12+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:12+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:12+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:12+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:12+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:12+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:12+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:12+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-23,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:12+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:12+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_uwhere amount >= 100; ,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:33+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,';,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-23',success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where amount >= 100;,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-23',success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:55+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amountwhere amount >= 100; ,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:56:59+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use proamsdwdb;,success
0a60a6a96833d2e90000000000000019,2025-05-26T10:57:01+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-23'     group by f_user_id ) user_amount where amount >= 100;,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:19:00+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-23'     group by f_user_id ) user_amount where amount >= 100;,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:21:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-23'     group by f_user_id ) user_amount where amount >= 1000;,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:24:04+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-23'     group by f_user_id ) user_amount where amount >= 10000;,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:26:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where amount >= 100;,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:26:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:26:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:26:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:26:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:26:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:26:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:26:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) > '2025-05-16' ,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:26:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-23',success
0a60a6a96833d2e90000000000000019,2025-05-26T11:26:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:26:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:26:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:26:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:29:52+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:29:52+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:29:52+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:29:52+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:29:52+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:29:52+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-23',success
0a60a6a96833d2e90000000000000019,2025-05-26T11:30:03+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id;,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:31:12+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2025-05-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-23'     group by f_user_id;,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:31:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2025-05-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-23' and amount> 100000     group by f_user_id;,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:32:02+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2025-05-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-23' and sum(dw.f_amount * cy.usdt_rate)> 100000     group by f_user_id;,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:32:14+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2025-05-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-23'     group by f_user_id order by amount desc;;,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:42:04+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use proamsdwdb;,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:42:23+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use proamsdwdb;,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:42:36+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date ,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:42:36+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from t_withdraw_virtual  ,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:42:36+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_state in (4,3,6,8),success
0a60a6a96833d2e90000000000000019,2025-05-26T11:42:36+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********, and from_unixtime(f_updated_at/1000) > '2022-11-01',success
0a60a6a96833d2e90000000000000019,2025-05-26T11:42:36+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********, and f_currency in('btc','eth') ,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:42:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,order by f_currency;,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:43:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use proamsdwdb;,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:43:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:43:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:43:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:43:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:43:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:43:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:43:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:43:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:43:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-23',success
0a60a6a96833d2e90000000000000019,2025-05-26T11:43:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:43:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:43:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where amount >= 100;,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:47:57+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-23'     group by f_user_id ) user_amount where amount >= 100;,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:48:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:48:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:48:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:48:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:48:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:48:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:48:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:49:05+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2025-05-16‘ and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-23',success
0a60a6a96833d2e90000000000000019,2025-05-26T11:49:05+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:49:05+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:49:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where amount >= 100;,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:49:26+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,';,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:49:32+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:49:32+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:49:32+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:49:32+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:49:32+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:49:32+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:49:32+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:49:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2025-05-16'  and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-23',success
0a60a6a96833d2e90000000000000019,2025-05-26T11:49:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:49:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:49:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where amount >= 100;,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:51:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:51:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:51:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:51:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:51:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:51:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:51:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:51:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:51:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-16',success
0a60a6a96833d2e90000000000000019,2025-05-26T11:51:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:51:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:51:14+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amounwhere amount >= 100;,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:58:02+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:58:02+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:58:02+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:58:02+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:58:02+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:58:02+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:58:02+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:58:02+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:58:02+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-01-31',success
0a60a6a96833d2e90000000000000019,2025-05-26T11:58:02+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:58:02+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:58:03+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amountwhere amount >= 100;,success
0a60a6a96833d2e90000000000000019,2025-05-26T11:59:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-10-04'     group by f_user_id ) user_amount where amount >= 100;,success
0a60a6a96833d2e90000000000000019,2025-05-26T12:09:35+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
0a60a6a96833d2e90000000000000019,2025-05-26T12:09:35+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select sum(dw.f_amount * cy.usdt_rate) as amount ,success
0a60a6a96833d2e90000000000000019,2025-05-26T12:09:35+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
0a60a6a96833d2e90000000000000019,2025-05-26T12:09:35+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
0a60a6a96833d2e90000000000000019,2025-05-26T12:09:35+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_currency='new';,success
0a60a6a96833d2e90000000000000019,2025-05-26T13:51:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
0a60a6a96833d2e90000000000000019,2025-05-26T13:51:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
0a60a6a96833d2e90000000000000019,2025-05-26T13:51:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
0a60a6a96833d2e90000000000000019,2025-05-26T13:51:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
0a60a6a96833d2e90000000000000019,2025-05-26T13:51:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
0a60a6a96833d2e90000000000000019,2025-05-26T13:51:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
0a60a6a96833d2e90000000000000019,2025-05-26T13:51:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
0a60a6a96833d2e90000000000000019,2025-05-26T13:51:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
0a60a6a96833d2e90000000000000019,2025-05-26T13:51:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-23',success
0a60a6a96833d2e90000000000000019,2025-05-26T13:51:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
0a60a6a96833d2e90000000000000019,2025-05-26T13:51:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
0a60a6a96833d2e90000000000000019,2025-05-26T13:51:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
0a60a6a96833d2e90000000000000019,2025-05-26T13:51:52+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where amount >= 100;,success
0a60a6a96833d2e90000000000000019,2025-05-26T13:52:33+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-23'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 1000;,success
0a60a6a96833d2e90000000000000019,2025-05-26T13:53:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-05-23'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 10000;,success
0a60a6a96833d2e90000000000000019,2025-05-26T14:03:11+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use hbg_trade; ,success
0a60a6a96833d2e90000000000000019,2025-05-26T14:03:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from t_account_manage_fee_snapshot,success
0a60a6a96833d2e90000000000000019,2025-05-26T14:03:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_day like '202505%'  and f_state = 1 ,success
0a60a6a96833d2e90000000000000019,2025-05-26T14:03:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_currency,sum(f_amount),success
0a60a6a96833d2e90000000000000019,2025-05-26T14:03:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,group by f_currency order by f_currency;,success
0a60a6a96833d2e90000000000000019,2025-05-26T14:09:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_currency,sum(f_amount),success
0a60a6a96833d2e90000000000000019,2025-05-26T14:09:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from t_account_manage_fee_snapshot,success
0a60a6a96833d2e90000000000000019,2025-05-26T14:09:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_day like '202505%'  and f_state = 1 ,success
0a60a6a96833d2e90000000000000019,2025-05-26T14:09:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,and f_currency in ('btc', 'eth', 'tfuel', 'usdt', 'doge', 'shib', 'xrp', 'trx', 'ltc', 'ada', 'fil', 'elf', 'eos', 'ong', 'etc', 'cvnt', 'btt', 'bsv', 'link', 'dot', 'nft', 'theta', 'mx', 'uni'),success
0a60a6a96833d2e90000000000000019,2025-05-26T14:09:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,group by f_currency order by f_currency;,success
0a60a6a96833d2e90000000000000019,2025-05-26T14:16:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date ,success
0a60a6a96833d2e90000000000000019,2025-05-26T14:16:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from t_withdraw_virtual  ,success
0a60a6a96833d2e90000000000000019,2025-05-26T14:16:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_state in (4,3,6,8),success
0a60a6a96833d2e90000000000000019,2025-05-26T14:16:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********, and from_unixtime(f_updated_at/1000) > '2022-11-01',success
0a60a6a96833d2e90000000000000019,2025-05-26T14:16:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********, and f_currency in('btc','eth') ,success
0a60a6a96833d2e90000000000000019,2025-05-26T14:16:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,order by f_currency;,success
0a60a6a96833d2e90000000000000019,2025-05-26T14:16:50+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use proamsdwdb;,success
0a60a6a96833d2e90000000000000019,2025-05-26T14:16:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date  from t_withdraw_virtual   where f_state in (4,3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01'  and f_currency in('btc','eth')  order by f_currency;,success
0a60a6a96833d2e90000000000000019,2025-05-26T14:17:31+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date  from t_withdraw_virtual   where from_unixtime(f_updated_at/1000) > '2025-05-16'  and f_currency in('btc','eth')  order by f_currency;,success
0a60a6a96833d2e90000000000000019,2025-05-26T14:17:50+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date  from t_withdraw_virtual   where from_unixtime(f_updated_at/1000) > '2025-05-16'  and f_currency in('btc','eth') and f_amount>1  order by f_currency;,success
0a60a6a96833d2e90000000000000019,2025-05-26T14:43:47+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
0a60a6a96833d2e90000000000000019,2025-05-26T14:43:48+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
9787e1ca68345489000000000000003a,2025-05-26T19:46:17+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,while true; do sleep 1;head -v -n 8 /proc/meminfo; head -v -n 2 /proc/stat /proc/version /proc/uptime /proc/loadavg /proc/sys/fs/file-nr /proc/sys/kernel/hostname; tail -v -n 32 /proc/net/dev;echo '==> /proc/df <==';df -l;echo '==> /proc/who <==';who;echo '==> /proc/end <==';echo '##Moba##'; done,success
4a67a11868345489000000000000003a,2025-05-26T19:46:17+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '.';cd '.';cd '.';pwd;ls -la,success
30f1634c6833d47e000000000000003a,2025-05-26T19:46:17+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,exit,success
c48c8de168367757000000000000003a,2025-05-28T10:40:06+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,}',success
c48c8de168367757000000000000003a,2025-05-28T10:40:06+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,-d '{,success
c48c8de168367757000000000000003a,2025-05-28T10:40:06+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,  "type": "message",,success
c48c8de168367757000000000000003a,2025-05-28T10:40:06+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,  "attachments": [,success
c48c8de168367757000000000000003a,2025-05-28T10:40:06+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,    {,success
c48c8de168367757000000000000003a,2025-05-28T10:40:06+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,      "contentType": "application/vnd.microsoft.card.adaptive",,success
c48c8de168367757000000000000003a,2025-05-28T10:40:06+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,      "content": {,success
c48c8de168367757000000000000003a,2025-05-28T10:40:06+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "type": "AdaptiveCard",,success
c48c8de168367757000000000000003a,2025-05-28T10:40:06+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "body": [,success
c48c8de168367757000000000000003a,2025-05-28T10:40:06+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,          {,success
c48c8de168367757000000000000003a,2025-05-28T10:40:06+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            "type": "TextBlock",,success
c48c8de168367757000000000000003a,2025-05-28T10:40:06+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            "text": "这里是你的消息内容",success
c48c8de168367757000000000000003a,2025-05-28T10:40:06+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,          },success
c48c8de168367757000000000000003a,2025-05-28T10:40:06+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        ],,success
c48c8de168367757000000000000003a,2025-05-28T10:40:06+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "$schema": "http://adaptivecards.io/schemas/adaptive-card.json",,success
c48c8de168367757000000000000003a,2025-05-28T10:40:06+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "version": "1.0",success
c48c8de168367757000000000000003a,2025-05-28T10:40:06+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,      },success
c48c8de168367757000000000000003a,2025-05-28T10:40:06+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,    },success
c48c8de168367757000000000000003a,2025-05-28T10:40:06+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,  ],success
c48c8de168367757000000000000003a,2025-05-28T10:40:06+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,-H "Content-Type: application/json" \,success
e19f6d60683683510000000000000019,2025-05-28T11:30:34+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
e19f6d60683683510000000000000019,2025-05-28T11:30:55+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use proamsdwdb;,success
e19f6d60683683510000000000000019,2025-05-28T11:31:05+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
e19f6d60683683510000000000000019,2025-05-28T11:31:05+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_state in(4,3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
e19f6d60683683510000000000000019,2025-05-28T11:31:05+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,group by f_currency;,success
e19f6d60683683510000000000000019,2025-05-28T11:39:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********, and f_currency in('btc','eth','usdt') ,success
e19f6d60683683510000000000000019,2025-05-28T11:39:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date ,success
e19f6d60683683510000000000000019,2025-05-28T11:39:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from t_withdraw_virtual  ,success
e19f6d60683683510000000000000019,2025-05-28T11:39:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********, and from_unixtime(f_updated_at/1000) > '2025-05-11',success
e19f6d60683683510000000000000019,2025-05-28T11:39:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_state in (4,3,6,8),success
e19f6d60683683510000000000000019,2025-05-28T11:40:08+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,order by f_currency;,success
e19f6d60683683510000000000000019,2025-05-28T11:45:59+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
e19f6d60683683510000000000000019,2025-05-28T11:46:00+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
177f6c2d68369641000000000000003a,2025-05-28T12:51:13+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,while true; do sleep 1;head -v -n 8 /proc/meminfo; head -v -n 2 /proc/stat /proc/version /proc/uptime /proc/loadavg /proc/sys/fs/file-nr /proc/sys/kernel/hostname; tail -v -n 32 /proc/net/dev;echo '==> /proc/df <==';df -l;echo '==> /proc/who <==';who;echo '==> /proc/end <==';echo '##Moba##'; done,success
9a408bb168369641000000000000003a,2025-05-28T12:51:13+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '.';cd '.';cd '.';pwd;ls -la,success
d12e88476838170d0000000000000013,2025-05-29T16:13:07+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,omz update,success
d12e88476838170d0000000000000013,2025-05-29T16:13:11+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,ls,success
d12e88476838170d0000000000000013,2025-05-29T16:13:17+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,screen -ls,success
d12e88476838170d0000000000000013,2025-05-29T16:13:23+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,screen -Dr f,success
86c838c568397f4a0000000000000019,2025-05-30T17:51:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
86c838c568397f4a0000000000000019,2025-05-30T17:51:27+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use proamsdwdb;,success
86c838c568397f4a0000000000000019,2025-05-30T17:51:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date ,success
86c838c568397f4a0000000000000019,2025-05-30T17:51:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from t_withdraw_virtual  ,success
86c838c568397f4a0000000000000019,2025-05-30T17:51:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_state in (4,3,6,8),success
86c838c568397f4a0000000000000019,2025-05-30T17:51:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********, and from_unixtime(f_updated_at/1000) > '2025-05-11',success
86c838c568397f4a0000000000000019,2025-05-30T17:51:55+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********, and f_currency in('btc','eth','usdt') ,success
86c838c568397f4a0000000000000019,2025-05-30T17:52:21+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,and f_user_id in(3175007, 11465600, 26534598, 4218266, 1612965, 11178423, 3033272, 20907918, 11956937, 14642850, 2900536, 11197033, 5067364, 4559932, 4516457, 1418755, 12538168, 789547, 9158653, 2083374, 4009091, 9079359, 8387145, 11910740, 14910168, 15691397, 22632588, 20048841, 6181215, 4126330, 4126349);,success
86c838c568397f4a0000000000000019,2025-05-30T17:53:23+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date  from t_withdraw_virtual   where from_unixtime(f_updated_at/1000) > '2025-05-11'  and f_currency in('btc','eth','usdt')  and f_user_id in(3175007, 11465600, 26534598, 4218266, 1612965, 11178423, 3033272, 20907918, 11956937, 14642850, 2900536, 11197033, 5067364, 4559932, 4516457, 1418755, 12538168, 789547, 9158653, 2083374, 4009091, 9079359, 8387145, 11910740, 14910168, 15691397, 22632588, 20048841, 6181215, 4126330, 4126349);,success
86c838c568397f4a0000000000000019,2025-05-30T17:54:29+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date  from t_withdraw_virtual   where from_unixtime(f_updated_at/1000) > '2025-05-11'  and f_currency in('btc','eth','usdt')  and f_user_id in(3175007, 11465600, 26534598, 4218266, 1612965, 11178423, 3033272, 20907918, 11956937, 14642850, 2900536, 11197033, 5067364, 4559932, 4516457, 1418755, 12538168, 789547, 9158653, 2083374, 4009091, 9079359, 8387145, 11910740, 14910168, 15691397, 22632588, 20048841, 6181215, 4126330, 4126349);,success
86c838c568397f4a0000000000000019,2025-05-30T17:55:10+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date  from t_withdraw_virtual   where from_unixtime(f_updated_at/1000) > '2025-05-11'  and f_user_id in(3175007, 11465600, 26534598, 4218266, 1612965, 11178423, 3033272, 20907918, 11956937, 14642850, 2900536, 11197033, 5067364, 4559932, 4516457, 1418755, 12538168, 789547, 9158653, 2083374, 4009091, 9079359, 8387145, 11910740, 14910168, 15691397, 22632588, 20048841, 6181215, 4126330, 4126349);,success
86c838c568397f4a0000000000000019,2025-05-30T18:01:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date  from t_withdraw_virtual   where from_unixtime(f_updated_at/1000) > '2025-05-11'  and f_user_id in(3175007, 11465600, 26534598, 4218266, 1612965, 11178423, 3033272, 20907918, 11956937, 14642850, 2900536, 11197033, 5067364, 4559932, 4516457, 1418755, 12538168, 789547, 9158653, 2083374, 4009091, 9079359, 8387145, 11910740, 14910168, 15691397, 22632588, 20048841, 6181215);,success
86c838c568397f4a0000000000000019,2025-05-30T18:02:33+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date  from t_withdraw_virtual   where f_state in (4,3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-10-01'  and f_currency in('btc','eth','usdt')  and f_user_id in(3175007, 11465600, 26534598, 4218266, 1612965, 11178423, 3033272, 20907918, 11956937, 14642850, 29005                                                                                                               , 119,success
86c838c568397f4a0000000000000019,2025-05-30T18:03:25+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date  from t_withdraw_virtual   where f_state =11  and from_unixtime(f_updated_at/1000) > '2022-10-01'  and f_currency in('btc','eth','usdt')  and f_user_id in(3175007, 11465600, 26534598, 4218266, 1612965, 11178423, 3033272, 20907918, 11956937, 14642850, 2900536, 11197033, 5067364, 4559932, 4516457, 1418755, 12538168, 789547, 9158653, 2083374, 4009091, 9079359, 8387145, 11910740, 14910168, 15691397, 22632588, 20048841, 6181215, 4126330, 4126349);,success
86c838c568397f4a0000000000000019,2025-05-30T18:03:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date  from t_withdraw_virtual   where f_state =11  and from_unixtime(f_updated_at/1000) > '2022-10-01'  and f_currency in('btc','eth','usdt')  and f_user_id in(3175007, 11465600, 26534598, 4218266, 1612965, 11178423, 3033272, 20907918, 11956937, 14642850, 2900536, 11197033, 5067364, 4559932, 4516457, 1418755, 12538168, 789547, 9158653, 2083374, 4009091, 9079359, 8387145, 11910740, 14910168, 15691397, 22632588, 20048841, 6181215, 4126330, 4126349) order by f_currency;,success
86c838c568397f4a0000000000000019,2025-05-30T18:28:48+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date  from t_withdraw_virtual   where f_state =11  and from_unixtime(f_updated_at/1000) > '2022-10-01'   and f_user_id in(3175007, 11465600, 26534598, 4218266, 1612965, 11178423, 3033272, 20907918, 11956937, 14642850, 2900536, 11197033, 5067364, 4559932, 4516457, 1418755, 12538168, 789547, 9158653, 2083374, 4009091, 9079359, 8387145, 11910740, 14910168, 15691397, 22632588, 20048841, 6181215, 4126330, 4126349) order by f_currency;,success
86c838c568397f4a0000000000000019,2025-05-30T18:49:50+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date  from t_withdraw_virtual   where f_state =11  and from_unixtime(f_updated_at/1000) > '2022-10-01'   and f_user_id in(4126349) order by f_currency;,success
86c838c568397f4a0000000000000019,2025-05-30T19:04:27+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
86c838c568397f4a0000000000000019,2025-05-30T19:04:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
