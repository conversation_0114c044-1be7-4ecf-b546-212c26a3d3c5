SessionID,EventTime,EventType,<PERSON>rna<PERSON>,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,Result
8ab1aaf265bae511000000000000000d,2024-02-01T08:25:58+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd /var/log/hue,success
8ab1aaf265bae511000000000000000d,2024-02-01T08:25:59+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
8ab1aaf265bae511000000000000000d,2024-02-01T08:27:01+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-bigdata-datanode-1c-02,172.28.3.8,tar zcvf /tmp/runcpserver.log.20230201-02.tar.gz runcpserver.log runcpserver.log.1 runcpserver.log.2 runcpserver.log.3,success
39dd833565bae561000000000000000d,2024-02-01T08:27:19+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-bigdata-datanode-1c-03,172.28.3.121,cd /var/log/hue,success
39dd833565bae561000000000000000d,2024-02-01T08:27:21+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-bigdata-datanode-1c-03,172.28.3.121,ll,success
39dd833565bae561000000000000000d,2024-02-01T08:27:52+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-bigdata-datanode-1c-03,172.28.3.121,tar zcvf /tmp/runcpserver.log.20230201-03.tar.gz runcpserver.log runcpserver.log.1 runcpserver.log.2 runcpserver.log.3,success
ec50384565bae58e000000000000000d,2024-02-01T08:28:01+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-bigdata-datanode-1c-05,172.28.3.93,cd /var/log/hue,success
ec50384565bae58e000000000000000d,2024-02-01T08:28:03+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-bigdata-datanode-1c-05,172.28.3.93,ll,success
ec50384565bae58e000000000000000d,2024-02-01T08:28:29+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-bigdata-datanode-1c-05,172.28.3.93,tar zcvf /tmp/runcpserver.log.20230201-05.tar.gz runcpserver.log runcpserver.log.1 runcpserver.log.2 runcpserver.log.3,success
70dcc13b65bae5b5000000000000000d,2024-02-01T08:28:40+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,cd /tmp/,success
70dcc13b65bae5b5000000000000000d,2024-02-01T08:28:41+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,ll,success
70dcc13b65bae5b5000000000000000d,2024-02-01T08:32:24+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,vim /etc/environment,success
70dcc13b65bae5b5000000000000000d,2024-02-01T08:32:44+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,:wq,success
963549f965bae6b3000000000000000d,2024-02-01T08:32:58+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,cd /tmp/,success
963549f965bae6b3000000000000000d,2024-02-01T08:32:59+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,ll,success
963549f965bae6b3000000000000000d,2024-02-01T08:34:39+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,vim ~/.bash_profile,success
963549f965bae6b3000000000000000d,2024-02-01T08:35:06+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,:wq,success
963549f965bae6b3000000000000000d,2024-02-01T08:35:15+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,source ~/.bash_profile,success
963549f965bae6b3000000000000000d,2024-02-01T08:35:29+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,scp 172.28.3.8:/tmp/runcpserver.log.20230201-02.tar.gz .,success
963549f965bae6b3000000000000000d,2024-02-01T08:35:43+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,scp 172.28.3.121:/tmp/runcpserver.log.20230201-03.tar.gz .,success
963549f965bae6b3000000000000000d,2024-02-01T08:35:50+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,scp 172.28.3.93:/tmp/runcpserver.log.20230201-05.tar.gz .,success
963549f965bae6b3000000000000000d,2024-02-01T08:35:55+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,vim /etc/environment,success
963549f965bae6b3000000000000000d,2024-02-01T08:36:00+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,:wq,success
963549f965bae6b3000000000000000d,2024-02-01T08:36:24+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,ossutil cp runcpserver.log.20230201-02.tar.gz  oss://miniglobal-devops/hue_log/,success
963549f965bae6b3000000000000000d,2024-02-01T08:36:28+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,ossutil cp runcpserver.log.20230201-03.tar.gz  oss://miniglobal-devops/hue_log/,success
963549f965bae6b3000000000000000d,2024-02-01T08:36:32+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,ossutil cp runcpserver.log.20230201-05.tar.gz  oss://miniglobal-devops/hue_log/,success
963549f965bae6b3000000000000000d,2024-02-01T08:37:46+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,ll,success
963549f965bae6b3000000000000000d,2024-02-01T08:37:50+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,rm -f runcpserver.log.20230201-0*,success
963549f965bae6b3000000000000000d,2024-02-01T08:37:51+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,ll,success
b857b8ce65bae7e5000000000000000d,2024-02-01T08:38:02+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-bigdata-datanode-1c-02,172.28.3.8,rm -f /tmp/runcpserver.log.20230201-02.tar.gz,success
21fdad4665bae7ee000000000000000d,2024-02-01T08:38:12+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-bigdata-datanode-1c-03,172.28.3.121,rm -f /tmp/runcpserver.log.20230201-03.tar.gz,success
7ba2ce5765bae7f8000000000000000d,2024-02-01T08:38:23+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-bigdata-datanode-1c-05,172.28.3.93,rm -f /tmp/runcpserver.log.20230201-05.tar.gz,success
97557c0a65bb058e000000000000000d,2024-02-01T10:45:09+08:00,cmd.Command,avenir_user01,223.104.40.164,miniglobal-devops,***********,ssh 172.28.2.38,success
97557c0a65bb058e000000000000000d,2024-02-01T10:45:18+08:00,cmd.Command,avenir_user01,223.104.40.164,miniglobal-devops,***********,cd /yonyou/,success
97557c0a65bb058e000000000000000d,2024-02-01T10:45:19+08:00,cmd.Command,avenir_user01,223.104.40.164,miniglobal-devops,***********,ll,success
97557c0a65bb058e000000000000000d,2024-02-01T10:45:23+08:00,cmd.Command,avenir_user01,223.104.40.164,miniglobal-devops,***********,cd nchomesc/,success
97557c0a65bb058e000000000000000d,2024-02-01T10:45:24+08:00,cmd.Command,avenir_user01,223.104.40.164,miniglobal-devops,***********,ll,success
97557c0a65bb058e000000000000000d,2024-02-01T10:48:18+08:00,cmd.Command,avenir_user01,223.104.40.164,miniglobal-devops,***********,./stop.sh,success
97557c0a65bb058e000000000000000d,2024-02-01T10:48:31+08:00,cmd.Command,avenir_user01,223.104.40.164,miniglobal-devops,***********,./startup.sh,success
97557c0a65bb058e000000000000000d,2024-02-01T10:49:37+08:00,cmd.Command,avenir_user01,223.104.40.164,miniglobal-devops,***********,cd ../searchserver/,success
97557c0a65bb058e000000000000000d,2024-02-01T10:49:42+08:00,cmd.Command,avenir_user01,223.104.40.164,miniglobal-devops,***********,./stop.sh,success
97557c0a65bb058e000000000000000d,2024-02-01T10:49:50+08:00,cmd.Command,avenir_user01,223.104.40.164,miniglobal-devops,***********,./startup.sh,success
b7501e4665bb8a810000000000000019,2024-02-01T20:11:58+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
b7501e4665bb8a810000000000000019,2024-02-01T20:12:32+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,use hbg_trade;,success
b7501e4665bb8a810000000000000019,2024-02-01T20:12:38+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,use proamsdwdb;,success
b7501e4665bb8a810000000000000019,2024-02-01T20:12:48+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
b7501e4665bb8a810000000000000019,2024-02-01T20:12:48+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,from,success
b7501e4665bb8a810000000000000019,2024-02-01T20:12:49+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
b7501e4665bb8a810000000000000019,2024-02-01T20:12:49+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
b7501e4665bb8a810000000000000019,2024-02-01T20:12:49+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,) user_amount,success
b7501e4665bb8a810000000000000019,2024-02-01T20:12:49+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,(,success
b7501e4665bb8a810000000000000019,2024-02-01T20:12:49+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,where amount >= 100;,success
b7501e4665bb8a810000000000000019,2024-02-01T20:12:49+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
b7501e4665bb8a810000000000000019,2024-02-01T20:12:49+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-01-25',success
b7501e4665bb8a810000000000000019,2024-02-01T20:12:49+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    and dw.f_state = 11,success
b7501e4665bb8a810000000000000019,2024-02-01T20:12:49+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    group by f_user_id,success
b7501e4665bb8a810000000000000019,2024-02-01T20:12:49+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
b7501e4665bb8a810000000000000019,2024-02-01T20:12:49+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
b7501e4665bb8a810000000000000019,2024-02-01T20:13:30+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,0,success
b7501e4665bb8a810000000000000019,2024-02-01T20:14:02+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-01-25'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 10000;,success
b7501e4665bb8a810000000000000019,2024-02-01T20:14:40+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
b7501e4665bb8a810000000000000019,2024-02-01T20:14:40+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,from,success
b7501e4665bb8a810000000000000019,2024-02-01T20:14:40+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,(,success
b7501e4665bb8a810000000000000019,2024-02-01T20:14:40+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
b7501e4665bb8a810000000000000019,2024-02-01T20:14:40+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
b7501e4665bb8a810000000000000019,2024-02-01T20:14:40+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
b7501e4665bb8a810000000000000019,2024-02-01T20:14:40+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
b7501e4665bb8a810000000000000019,2024-02-01T20:14:40+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) > '2024-01-18' ,success
b7501e4665bb8a810000000000000019,2024-02-01T20:14:40+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-01-25',success
b7501e4665bb8a810000000000000019,2024-02-01T20:14:41+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    and dw.f_state = 11,success
b7501e4665bb8a810000000000000019,2024-02-01T20:14:41+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    group by f_user_id,success
b7501e4665bb8a810000000000000019,2024-02-01T20:14:41+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,) user_amount,success
b7501e4665bb8a810000000000000019,2024-02-01T20:14:41+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,where amount >= 100;,success
b7501e4665bb8a810000000000000019,2024-02-01T20:15:04+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,) user_amount,success
b7501e4665bb8a810000000000000019,2024-02-01T20:15:04+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,from,success
b7501e4665bb8a810000000000000019,2024-02-01T20:15:04+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,(,success
b7501e4665bb8a810000000000000019,2024-02-01T20:15:04+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
b7501e4665bb8a810000000000000019,2024-02-01T20:15:04+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
b7501e4665bb8a810000000000000019,2024-02-01T20:15:04+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
b7501e4665bb8a810000000000000019,2024-02-01T20:15:04+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
b7501e4665bb8a810000000000000019,2024-02-01T20:15:04+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
b7501e4665bb8a810000000000000019,2024-02-01T20:15:04+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-02-01',success
b7501e4665bb8a810000000000000019,2024-02-01T20:15:04+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    and dw.f_state = 11,success
b7501e4665bb8a810000000000000019,2024-02-01T20:15:04+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    group by f_user_id,success
b7501e4665bb8a810000000000000019,2024-02-01T20:15:04+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
b7501e4665bb8a810000000000000019,2024-02-01T20:15:05+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,where amount >= 100;,success
b7501e4665bb8a810000000000000019,2024-02-01T20:15:46+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,0,success
b7501e4665bb8a810000000000000019,2024-02-01T20:16:17+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-02-01'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 10000;,success
b7501e4665bb8a810000000000000019,2024-02-01T20:16:54+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
b7501e4665bb8a810000000000000019,2024-02-01T20:16:54+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,from,success
b7501e4665bb8a810000000000000019,2024-02-01T20:16:54+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,(,success
b7501e4665bb8a810000000000000019,2024-02-01T20:16:54+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
b7501e4665bb8a810000000000000019,2024-02-01T20:16:54+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) > '2024-01-25' ,success
b7501e4665bb8a810000000000000019,2024-02-01T20:16:54+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
b7501e4665bb8a810000000000000019,2024-02-01T20:16:54+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
b7501e4665bb8a810000000000000019,2024-02-01T20:16:54+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
b7501e4665bb8a810000000000000019,2024-02-01T20:16:55+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    and dw.f_state = 11,success
b7501e4665bb8a810000000000000019,2024-02-01T20:16:55+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    group by f_user_id,success
b7501e4665bb8a810000000000000019,2024-02-01T20:16:55+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,) user_amount,success
b7501e4665bb8a810000000000000019,2024-02-01T20:16:55+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,where amount >= 100;,success
b7501e4665bb8a810000000000000019,2024-02-01T20:16:55+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-02-01',success
b7501e4665bb8a810000000000000019,2024-02-01T21:59:49+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,exit,success
b7501e4665bb8a810000000000000019,2024-02-01T21:59:51+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,exit,success
0ddb0f3865bc2baa0000000000000019,2024-02-02T07:39:30+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
0ddb0f3865bc2baa0000000000000019,2024-02-02T07:39:46+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,use proamsdwdb;,success
0ddb0f3865bc2baa0000000000000019,2024-02-02T07:39:53+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
0ddb0f3865bc2baa0000000000000019,2024-02-02T07:39:53+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,where f_state in(3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
0ddb0f3865bc2baa0000000000000019,2024-02-02T07:39:53+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,group by f_currency;,success
0ddb0f3865bc2baa0000000000000019,2024-02-02T07:59:11+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,exit,success
0ddb0f3865bc2baa0000000000000019,2024-02-02T07:59:13+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,exit,success
55f13fb265bc5468000000000000000d,2024-02-02T10:33:14+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
55f13fb265bc5468000000000000000d,2024-02-02T10:34:00+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,use hbgorderdb,success
55f13fb265bc5468000000000000000d,2024-02-02T10:34:04+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,show tables;,success
55f13fb265bc5468000000000000000d,2024-02-02T10:34:50+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,select a.*, from_unixtime(f_created_at/1000, '%Y-%m-%d %h:%i:%s') as create_time, from_unixtime(f_updated_at/1000, '%Y-%m-%d %h:%i:%s') as update_time from t_point_buy_back a limit 3;,success
55f13fb265bc5468000000000000000d,2024-02-02T10:41:14+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,select count(*) from t_point_buy_back;,success
55f13fb265bc5468000000000000000d,2024-02-02T10:41:44+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,select sum(f_total_price ) from t_point_buy_back;,success
55f13fb265bc5468000000000000000d,2024-02-02T10:42:12+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,select sum(f_total_price ) from t_point_buy_back where f_total_price < 3;,success
55f13fb265bc5468000000000000000d,2024-02-02T10:42:31+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,select count(*) from t_point_buy_back where f_total_price < 3;,success
55f13fb265bc5468000000000000000d,2024-02-02T10:45:41+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,select count(*) from t_point_buy_back where f_total_price < 3 and f_total_price > 0;,success
55f13fb265bc5468000000000000000d,2024-02-02T10:46:43+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,select * from t_point_buy_back where f_u_id= 7505009;,success
55f13fb265bc5468000000000000000d,2024-02-02T10:47:11+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,select * from t_point_buy_back where f_state = 1 limit 3;,success
55f13fb265bc5468000000000000000d,2024-02-02T10:47:23+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,select * from t_point_buy_back where f_state <> 0 limit 3;,success
55f13fb265bc5468000000000000000d,2024-02-02T10:47:37+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,show create table t_point_buy_back;,success
55f13fb265bc5468000000000000000d,2024-02-02T10:48:12+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,select sum(f_total_price ) from t_point_buy_back where f_state <> 0;,success
55f13fb265bc5468000000000000000d,2024-02-02T10:48:55+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,select sum(f_total_price ) from t_point_buy_back where f_state <> 8;,success
55f13fb265bc5468000000000000000d,2024-02-02T10:49:23+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,select sum(f_total_price ) from t_point_buy_back where f_state not in (3,7, 8);,success
55f13fb265bc5468000000000000000d,2024-02-02T10:52:13+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,select f_state, sum(f_total_price ) from t_point_buy_back group by f_state ;,success
55f13fb265bc5468000000000000000d,2024-02-02T10:55:15+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,show tables;,success
55f13fb265bc5468000000000000000d,2024-02-02T10:55:26+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,show create table t_point_buy_back_item;,success
55f13fb265bc5468000000000000000d,2024-02-02T10:56:51+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,select * from t_point_buy_back_item where f_user_id = 73490;,success
e541095065bc5dd4000000000000000d,2024-02-02T11:15:32+08:00,file.Upload,avenir_user01,120.244.194.94,MiniAD04,172.28.2.251,H79cs^VAYgr7c;$#JRbg.txt,success
e541095065bc5dd4000000000000000d,2024-02-02T11:20:41+08:00,file.Upload,avenir_user01,120.244.194.94,MiniAD04,172.28.2.251,ocr.txt,success
e541095065bc5dd4000000000000000d,2024-02-02T11:20:41+08:00,file.UploadSave,avenir_user01,120.244.194.94,MiniAD04,172.28.2.251,keyboard.txt,success
f8ea6f9665bc851f000000000000000d,2024-02-02T14:01:07+08:00,cmd.Command,avenir_user01,223.104.40.164,miniglobal-deploy,***********,cd /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-hbg-encn/,success
f8ea6f9665bc851f000000000000000d,2024-02-02T14:01:08+08:00,cmd.Command,avenir_user01,223.104.40.164,miniglobal-deploy,***********,ll,success
f8ea6f9665bc851f000000000000000d,2024-02-02T14:01:33+08:00,cmd.Command,avenir_user01,223.104.40.164,miniglobal-deploy,***********,make build TAG=PRD_T402022,success
f8ea6f9665bc851f000000000000000d,2024-02-02T14:04:07+08:00,cmd.Command,avenir_user01,223.104.40.164,miniglobal-deploy,***********,make build TAG=PRD_T402022,success
5047954065bd618d000000000000000d,2024-02-03T05:41:41+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,ssh 172.28.32.101,success
5047954065bd618d000000000000000d,2024-02-03T05:42:53+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,ping 172.28.32.101,success
5047954065bd618d000000000000000d,2024-02-03T05:43:44+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,ssh 172.28.32.101,success
5047954065bd618d000000000000000d,2024-02-03T05:43:51+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,yes,success
5047954065bd618d000000000000000d,2024-02-03T05:43:56+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,df -h,success
5047954065bd618d000000000000000d,2024-02-03T05:44:20+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,lsblk,success
cdf005b165bf14d9000000000000000d,2024-02-04T12:38:58+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-deploy,***********,cd /hbdata/soft/miniglobal-apps/apps/miniglobal/miniglobal-finance-daily-report/,success
cdf005b165bf14d9000000000000000d,2024-02-04T12:38:59+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-deploy,***********,ll,success
cdf005b165bf14d9000000000000000d,2024-02-04T12:39:25+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-deploy,***********,make build TAG=PRD_T20240204,success
cb58364f65c03f660000000000000019,2024-02-05T09:52:43+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
cb58364f65c03f660000000000000019,2024-02-05T09:53:00+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,use proamsdwdb;,success
cb58364f65c03f660000000000000019,2024-02-05T09:53:07+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
cb58364f65c03f660000000000000019,2024-02-05T09:53:07+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where f_state in(3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
cb58364f65c03f660000000000000019,2024-02-05T09:53:07+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,group by f_currency;,success
cb58364f65c03f660000000000000019,2024-02-05T10:12:32+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,exit,success
cb58364f65c03f660000000000000019,2024-02-05T10:12:34+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,exit,success
c996167065c083d2000000000000000d,2024-02-05T14:44:39+08:00,cmd.Command,avenir_user01,223.104.40.200,db-mysql-miniglobal-1c-4,172.28.5.15,df -h,success
c996167065c083d2000000000000000d,2024-02-05T14:44:52+08:00,cmd.Command,avenir_user01,223.104.40.200,db-mysql-miniglobal-1c-4,172.28.5.15,cd /hbdata/mysql3614/,success
c996167065c083d2000000000000000d,2024-02-05T14:44:53+08:00,cmd.Command,avenir_user01,223.104.40.200,db-mysql-miniglobal-1c-4,172.28.5.15,ll,success
c996167065c083d2000000000000000d,2024-02-05T14:45:02+08:00,cmd.Command,avenir_user01,223.104.40.200,db-mysql-miniglobal-1c-4,172.28.5.15,cd binarylogs/,success
c996167065c083d2000000000000000d,2024-02-05T14:45:03+08:00,cmd.Command,avenir_user01,223.104.40.200,db-mysql-miniglobal-1c-4,172.28.5.15,ls,success
c996167065c083d2000000000000000d,2024-02-05T14:45:09+08:00,cmd.Command,avenir_user01,223.104.40.200,db-mysql-miniglobal-1c-4,172.28.5.15,cd ../logs/,success
c996167065c083d2000000000000000d,2024-02-05T14:45:12+08:00,cmd.Command,avenir_user01,223.104.40.200,db-mysql-miniglobal-1c-4,172.28.5.15,ll -h,success
c996167065c083d2000000000000000d,2024-02-05T14:45:37+08:00,cmd.Command,avenir_user01,223.104.40.200,db-mysql-miniglobal-1c-4,172.28.5.15,cd ../data/,success
c996167065c083d2000000000000000d,2024-02-05T14:45:38+08:00,cmd.Command,avenir_user01,223.104.40.200,db-mysql-miniglobal-1c-4,172.28.5.15,ll,success
a527919365c0942a000000000000000d,2024-02-05T15:56:46+08:00,cmd.Command,avenir_user01,223.104.40.200,miniglobal-deploy,***********,cd /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-hbg-encn/,success
a527919365c0942a000000000000000d,2024-02-05T15:56:47+08:00,cmd.Command,avenir_user01,223.104.40.200,miniglobal-deploy,***********,ll,success
a527919365c0942a000000000000000d,2024-02-05T15:57:21+08:00,cmd.Command,avenir_user01,223.104.40.200,miniglobal-deploy,***********,make build TAG=PRD_T402051,success
0a2bd50465c1a5b1000000000000000d,2024-02-06T11:25:14+08:00,cmd.Command,avenir_user01,223.104.41.191,miniglobal-deploy,***********,cd /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-hbg-encn/,success
0a2bd50465c1a5b1000000000000000d,2024-02-06T11:25:16+08:00,cmd.Command,avenir_user01,223.104.41.191,miniglobal-deploy,***********,ll,success
0a2bd50465c1a5b1000000000000000d,2024-02-06T11:25:43+08:00,cmd.Command,avenir_user01,223.104.41.191,miniglobal-deploy,***********,cd ..,success
0a2bd50465c1a5b1000000000000000d,2024-02-06T11:26:11+08:00,cmd.Command,avenir_user01,223.104.41.191,miniglobal-deploy,***********,cp fed-hbg-encn fed-avenir,success
0a2bd50465c1a5b1000000000000000d,2024-02-06T11:26:24+08:00,cmd.Command,avenir_user01,223.104.41.191,miniglobal-deploy,***********,cp -r fed-hbg-encn fed-avenir,success
0a2bd50465c1a5b1000000000000000d,2024-02-06T11:26:29+08:00,cmd.Command,avenir_user01,223.104.41.191,miniglobal-deploy,***********,cd fed-avenir/,success
0a2bd50465c1a5b1000000000000000d,2024-02-06T11:26:30+08:00,cmd.Command,avenir_user01,223.104.41.191,miniglobal-deploy,***********,ll,success
0a2bd50465c1a5b1000000000000000d,2024-02-06T11:26:45+08:00,cmd.Command,avenir_user01,223.104.41.191,miniglobal-deploy,***********,vim Dockerfile,success
0a2bd50465c1a5b1000000000000000d,2024-02-06T11:27:37+08:00,cmd.Command,avenir_user01,223.104.41.191,miniglobal-deploy,***********,:set nu,success
0a2bd50465c1a5b1000000000000000d,2024-02-06T11:28:12+08:00,cmd.Command,avenir_user01,223.104.41.191,miniglobal-deploy,***********,:wq,success
0a2bd50465c1a5b1000000000000000d,2024-02-06T11:28:20+08:00,cmd.Command,avenir_user01,223.104.41.191,miniglobal-deploy,***********,vim Makefile,success
0a2bd50465c1a5b1000000000000000d,2024-02-06T11:28:48+08:00,cmd.Command,avenir_user01,223.104.41.191,miniglobal-deploy,***********,:wq,success
0a2bd50465c1a5b1000000000000000d,2024-02-06T11:29:06+08:00,cmd.Command,avenir_user01,223.104.41.191,miniglobal-deploy,***********,vim build.sh,success
0a2bd50465c1a5b1000000000000000d,2024-02-06T11:31:51+08:00,cmd.Command,avenir_user01,223.104.41.191,miniglobal-deploy,***********,:w,success
b790b48365c2e7af0000000000000019,2024-02-07T10:15:18+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
b790b48365c2e7af0000000000000019,2024-02-07T10:15:48+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,use proamsdwdb;,success
b790b48365c2e7af0000000000000019,2024-02-07T10:15:57+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
b790b48365c2e7af0000000000000019,2024-02-07T10:15:57+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,where f_state in(3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
b790b48365c2e7af0000000000000019,2024-02-07T10:15:58+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,group by f_currency;,success
b790b48365c2e7af0000000000000019,2024-02-07T10:28:13+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,exit,success
b790b48365c2e7af0000000000000019,2024-02-07T10:28:15+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,exit,success
2f7995ba65c42d15000000000000000d,2024-02-08T09:23:37+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd /var/log/hue,success
2f7995ba65c42d15000000000000000d,2024-02-08T09:23:38+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
2f7995ba65c42d15000000000000000d,2024-02-08T09:24:20+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-bigdata-datanode-1c-02,172.28.3.8,tar zcvf /tmp/runcpserver.log.20230208-02.tar.gz runcpserver.log runcpserver.log.1,success
af667c9165c42d4d000000000000000d,2024-02-08T09:24:33+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-bigdata-datanode-1c-03,172.28.3.121,cd /var/log/hue,success
af667c9165c42d4d000000000000000d,2024-02-08T09:24:34+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-bigdata-datanode-1c-03,172.28.3.121,ll,success
af667c9165c42d4d000000000000000d,2024-02-08T09:24:50+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-bigdata-datanode-1c-03,172.28.3.121,tar zcvf /tmp/runcpserver.log.20230208-03.tar.gz runcpserver.log runcpserver.log.1 runcpserver.log.2,success
6a3d036a65c42d69000000000000000d,2024-02-08T09:25:02+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-bigdata-datanode-1c-05,172.28.3.93,cd /var/log/hue,success
6a3d036a65c42d69000000000000000d,2024-02-08T09:25:04+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-bigdata-datanode-1c-05,172.28.3.93,ll,success
6a3d036a65c42d69000000000000000d,2024-02-08T09:26:01+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-bigdata-datanode-1c-05,172.28.3.93,tar zcvf /tmp/runcpserver.log.20230208-05.tar.gz runcpserver.log runcpserver.log.1 runcpserver.log.2,success
c64e151865c42dbc000000000000000d,2024-02-08T09:26:22+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,cd /tmp,success
c64e151865c42dbc000000000000000d,2024-02-08T09:26:23+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,ll,success
c64e151865c42dbc000000000000000d,2024-02-08T09:26:56+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,scp 172.28.3.8:/tmp/runcpserver.log.20230208-02.tar.gz .,success
c64e151865c42dbc000000000000000d,2024-02-08T09:27:05+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,scp 172.28.3.121:/tmp/runcpserver.log.20230208-03.tar.gz .,success
c64e151865c42dbc000000000000000d,2024-02-08T09:27:11+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,scp 172.28.3.93:/tmp/runcpserver.log.20230208-05.tar.gz .,success
c64e151865c42dbc000000000000000d,2024-02-08T09:27:26+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,ossutil cp runcpserver.log.20230208-02.tar.gz oss://miniglobal-devops/hue_log/,success
c64e151865c42dbc000000000000000d,2024-02-08T09:27:31+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,ossutil cp runcpserver.log.20230208-03.tar.gz oss://miniglobal-devops/hue_log/,success
c64e151865c42dbc000000000000000d,2024-02-08T09:27:36+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,ossutil cp runcpserver.log.20230208-05.tar.gz oss://miniglobal-devops/hue_log/,success
c64e151865c42dbc000000000000000d,2024-02-08T09:33:02+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,ll,success
c64e151865c42dbc000000000000000d,2024-02-08T09:33:09+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,rm -f runcpserver.log.20230208-0*,success
35c64b8065c42f5c000000000000000d,2024-02-08T09:33:23+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-bigdata-datanode-1c-02,172.28.3.8,rm -f /tmp/runcpserver.log.20230208-02.tar.gz,success
1147039a65c42f67000000000000000d,2024-02-08T09:34:34+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-bigdata-datanode-1c-03,172.28.3.121,rm -f /tmp/runcpserver.log.20230208-03.tar.gz,success
4d9e17a265c42fb1000000000000000d,2024-02-08T09:34:49+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-bigdata-datanode-1c-05,172.28.3.93,rm -f /tmp/runcpserver.log.20230208-05.tar.gz,success
8bf7dd9565c43b6e0000000000000019,2024-02-08T10:24:52+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
8bf7dd9565c43b6e0000000000000019,2024-02-08T10:25:09+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,use proamsdwdb;,success
8bf7dd9565c43b6e0000000000000019,2024-02-08T10:25:17+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
8bf7dd9565c43b6e0000000000000019,2024-02-08T10:25:17+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,where f_state in(3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
8bf7dd9565c43b6e0000000000000019,2024-02-08T10:25:18+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,group by f_currency;,success
8bf7dd9565c43b6e0000000000000019,2024-02-08T10:37:53+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,it,success
8bf7dd9565c43b6e0000000000000019,2024-02-08T10:37:55+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,exit,success
bef14ac465ce225a000000000000000d,2024-02-15T22:42:04+08:00,file.Upload,avenir_user01,120.244.194.81,MiniAD04,172.28.2.251,gY5**B*AX4>udCTxd]Y#.txt,success
bef14ac465ce225a000000000000000d,2024-02-15T22:42:56+08:00,file.Upload,avenir_user01,120.244.194.81,MiniAD04,172.28.2.251,ocr.txt,success
bef14ac465ce225a000000000000000d,2024-02-15T22:42:56+08:00,file.UploadSave,avenir_user01,120.244.194.81,MiniAD04,172.28.2.251,keyboard.txt,success
f177fb0965ce24c30000000000000013,2024-02-15T22:51:41+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,102l,success
f177fb0965ce24c30000000000000013,2024-02-15T22:51:46+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][      0$ mycli  (1*$~)  2-$ docker  3$ ..f475b438974da  4!$ vim  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl       ][15/02 14:52],success
f177fb0965ce24c30000000000000013,2024-02-15T22:51:59+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,787,success
f177fb0965ce24c30000000000000013,2024-02-15T22:52:38+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,qABupda forBBAA,success
f177fb0965ce24c30000000000000013,2024-02-15T22:52:56+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,set f_status = 1 where id = 5787;,success
f177fb0965ce24c30000000000000013,2024-02-15T22:56:37+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,2,success
f177fb0965ce24c30000000000000013,2024-02-15T22:56:43+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][       0$ mycli  1$ ~  2-$ docker  3$ ..f475b438974da  (4*$vim)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl       ][15/02 14:57],success
f177fb0965ce24c30000000000000013,2024-02-15T22:56:44+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][  0$ mycli  1$ ~  2-$ docker  3$ ..f475b438974da  (4*$~/forensic_etl)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl  ][15/02 14:57,success
f177fb0965ce24c30000000000000013,2024-02-15T22:56:51+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][  0$ mycli  1$ ~  2$ docker  3-$ ..f475b438974da  (4*$~/forensic_etl)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl  ][15/02 14:57,success
f177fb0965ce24c30000000000000013,2024-02-15T22:56:52+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][  0$ mycli  1$ ~  2$ docker  3-$ ..f475b438974da  (4*$~/forensic_etl)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl  ][15/02 14:57,success
f177fb0965ce24c30000000000000013,2024-02-15T22:56:59+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][  0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  4-$ ~/forensic_etl  (5*$~/forensic_etl)  6$ ..forensic_task  7$ ~/forensic_etl  ][15/02 14:57,success
f177fb0965ce24c30000000000000013,2024-02-15T22:57:02+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][  0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  4-$ ~/forensic_etl  (5*$~/forensic_etl)  6$ ..forensic_task  7$ ~/forensic_etl  ][15/02 14:57,success
f177fb0965ce24c30000000000000013,2024-02-15T22:57:12+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][       0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  4-$ ~/forensic_etl  (5*$vim)  6$ ..forensic_task  7$ ~/forensic_etl       ][15/02 14:57],success
f177fb0965ce24c30000000000000013,2024-02-15T22:57:14+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][       0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  4$ ~/forensic_etl  5-$ vim  (6*$..forensic_task)  7$ ~/forensic_etl       ][15/02 14:57],success
f177fb0965ce24c30000000000000013,2024-02-15T22:57:16+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][       0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  4$ ~/forensic_etl  5-$ vim  (6*$..forensic_task)  7$ ~/forensic_etl       ][15/02 14:57],success
f177fb0965ce24c30000000000000013,2024-02-15T22:57:18+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][       0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  4$ ~/forensic_etl  5-$ vim  (6*$~/forensic_etl)  7$ ~/forensic_etl        ][15/02 14:57],success
f177fb0965ce24c30000000000000013,2024-02-15T22:57:32+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][           0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  (4*$~/forensic_etl)  5$ vim  6-$ python  7$ ~/forensic_etl            ][15/02 14:57],success
f177fb0965ce24c30000000000000013,2024-02-15T22:57:43+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][           0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  (4*$~/forensic_etl)  5$ vim  6-$ python  7$ ~/forensic_etl            ][15/02 14:57],success
f177fb0965ce24c30000000000000013,2024-02-15T22:57:44+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][           0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  (4*$~/forensic_etl)  5$ vim  6-$ python  7$ ~/forensic_etl            ][15/02 14:58],success
f177fb0965ce24c30000000000000013,2024-02-15T22:57:45+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][           0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  (4*$..ensic_etl/gen)  5$ vim  6-$ python  7$ ~/forensic_etl           ][15/02 14:58],success
f177fb0965ce24c30000000000000013,2024-02-15T22:57:47+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][           0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  (4*$..ensic_etl/gen)  5$ vim  6-$ python  7$ ~/forensic_etl           ][15/02 14:58],success
f177fb0965ce24c30000000000000013,2024-02-15T22:58:36+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][             0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  4$ vim  5$ vim  (6*$~/forensic_etl)  7-$ ~/forensic_etl             ][15/02 14:58],success
f177fb0965ce24c30000000000000013,2024-02-15T22:59:15+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,0$ mycli  1$ ~  2$ docker  3-$ ..f475b438974da  (4*$vim)  5$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl             ][15/02 14:59],success
f177fb0965ce24c30000000000000013,2024-02-15T22:59:17+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][             0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  4-$ vim  5$ vim  (6*$~/forensic_etl)  7$ ~/forensic_etl             ][15/02 14:59],success
f177fb0965ce24c30000000000000013,2024-02-15T22:59:51+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][             0$ mycli  1$ ~  2-$ docker  3$ ..f475b438974da  4$ vim  (5*$vim)  6$ ~/forensic_etl  7$ ~/forensic_etl             ][15/02 15:00],success
f177fb0965ce24c30000000000000013,2024-02-15T23:00:16+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][       0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  (4*$vim)  5-$ ~/forensic_etl  6$ ~/forensic_etl  7$ ~/forensic_etl        ][15/02 15:00],success
f177fb0965ce24c30000000000000013,2024-02-15T23:00:19+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][       0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  4-$ vim  5$ ~/forensic_etl  (6*$~/forensic_etl)  7$ ~/forensic_etl        ][15/02 15:00],success
f177fb0965ce24c30000000000000013,2024-02-15T23:00:20+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][       0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  4-$ vim  5$ ~/forensic_etl  (6*$~/forensic_etl)  7$ ~/forensic_etl        ][15/02 15:00],success
f177fb0965ce24c30000000000000013,2024-02-15T23:00:21+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][       0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  4-$ vim  5$ ~/forensic_etl  (6*$~/forensic_etl)  7$ ~/forensic_etl        ][15/02 15:00],success
f177fb0965ce24c30000000000000013,2024-02-15T23:00:46+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,AAAAAAAA,success
f177fb0965ce24c30000000000000013,2024-02-15T23:00:47+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,:,success
f177fb0965ce24c30000000000000013,2024-02-15T23:00:48+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,ko,success
f177fb0965ce24c30000000000000013,2024-02-15T23:01:13+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][       0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  (4*$vim)  5$ ~/forensic_etl  6-$ ~/forensic_etl  7$ ~/forensic_etl        ][15/02 15:01],success
f177fb0965ce24c30000000000000013,2024-02-15T23:01:20+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,:,success
f177fb0965ce24c30000000000000013,2024-02-15T23:01:28+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,()lii.fitilew,success
f177fb0965ce24c30000000000000013,2024-02-15T23:01:33+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,6BA,success
f177fb0965ce24c30000000000000013,2024-02-15T23:01:51+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,w,success
f177fb0965ce24c30000000000000013,2024-02-15T23:01:53+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,6A,success
f177fb0965ce24c30000000000000013,2024-02-15T23:02:16+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,jjjjjhhhVkjddduuVdkkkkkkkpxxjjjjVjjdw,success
f177fb0965ce24c30000000000000013,2024-02-15T23:02:17+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,6A,success
f177fb0965ce24c30000000000000013,2024-02-15T23:02:39+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][       0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  (4*$vim)  5$ ~/forensic_etl  6-$ ~/forensic_etl  7$ ~/forensic_etl        ][15/02 15:02],success
f177fb0965ce24c30000000000000013,2024-02-15T23:02:42+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  (4*$vim)  5$ ~/forensic_etl  6-$ ~/forensic_etl  7$ ~/forensic_etl        ][15/02 15:02],success
f177fb0965ce24c30000000000000013,2024-02-15T23:02:44+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][       0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  4-$ vim  5$ ~/forensic_etl  (6*$~/forensic_etl)  7$ ~/forensic_etl        ][15/02 15:03],success
f177fb0965ce24c30000000000000013,2024-02-15T23:03:14+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][       0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  (4*$vim)  5$ ~/forensic_etl  6-$ ~/forensic_etl  7$ ~/forensic_etl        ][15/02 15:03],success
f177fb0965ce24c30000000000000013,2024-02-15T23:03:16+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  (4*$vim)  5$ ~/forensic_etl  6-$ ~/forensic_etl  7$ ~/forensic_etl        ][15/02 15:03],success
f177fb0965ce24c30000000000000013,2024-02-15T23:03:19+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][       0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  4$ vim  5-$ ~/forensic_etl  (6*$~/forensic_etl)  7$ ~/forensic_etl        ][15/02 15:03],success
f177fb0965ce24c30000000000000013,2024-02-15T23:03:50+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][       0$ mycli  1$ ~  2$ docker  3-$ ..f475b438974da  (4*$vim)  5$ ~/forensic_etl  6$ ~/forensic_etl  7$ ~/forensic_etl        ][15/02 15:04],success
f177fb0965ce24c30000000000000013,2024-02-15T23:03:56+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,0$ mycli  1$ ~  2$ docker  3-$ ..f475b438974da  (4*$vim)  5$ ~/forensic_etl  6$ ~/forensic_etl  7$ ~/forensic_etl        ][15/02 15:04],success
f177fb0965ce24c30000000000000013,2024-02-15T23:03:57+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][       0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  4-$ vim  5$ ~/forensic_etl  (6*$~/forensic_etl)  7$ ~/forensic_etl        ][15/02 15:04],success
f177fb0965ce24c30000000000000013,2024-02-15T23:04:06+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  (4*$vim)  5$ ~/forensic_etl  6-$ ~/forensic_etl  7$ ~/forensic_etl        ][15/02 15:04],success
f177fb0965ce24c30000000000000013,2024-02-15T23:04:08+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][       0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  4-$ vim  5$ ~/forensic_etl  (6*$~/forensic_etl)  7$ ~/forensic_etl        ][15/02 15:04],success
f177fb0965ce24c30000000000000013,2024-02-15T23:04:28+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  (4*$vim)  5$ ~/forensic_etl  6-$ ~/forensic_etl  7$ ~/forensic_etl        ][15/02 15:04],success
f177fb0965ce24c30000000000000013,2024-02-15T23:04:30+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][       0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  4-$ vim  5$ ~/forensic_etl  (6*$~/forensic_etl)  7$ ~/forensic_etl        ][15/02 15:04],success
f177fb0965ce24c30000000000000013,2024-02-15T23:05:04+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  (4*$vim)  5$ ~/forensic_etl  6-$ ~/forensic_etl  7$ ~/forensic_etl        ][15/02 15:05],success
f177fb0965ce24c30000000000000013,2024-02-15T23:05:11+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,VVjjdki# kw,success
f177fb0965ce24c30000000000000013,2024-02-15T23:05:13+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,46B,success
f177fb0965ce24c30000000000000013,2024-02-15T23:05:16+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][       0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  4-$ vim  5$ ~/forensic_etl  (6*$~/forensic_etl)  7$ ~/forensic_etl        ][15/02 15:05],success
f177fb0965ce24c30000000000000013,2024-02-15T23:05:58+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,* from cs where f in ['43553802', '75935474', '198289476', '189507092', '113894796', '190617520', '46584879', '133044035', '169731984', '93308610', '4774677', '111424504', '116154030', '15708268', '62591986', '27809925', '6187299', '94512118', '174249313', '116337417', '46324687', '117728434', '26484247', '150460510', '121759218)aCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC(,success
f177fb0965ce24c30000000000000013,2024-02-15T23:06:00+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,;,success
f177fb0965ce24c30000000000000013,2024-02-15T23:06:37+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][       0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  (4*$vim)  5-$ ~/forensic_etl  6$ ~/forensic_etl  7$ ~/forensic_etl        ][15/02 15:06],success
f177fb0965ce24c30000000000000013,2024-02-15T23:06:40+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,listw,success
f177fb0965ce24c30000000000000013,2024-02-15T23:06:50+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,6BA,success
f177fb0965ce24c30000000000000013,2024-02-15T23:07:15+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,uidw,success
f177fb0965ce24c30000000000000013,2024-02-15T23:07:19+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,jhhhhhhhhi# w,success
f177fb0965ce24c30000000000000013,2024-02-15T23:07:21+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][       0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  4-$ vim  5$ ~/forensic_etl  (6*$~/forensic_etl)  7$ ~/forensic_etl        ][15/02 15:07],success
f177fb0965ce24c30000000000000013,2024-02-15T23:07:57+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,cBBBBB,success
f177fb0965ce24c30000000000000013,2024-02-15T23:08:02+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,BBBBAA,success
f177fb0965ce24c30000000000000013,2024-02-15T23:08:09+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,\G,success
f177fb0965ce24c30000000000000013,2024-02-15T23:09:41+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                    0-$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  4$ vim  (5*$~/forensic_etl)  6$ ~/forensic_etl  7$ ~/forensic_etl                                    ][15/02 15:09],success
f177fb0965ce24c30000000000000013,2024-02-15T23:09:51+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0-$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  4$ vim  (5*$vim)  6$ ~/forensic_etl  7$ ~/forensic_etl                                          ][15/02 15:10],success
f177fb0965ce24c30000000000000013,2024-02-15T23:10:09+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0-$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  4$ vim  (5*$vim)  6$ ~/forensic_etl  7$ ~/forensic_etl                                          ][15/02 15:10],success
f177fb0965ce24c30000000000000013,2024-02-15T23:10:22+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,5736,success
f177fb0965ce24c30000000000000013,2024-02-15T23:10:43+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0-$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  4$ vim  (5*$vim)  6$ ~/forensic_etl  7$ ~/forensic_etl                                          ][15/02 15:10],success
f177fb0965ce24c30000000000000013,2024-02-15T23:10:50+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  4$ vim  5-$ vim  (6*$~/forensic_etl)  7$ ~/forensic_etl                                          ][15/02 15:11],success
f177fb0965ce24c30000000000000013,2024-02-15T23:10:56+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  4$ vim  5-$ vim  (6*$~/forensic_etl)  7$ ~/forensic_etl                                          ][15/02 15:11],success
f177fb0965ce24c30000000000000013,2024-02-15T23:11:26+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,5jjjj7,success
f177fb0965ce24c30000000000000013,2024-02-15T23:11:27+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,ls,success
f177fb0965ce24c30000000000000013,2024-02-15T23:11:37+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                             0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  (4*$vim)  5$ vim  6$ python  7-$ ~/forensic_etl                                              ][15/02 15:11],success
f177fb0965ce24c30000000000000013,2024-02-15T23:11:37+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                             0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  (4*$vim)  5$ vim  6$ python  7-$ ~/forensic_etl                                              ][15/02 15:11],success
f177fb0965ce24c30000000000000013,2024-02-15T23:11:38+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                       0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  (4*$..ensic_etl/gen)  5$ vim  6$ python  7-$ ~/forensic_etl                                        ][15/02 15:11],success
f177fb0965ce24c30000000000000013,2024-02-15T23:11:43+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                       0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  (4*$..ensic_etl/gen)  5$ vim  6$ python  7-$ ~/forensic_etl                                        ][15/02 15:11],success
f177fb0965ce24c30000000000000013,2024-02-15T23:11:45+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                       0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  (4*$..ensic_etl/gen)  5$ vim  6$ python  7-$ ~/forensic_etl                                        ][15/02 15:12],success
f177fb0965ce24c30000000000000013,2024-02-15T23:11:45+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                        0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  (4*$~/forensic_etl)  5$ vim  6$ python  7-$ ~/forensic_etl                                        ][15/02 15:12],success
f177fb0965ce24c30000000000000013,2024-02-15T23:11:48+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                        0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  (4*$~/forensic_etl)  5$ vim  6$ python  7-$ ~/forensic_etl                                        ][15/02 15:12],success
f177fb0965ce24c30000000000000013,2024-02-15T23:12:16+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,4306,success
f177fb0965ce24c30000000000000013,2024-02-15T23:12:38+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************, = {"otc": ["order_id", "face_uid", "currency", "volume", "price", "official", "amount", "time", "uid", "order_side"], "reg": ["idcard", "bankcard", "alipay", "wechat", "balance", "user_address", "uid", "name", "phone", "email", "gmt-created", "country", "user_chain", "user_currency", "user_tag"], "dw": ["address", "tag", "order_id", "uid", "dw_side", "currency", "deal_time", "volume"], "trade": ["order_type", "price", "volume", "amount", "uid", "order_id", "created_time", "symbol", "order_side"], "history": ["uid", "pay_type", "pay_info"], "login": ["uid", "login_time", "login_terminal", "ip"], "balance": ["uid", "currency", "balance"], "dm_trade": ["uid", "instrument_id", "instrument_type", "product_id", "dm_order_type", "direction", "offset", "traded_type", "dm_price", "traded_volume", "traded_coin_volume", "traded_turnover", "traded_fee", "offset_profitloss", "created_at"], "swap_trade": ["uid", "instrument_id", "instrument_type", "product_id", "dm_order_type", "direction", "offset", "traded_type", "dm_price", "traded_volume", "traded_coin_volume", "traded_turnover", "traded_fee", "offset_profitloss", "created_at"], "dm_transfer": ["uid", "currency", "amount", "transfer_result_type", "created_time"], "swap_transfer": ["uid", "currency", "amount", "transfer_result_type", "created_time"], "device_fingerprint": ["proxy", "jailbreak", "sim_id", "imsi", "sim_country", "sim_operator", "lat", "lng", "uid", "sys", "sys_ver", "brand", "p_type", "uuid", "vtoken", "network_type", "imei", "mac", "andr_id", "ssid", "bssid", "idfa", "idfv", "created_time"]}kjow,success
f177fb0965ce24c30000000000000013,2024-02-15T23:12:51+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,th test,success
c368828065cf147b000000000000000d,2024-02-16T15:53:33+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
c368828065cf147b000000000000000d,2024-02-16T15:53:55+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,show databases like '%kyc%';,success
c368828065cf147b000000000000000d,2024-02-16T15:54:15+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,use huobi_platform_kyc,success
c368828065cf147b000000000000000d,2024-02-16T15:54:22+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,show tables;,success
c368828065cf147b000000000000000d,2024-02-16T15:54:37+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,desc t_user_kyc_item_state;,success
c368828065cf147b000000000000000d,2024-02-16T15:55:11+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,select * from t_user_kyc_item_state limit 3;,success
c368828065cf147b000000000000000d,2024-02-16T15:56:38+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,select * from t_user_kyc_item_state where f_uid in(116008985,54465913);,success
c368828065cf147b000000000000000d,2024-02-16T15:57:04+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,select * from t_user_kyc_item_state where f_uid in (116008985,54465913);,success
c368828065cf147b000000000000000d,2024-02-16T16:05:39+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,desc t_user_kyc_item_record;,success
c368828065cf147b000000000000000d,2024-02-16T16:05:58+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,select * from t_user_kyc_item_record where f_uid in (116008985,54465913);,success
c368828065cf147b000000000000000d,2024-02-16T16:06:15+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,desc t_user_kyc_auth;,success
c368828065cf147b000000000000000d,2024-02-16T16:06:45+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,select * from t_user_kyc_auth where f_uid in (116008985,54465913);,success
c368828065cf147b000000000000000d,2024-02-16T16:09:11+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,show create table t_user_kyc_auth;,success
c368828065cf147b000000000000000d,2024-02-16T16:10:47+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,select count(*) from t_user_kyc_auth;,success
c368828065cf147b000000000000000d,2024-02-16T16:12:58+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,use huobi,success
c368828065cf147b000000000000000d,2024-02-16T16:13:03+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,show tables;,success
fc2dd77e65cf1989000000000000000d,2024-02-16T16:15:07+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
fc2dd77e65cf1989000000000000000d,2024-02-16T16:15:18+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,obi,success
fc2dd77e65cf1989000000000000000d,2024-02-16T16:15:52+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,show tables like '%depo%';,success
fc2dd77e65cf1989000000000000000d,2024-02-16T16:16:39+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,select * from card_deposit_history limit 2;,success
fc2dd77e65cf1989000000000000000d,2024-02-16T16:17:10+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,show create table card_deposit_history;,success
fc2dd77e65cf1989000000000000000d,2024-02-16T16:17:37+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,select * from card_deposit_history order by id desc limit 2;,success
fc2dd77e65cf1989000000000000000d,2024-02-16T16:18:19+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,show create table bityes_deposit_coin;,success
fc2dd77e65cf1989000000000000000d,2024-02-16T16:18:50+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,select * from bityes_deposit_coin order by id desc limit 2;,success
fc2dd77e65cf1989000000000000000d,2024-02-16T16:20:27+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,select count(*) from bityes_deposit_coin;,success
fc2dd77e65cf1989000000000000000d,2024-02-16T16:21:42+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,show create table deposit_history;,success
fc2dd77e65cf1989000000000000000d,2024-02-16T16:22:17+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,select * from deposit_history order by id desc limit 2;,success
fc2dd77e65cf1989000000000000000d,2024-02-16T16:25:09+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,show create table deposit_total;,success
fc2dd77e65cf1989000000000000000d,2024-02-16T16:28:49+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,show tables like '%withdraw%';,success
fc2dd77e65cf1989000000000000000d,2024-02-16T16:30:25+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,show create table card_withdraw_history;,success
fc2dd77e65cf1989000000000000000d,2024-02-16T16:30:47+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,show create table withdraw_history;,success
fc2dd77e65cf1989000000000000000d,2024-02-16T16:36:49+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,select * from card_deposit_history order by id desc limit 2;,success
fc2dd77e65cf1989000000000000000d,2024-02-16T16:55:05+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,select count(*) from deposit_history where channel_id in (5,6,7,10,11,13);,success
fc2dd77e65cf1989000000000000000d,2024-02-16T16:59:38+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********, and user_id in (12824916,5446591,11600898,12741774,12743215,9348858,5357351,11828571,12455824,13241676,12984049,5526033,5111367,5555334,1397228,18431651,5433346,12741393,4031175,6239562,12500028,5091579,5659587,7154355,10060769,9065524,9298189,12541046,5325342,13423122,12749169,13258692,9006969,12847140,11392616,7273488,13394041,12480511,9049306,12482061,1088210,26358049,10642104,11924661,18551452,12672427,12625434,10383025,9415881,9373798,1642038,10323151,10721995,9514212,12769698,13659854,12750601,13265003,5961973,12516803,8390764,5616754,5533525,7255414,12652715,12029853,13265954,4601686,8995421,12306779,12472628,5863494,7731228,10365268,6260218,12726670,12765940,5432163,19832131,4496313,11297688,12511878,5198373,5854276,11924391,12514008,12694925,9658502,12603837,4581533,12751819,9010578,5446595,9304298,9013404,9849588,11880661,5523313,12642757,9011055,12741654,4002485,12876330,7258860,10223379,5830212,26478731,10364855,6444658,9302737,13545165,5207903,6212744,10301988,9012770,9510749,10297145,5473344,12526809,5110281,12464424,5427264,5579994,12709349,6212336,5465457,14706808,10671867,7256893,7354130,12778099,5594858,10718314,11426216,8550638,12776990,9887796,5081364,10294468,12453670,12744066,11134476,5488562,12667499,12139929,15969899,4385957,10766909,9037961,12453484,12625018,9097463,12452712,9729781,889619,9343512,6205688,1068263,9450063,16282696,13529471,5080880,13008145,5240237,11358146,9491987,5109245,7254711,12854005,12476452,10234658,10316606,8675825,5118603,566262,10223346,12627816,10324028,12619746,5521033,10255306,10777101,12605865,12947073,5926608,10552586,12481638,9343710,4118249,10289207,5340964,5426608,12619308,9290421,6186430,10742430,12443793,840084,961876,1696386,19719964,12639416,11963841,6207158,17496080,15245455,13421065,15872018,12123000,12741199,12446309,7257184,6125696,9718553,6038436,5294184,12746962,10986423,12768796,5081498,12632763,12695015,5419690,11307314,6105010,13060856,12605834,11662044,6029315,5283982,10227050,3790737,1567209,11196385,12766679,12499856,16742389,13106638,166873,1762393,9653304,5839106,11065553,12143949,5421538,616859,12472724,9898270,12488937,10229801,5054412,22348039,12618767,9463395,12444355,5420596,12745929,20667864,12439592,5555316,9302169,12740199,12692425,14692214,12442240,13036090,5121501,12729232,12751202,7359794,10229635,6104624,5292122,9577220,15956411,15025245,6402405,12438739,12615092,13524602,12535801,11708114,5593481,16964200,12786864,2082754,12443053,12122057,11073680,12596281,10087397,12930999,9821512,5111261,12744268,22866769,8550583,9562479,12686879,6029729,14645985,5437416,10364701,10230144,8247413,13229250,10998963,12808716,5584630,1403247,12762637,744209,12420172,6202891,10284524,10701963,9268974,9030996,23340190,9108754,6336498,12438177,9521406,12565414,8064527,10227226,4617834,5025175,18027935,12500872,9006900,5923139,6490569,7342034,7352576,5994129,5856072,17218338,9517710,16631423,10004575,12648669,10188075,1608915,8389614,10132460,9510245,5473604,5550602,10432148,9364684,10398599,12524824,11962900,16660427,10229966,12769127,12442212,9436179,8649149,4259028,12463469,12491802,6159444,12770989,9354436,5582043,5534381,9449736,16898358,12454797,12539633,9509806,10342472,10224168,9508825,5447508,12765599,5447195,2037994,5616172,14776244,17492280,14782891,9287182,12446969,5607084,5446869,5531269,14536215,4499291,6165105,12946357,12494476,9520315,5425545,10226007,5426603,12489096,5432023,9849397,11110843,12287542,5076881,5659904,17298491,12429521,14476653,14750515,5431397,8762534,10060391,5801487,12463505,10373174,5426605,12637800,11082549,9005626,12467274,5604561,9020236,11616493,10299530,10297041,5427460,12466868,5432651,12482254,6193797,9286419,12538790,7221440,11094857,9290519,9378969,12745139,9340102,9354605,12486030,11720914,9009801,9473100,10401355,9566000,11083097,12511771,12457450,10368471,20076436,10774701,20155725,20780438,11075255,12485864,12045190,12775996,12830735,12497844,5431544,12491951,12534671,12128732,5427541,12538846,9585944,12430089,10364697,12807662,20312514,10158025,783501,19061022,12569438,8220187,10297794,12491120,14726059,14913222,10224075,5520657,12544297,9139354,5447412,8650477,10250455,11579125,6036698,8969392,783941,12524400,12762322,9606947,1104572,12603672,14171359,12637111,5503878,12435342,10311133,9378942,9379716,12443455,10155355,12590549,10382201,10301638,5083136,10272357,5066586,5425583,12443592,9571718,2355925,10275278,9302243,12208129,5432275,12928089,10160062,9510826,12464327,5426828,5531368,10117805,12556005,5520583,12461735,9291998,5244876,5541038,9010293,10985976,7229608,12611061,14800591,12581112,12448773,9494336,12461160,12959281,5426842,5995667,9543357,6090249,19312792,5600743,12929696,12766058,12748711,8664658,14787419,12442115,9059615,10298619,18365474,6036694,11413802,14694334,12701121,5076020,10210159,12295740,5426710,9315940,12504736,10270970,5431378,8935346,12446290,10060790,819826,12502936,9550718,12747417,10229292,5432636,5067066,10084095,12441051,4623219,9512468,14720761,10384844,760847,10807126,12581674,5426884,794621,10264797,9508850,5425782,5446661,9931327,12747616,12454163,12762199,14447216,1,success
fc2dd77e65cf1989000000000000000d,2024-02-16T16:59:45+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,);,success
fc2dd77e65cf1989000000000000000d,2024-02-16T17:01:02+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,withdraw,success
fc2dd77e65cf1989000000000000000d,2024-02-16T17:07:42+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,show tables like '%platform%';,success
fc2dd77e65cf1989000000000000000d,2024-02-16T17:08:04+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,select * from platform_transfer_record limit 2;,success
fc2dd77e65cf1989000000000000000d,2024-02-16T17:08:37+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,select * from platform_transfer_user limit 2;,success
fc2dd77e65cf1989000000000000000d,2024-02-16T17:16:54+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-devops,***********,quit,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:28:58+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,cd /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-avenir/,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:28:59+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,ll,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:29:07+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,vim Dockerfile,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:29:22+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,:q,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:31:21+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,docker pull node:20.11.0,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:31:52+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,docker ls images,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:31:58+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,docker --help,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:32:15+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,docker images,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:32:55+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,df -h,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:33:23+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,ll,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:33:28+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,vim Makefile,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:34:04+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,:q,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:34:13+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,vim build.sh,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:37:05+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,:q,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:38:08+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,docker login miniglobal-harbor.cogdocs.com,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:40:35+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,docker tag 683cfa47c0ac miniglobal-harbor.cogdocs.com/builder/node/20.11.0:20.11.0,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:41:03+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,docker push miniglobal-harbor.cogdocs.com/builder/node/20.11.0:20.11.0,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:44:54+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,docker tag 683cfa47c0ac miniglobal-harbor.cogdocs.com/builder/node:20.11.0,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:45:21+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,docker push miniglobal-harbor.cogdocs.com/builder/node:20.11.0,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:46:02+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,vim build.sh,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:46:15+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,:wq,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:46:17+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,ll,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:46:23+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,cd app/,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:46:24+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,ll,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:46:30+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,cd bin/,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:46:31+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,ll,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:46:43+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,less activate,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:47:10+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,less install_deps,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:47:17+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,cd ..,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:47:29+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,cd lib/fed-cn-web/,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:47:30+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,ll,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:47:36+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,cd ../..,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:47:38+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,ll,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:47:40+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,cd ..,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:47:41+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,ll,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:47:44+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,cd build_command_pkgs/,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:47:45+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,ll,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:48:01+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,vim __build.sh,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:48:09+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,/fed,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:48:24+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,:q,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:48:33+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,vim fed-cn-web__build.sh,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:48:39+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,:q,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:48:45+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,cd m2/,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:48:46+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,ll,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:48:49+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,cd ..,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:48:50+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,ll,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:49:03+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,cd fed-cn-web/,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:49:04+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,ll,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:49:16+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,cd ..,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:49:18+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,ll,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:49:23+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,rm -r fed-cn-web,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:49:27+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,ll,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:49:31+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,ll,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:49:31+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,cd ..,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:49:36+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,ll,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:50:55+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,less Makefile,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:51:38+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,make build,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:51:59+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,vim build.sh,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:52:42+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,:q,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:52:51+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,vim Makefile,success
ca0b3f5e65cf2abf000000000000000d,2024-02-16T17:54:48+08:00,cmd.Command,avenir_user01,223.104.40.4,miniglobal-deploy,***********,:q,success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:33:33+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,y,success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:33:39+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,╰─[:)] # screen -Dr f,success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:33:42+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2$ docker  3$ ..f475b438974da  4$ vim  5$ vim  6-$ ~/forensic_etl  (7*$~/forensic_etl)                                          ][16/02 16:33],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:33:48+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,0qA,success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:33:56+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0-$ mycli  1$ ~  2$ docker  (3*$..f475b438974da)  4$ vim  5$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                          ][16/02 16:34],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:33:57+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0-$ mycli  1$ ~  2$ docker  (3*$..d4879e8be4e09)  4$ vim  5$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                          ][16/02 16:34],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:34:00+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0-$ mycli  1$ ~  2$ docker  (3*$..d4879e8be4e09)  4$ vim  5$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                          ][16/02 16:34],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:34:00+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0-$ mycli  1$ ~  2$ docker  (3*$..be4e09/result)  4$ vim  5$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                          ][16/02 16:34],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:34:04+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0-$ mycli  1$ ~  2$ docker  (3*$..be4e09/result)  4$ vim  5$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                          ][16/02 16:34],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:34:15+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,mv 4306_forensic.xlsx /data/forensics/0a4ab617b2ca4df1b6fd4879e8be4e09/result,success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:34:23+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2-$ docker  (3*$..be4e09/result)  4$ vim  5$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                          ][16/02 16:34],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:34:26+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2-$ docker  (3*$..be4e09/result)  4$ vim  5$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                          ][16/02 16:34],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:34:27+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2-$ docker  (3*$..be4e09/result)  4$ vim  5$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                          ][16/02 16:34],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:34:28+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2-$ docker  (3*$..d4879e8be4e09)  4$ vim  5$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                          ][16/02 16:34],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:34:33+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,6ls,success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:34:47+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2$ docker  (3*$..d4879e8be4e09)  4$ vim  5$ vim  6-$ ~/forensic_etl  7$ ~/forensic_etl                                          ][16/02 16:35],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:34:51+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2$ docker  (3*$..d4879e8be4e09)  4$ vim  5$ vim  6-$ ~/forensic_etl  7$ ~/forensic_etl                                          ][16/02 16:35],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:34:52+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2$ docker  (3*$..d4879e8be4e09)  4$ vim  5$ vim  6-$ ~/forensic_etl  7$ ~/forensic_etl                                          ][16/02 16:35],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:34:54+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2$ docker  (3*$..d4879e8be4e09)  4$ vim  5$ vim  6-$ ~/forensic_etl  7$ ~/forensic_etl                                          ][16/02 16:35],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:34:54+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2$ docker  (3*$..be4e09/result)  4$ vim  5$ vim  6-$ ~/forensic_etl  7$ ~/forensic_etl                                          ][16/02 16:35],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:34:56+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2$ docker  (3*$..be4e09/result)  4$ vim  5$ vim  6-$ ~/forensic_etl  7$ ~/forensic_etl                                          ][16/02 16:35],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:35:10+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,5736,success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:35:25+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0-$ mycli  1$ ~  2$ docker  3$ ..be4e09/result  4$ vim  5$ vim  (6*$~/forensic_etl)  7$ ~/forensic_etl                                          ][16/02 16:35],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:35:31+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2$ docker  (3*$..be4e09/result)  4$ vim  5$ vim  6-$ ~/forensic_etl  7$ ~/forensic_etl                                          ][16/02 16:35],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:35:34+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2$ docker  (3*$..be4e09/result)  4$ vim  5$ vim  6-$ ~/forensic_etl  7$ ~/forensic_etl                                          ][16/02 16:35],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:35:35+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2$ docker  (3*$..d4879e8be4e09)  4$ vim  5$ vim  6-$ ~/forensic_etl  7$ ~/forensic_etl                                          ][16/02 16:35],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:35:40+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2$ docker  (3*$..d4879e8be4e09)  4$ vim  5$ vim  6-$ ~/forensic_etl  7$ ~/forensic_etl                                          ][16/02 16:35],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:35:40+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2$ docker  (3*$..54a484c641bec)  4$ vim  5$ vim  6-$ ~/forensic_etl  7$ ~/forensic_etl                                          ][16/02 16:35],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:35:41+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2$ docker  (3*$..54a484c641bec)  4$ vim  5$ vim  6-$ ~/forensic_etl  7$ ~/forensic_etl                                          ][16/02 16:35],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:35:42+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2$ docker  (3*$..641bec/result)  4$ vim  5$ vim  6-$ ~/forensic_etl  7$ ~/forensic_etl                                          ][16/02 16:35],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:35:44+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2$ docker  (3*$..641bec/result)  4$ vim  5$ vim  6-$ ~/forensic_etl  7$ ~/forensic_etl                                          ][16/02 16:36],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:36:32+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2$ docker  3-$ ..641bec/result  4$ vim  (5*$vim)  6$ ~/forensic_etl  7$ ~/forensic_etl                                          ][16/02 16:36],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:36:34+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,0$ mycli  1$ ~  2$ docker  3-$ ..641bec/result  4$ vim  (5*$~/forensic_etl)  6$ ~/forensic_etl  7$ ~/forensic_etl                                    ][16/02 16:36],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:36:37+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,4q,success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:36:38+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                              0$ mycli  1$ ~  2$ docker  3$ ..641bec/result  (4*$~/forensic_etl)  5-$ ~/forensic_etl  6$ ~/forensic_etl  7$ ~/forensic_etl                               ][16/02 16:36],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:36:40+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                              0$ mycli  1$ ~  2$ docker  3$ ..641bec/result  (4*$~/forensic_etl)  5-$ ~/forensic_etl  6$ ~/forensic_etl  7$ ~/forensic_etl                               ][16/02 16:36],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:36:40+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                              0$ mycli  1$ ~  2$ docker  3$ ..641bec/result  (4*$..forensic_task)  5-$ ~/forensic_etl  6$ ~/forensic_etl  7$ ~/forensic_etl                              ][16/02 16:36],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:36:42+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                              0$ mycli  1$ ~  2$ docker  3$ ..641bec/result  (4*$..forensic_task)  5-$ ~/forensic_etl  6$ ~/forensic_etl  7$ ~/forensic_etl                              ][16/02 16:36],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:36:45+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,jkxxwq,success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:36:46+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                              0$ mycli  1$ ~  2$ docker  3$ ..641bec/result  (4*$..forensic_task)  5-$ ~/forensic_etl  6$ ~/forensic_etl  7$ ~/forensic_etl                              ][16/02 16:37],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:36:51+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                              0$ mycli  1$ ~  2$ docker  3$ ..641bec/result  (4*$..forensic_task)  5-$ ~/forensic_etl  6$ ~/forensic_etl  7$ ~/forensic_etl                              ][16/02 16:37],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:37:01+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,kllllxxwq,success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:37:02+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                              0$ mycli  1$ ~  2$ docker  3$ ..641bec/result  (4*$..forensic_task)  5-$ ~/forensic_etl  6$ ~/forensic_etl  7$ ~/forensic_etl                              ][16/02 16:37],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:37:07+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                              0$ mycli  1$ ~  2$ docker  3-$ ..641bec/result  (4*$..forensic_task)  5$ ~/forensic_etl  6$ ~/forensic_etl  7$ ~/forensic_etl                              ][16/02 16:37],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:37:11+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                              0$ mycli  1$ ~  2$ docker  3-$ ..641bec/result  (4*$..task/trade_dw)  5$ ~/forensic_etl  6$ ~/forensic_etl  7$ ~/forensic_etl                              ][16/02 16:37],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:37:11+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                              0$ mycli  1$ ~  2$ docker  3-$ ..641bec/result  (4*$..forensic_task)  5$ ~/forensic_etl  6$ ~/forensic_etl  7$ ~/forensic_etl                              ][16/02 16:37],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:37:15+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                              0$ mycli  1$ ~  2$ docker  3-$ ..641bec/result  (4*$..task/trade_dw)  5$ ~/forensic_etl  6$ ~/forensic_etl  7$ ~/forensic_etl                              ][16/02 16:37],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:37:18+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                    0$ mycli  1$ ~  2$ docker  3-$ ..641bec/result  (4*$vim)  5$ ~/forensic_etl  6$ ~/forensic_etl  7$ ~/forensic_etl                                    ][16/02 16:37],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:37:29+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                    0$ mycli  1$ ~  2$ docker  3$ ..641bec/result  4-$ vim  (5*$~/forensic_etl)  6$ ~/forensic_etl  7$ ~/forensic_etl                                    ][16/02 16:37],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:37:36+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,kkjjjjjjjjllllljjllkli# w,success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:37:39+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,jhi# w,success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:37:41+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2$ docker  3$ ..641bec/result  4$ vim  5-$ vim  (6*$~/forensic_etl)  7$ ~/forensic_etl                                          ][16/02 16:37],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:37:43+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2$ docker  3$ ..641bec/result  4$ vim  5-$ vim  (6*$~/forensic_etl)  7$ ~/forensic_etl                                          ][16/02 16:37],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:37:45+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2$ docker  3$ ..641bec/result  4$ vim  5-$ vim  (6*$~/forensic_etl)  7$ ~/forensic_etl                                          ][16/02 16:38],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:39:17+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,jjjhhhhhhhhhhhhhhhhhhhhhhhhki# hxxuxxw,success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:39:19+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2$ docker  3$ ..641bec/result  4$ vim  5-$ vim  (6*$~/forensic_etl)  7$ ~/forensic_etl                                          ][16/02 16:39],success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:40:15+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,# hxxw,success
a73623ea65cf8e5b0000000000000013,2024-02-17T00:40:18+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2$ docker  3$ ..641bec/result  4$ vim  5-$ vim  (6*$~/forensic_etl)  7$ ~/forensic_etl                                          ][16/02 16:40],success
57a3634e65d162520000000000000019,2024-02-18T09:50:16+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
57a3634e65d162520000000000000019,2024-02-18T09:51:08+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,use proamsdwdb;,success
57a3634e65d162520000000000000019,2024-02-18T09:51:22+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
57a3634e65d162520000000000000019,2024-02-18T09:51:22+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,where f_state in(3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
57a3634e65d162520000000000000019,2024-02-18T09:51:22+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,group by f_currency;,success
e157d0b965d1628f000000000000000d,2024-02-18T09:52:54+08:00,file.Upload,avenir_user01,**************,MiniAD04,172.28.2.251,"(A{4mh@KMx<3P\CrTTX.txt,success
e157d0b965d1628f000000000000000d,2024-02-18T09:54:35+08:00,file.Upload,avenir_user01,**************,MiniAD04,172.28.2.251,ocr.txt,success
e157d0b965d1628f000000000000000d,2024-02-18T09:54:35+08:00,file.UploadSave,avenir_user01,**************,MiniAD04,172.28.2.251,keyboard.txt,success
07c3f61165d16631000000000000000d,2024-02-18T10:08:21+08:00,file.Upload,avenir_user01,**************,MiniAD04,172.28.2.251,ga[@SVj%- 3Z)?pGdXMt.txt,success
57a3634e65d162520000000000000019,2024-02-18T10:10:59+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,and f_state in(3,6,8),success
57a3634e65d162520000000000000019,2024-02-18T10:10:59+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,and from_unixtime(f_updated_at/1000) > '2023-01-06',success
57a3634e65d162520000000000000019,2024-02-18T10:10:59+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount)) as amount from t_withdraw_virtual w ,success
57a3634e65d162520000000000000019,2024-02-18T10:10:59+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,where f_currency in('btc','eth','usdt','ht') ,success
57a3634e65d162520000000000000019,2024-02-18T10:11:00+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,group by f_currency;,success
07c3f61165d16631000000000000000d,2024-02-18T10:17:40+08:00,file.UploadSave,avenir_user01,**************,MiniAD04,172.28.2.251,keyboard.txt,success
07c3f61165d16631000000000000000d,2024-02-18T10:17:40+08:00,file.Upload,avenir_user01,**************,MiniAD04,172.28.2.251,ocr.txt,success
57a3634e65d162520000000000000019,2024-02-18T10:33:41+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,use hbg_trade; ,success
57a3634e65d162520000000000000019,2024-02-18T10:34:12+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,use hbg_trade; ,success
57a3634e65d162520000000000000019,2024-02-18T10:34:16+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,select f_state, count(*) as user_count,success
57a3634e65d162520000000000000019,2024-02-18T10:34:17+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,from t_account_manage_fee_snapshot  ,success
57a3634e65d162520000000000000019,2024-02-18T10:34:17+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,where f_day in('********', '********'),success
57a3634e65d162520000000000000019,2024-02-18T10:34:17+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,group by f_state;,success
57a3634e65d162520000000000000019,2024-02-18T10:38:13+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,select f_state, count(*) as user_count from t_account_manage_fee_snapshot   where f_day in('********') group by f_state;,success
288e7bfa65d16b65000000000000000d,2024-02-18T11:00:01+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
288e7bfa65d16b65000000000000000d,2024-02-18T11:00:15+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
288e7bfa65d16b65000000000000000d,2024-02-18T11:00:27+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,use huobi,success
288e7bfa65d16b65000000000000000d,2024-02-18T11:00:42+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,show tables like '%history%';,success
288e7bfa65d16b65000000000000000d,2024-02-18T11:01:04+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,show create table deposit_history;,success
288e7bfa65d16b65000000000000000d,2024-02-18T11:01:10+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,show create table withdraw_history;,success
288e7bfa65d16b65000000000000000d,2024-02-18T11:09:24+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select count(*) from deposit_history where address in ("**********************************","*********************************","**********************************","**********************************","**********************************","**********************************","**********************************");,success
288e7bfa65d16b65000000000000000d,2024-02-18T11:09:31+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select count(*) from withdraw_history where withdraw_account in ("**********************************","*********************************","**********************************","**********************************","**********************************","**********************************","**********************************");,success
288e7bfa65d16b65000000000000000d,2024-02-18T11:11:56+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select count(*) from deposit_history where trade_id in ("**********************************","*********************************","**********************************","**********************************","**********************************","**********************************","**********************************");,success
288e7bfa65d16b65000000000000000d,2024-02-18T11:12:26+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select * from deposit_history order by id desc limit 2;,success
aa779cfd65d17697000000000000000d,2024-02-18T11:16:41+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
aa779cfd65d17697000000000000000d,2024-02-18T11:16:51+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,use huobi,success
aa779cfd65d17697000000000000000d,2024-02-18T11:17:07+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select user_id, out_trade_id, withdraw_account from withdraw_history where withdraw_account in ("**********************************","*********************************","**********************************","**********************************","**********************************","**********************************","**********************************");,success
aa779cfd65d17697000000000000000d,2024-02-18T11:20:35+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,desc deposit_history;,success
aa779cfd65d17697000000000000000d,2024-02-18T11:21:10+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select * from deposit_history where trade_id = '2016040421560785494';,success
aa779cfd65d17697000000000000000d,2024-02-18T11:21:19+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select * from deposit_history where out_trade_id = '2016040421560785494';,success
aa779cfd65d17697000000000000000d,2024-02-18T11:21:40+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select * from deposit_history where id = '2016040421560785494';,success
aa779cfd65d17697000000000000000d,2024-02-18T11:23:40+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,show tables like '%channel%';,success
aa779cfd65d17697000000000000000d,2024-02-18T11:23:47+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,desc channel;,success
aa779cfd65d17697000000000000000d,2024-02-18T11:23:55+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,desc my_channel;,success
aa779cfd65d17697000000000000000d,2024-02-18T11:24:19+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,show create table my_channel;,success
aa779cfd65d17697000000000000000d,2024-02-18T11:25:30+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select * from my_channel where address_type <> 0 limit 10;,success
aa779cfd65d17697000000000000000d,2024-02-18T11:27:51+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select * from my_channel where channel_id in (2,9) limit 10;,success
aa779cfd65d17697000000000000000d,2024-02-18T11:28:27+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select * from my_channel where channel_id in (2,9) and account in ("**********************************","*********************************","**********************************","**********************************","**********************************","**********************************","**********************************");,success
aa779cfd65d17697000000000000000d,2024-02-18T11:29:56+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,show tables like '%addr%';,success
aa779cfd65d17697000000000000000d,2024-02-18T11:30:11+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,show create table user_address;,success
aa779cfd65d17697000000000000000d,2024-02-18T11:30:39+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select count(*) from user_address;,success
aa779cfd65d17697000000000000000d,2024-02-18T11:30:54+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select count(*) from wallet_addresses;,success
aa779cfd65d17697000000000000000d,2024-02-18T11:31:10+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select count(*) from user_address_ltc;,success
aa779cfd65d17697000000000000000d,2024-02-18T11:32:12+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select * from user_address where address in ("**********************************","*********************************","**********************************","**********************************","**********************************","**********************************","**********************************");,success
aa779cfd65d17697000000000000000d,2024-02-18T11:32:36+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select * from user_address limit 3;,success
8f32a5d865d17af4000000000000000d,2024-02-18T11:35:54+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,cd /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-avenir/,success
8f32a5d865d17af4000000000000000d,2024-02-18T11:36:04+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,make build TAG=PRD_T402181,success
aa779cfd65d17697000000000000000d,2024-02-18T11:38:02+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,show create table my_channel;,success
aa779cfd65d17697000000000000000d,2024-02-18T11:38:57+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select * from my_channel where user_id in (551173) limit 10;,success
aa779cfd65d17697000000000000000d,2024-02-18T11:39:58+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select * from my_channel where user_id in (551173);,success
aa779cfd65d17697000000000000000d,2024-02-18T11:40:26+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select * from my_channel where user_id in (546608);,success
aa779cfd65d17697000000000000000d,2024-02-18T11:46:21+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select * from withdraw_history where out_trade_id in ('2016022521559831238', 'dfbfa27bec432175bc5a6988ac8416fbf92d89bbac4165b6324f98cd2b747577');,success
aa779cfd65d17697000000000000000d,2024-02-18T11:47:10+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,show create table withdraw_history;,success
aa779cfd65d17697000000000000000d,2024-02-18T11:48:34+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select count(*) from withdraw_history where withdraw_account in ("**********************************","*********************************","**********************************","**********************************","**********************************","**********************************","**********************************") and  status = 4;,success
aa779cfd65d17697000000000000000d,2024-02-18T11:49:23+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select user_id, out_trade_id, withdraw_account from withdraw_history where withdraw_account in ("**********************************","*********************************","**********************************","1Q5k4dGMr,success
aa779cfd65d17697000000000000000d,2024-02-18T11:51:29+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,show tables like '%user%';,success
aa779cfd65d17697000000000000000d,2024-02-18T11:51:35+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,desc user;,success
aa779cfd65d17697000000000000000d,2024-02-18T11:53:56+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select u.uid, wh.out_trade_id, wh.withdraw_account from withdraw_history wh left join user u on wh.user_id = u.id where wh.withdraw_account in ("**********************************","*********************************","**********************************","**********************************","**********************************","**********************************","**********************************") and wh.status = 4;,success
aa779cfd65d17697000000000000000d,2024-02-18T11:55:11+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,quit,success
aa779cfd65d17697000000000000000d,2024-02-18T11:55:13+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,cd /tmp,success
aa779cfd65d17697000000000000000d,2024-02-18T11:55:15+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,ll,success
aa779cfd65d17697000000000000000d,2024-02-18T11:55:20+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com huobi -e "select u.uid, wh.out_trade_id, wh.withdraw_account from withdraw_history wh left join user u on wh.user_id = u.id where wh.withdraw_account in ("**********************************","*********************************","**********************************","**********************************","**********************************","**********************************","**********************************") and wh.status = 4;" > withdraw_20240218.tsv,success
aa779cfd65d17697000000000000000d,2024-02-18T11:55:33+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,ll,success
aa779cfd65d17697000000000000000d,2024-02-18T11:55:37+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,less withdraw_20240218.tsv,success
aa779cfd65d17697000000000000000d,2024-02-18T11:56:43+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,kjkkkqmysql -u superdba -P 3614 -p -h master-3614.docsl.com huobi -e 'select u.uid, wh.out_trade_id, wh.withdraw_account from withdraw_history wh left join user u on wh.user_id = u.id where wh.withdraw_account in ("**********************************","*********************************","**********************************","**********************************","**********************************","**********************************","**********************************") and wh.status = 4;' > withdraw_20240218.tsv,success
aa779cfd65d17697000000000000000d,2024-02-18T11:56:48+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,ll,success
aa779cfd65d17697000000000000000d,2024-02-18T11:56:52+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,less withdraw_20240218.tsv,success
aa779cfd65d17697000000000000000d,2024-02-18T11:57:09+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,qcat withdraw_20240218.tsv | sed 's/\t/,/g' > withdraw_20240218.csv,success
aa779cfd65d17697000000000000000d,2024-02-18T11:57:14+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,less withdraw_20240218.csv,success
aa779cfd65d17697000000000000000d,2024-02-18T11:57:59+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,ossutil cp withdraw_20240218.csv  oss://miniglobal-devops/hue_log/,success
aa779cfd65d17697000000000000000d,2024-02-18T12:04:10+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,ll,success
aa779cfd65d17697000000000000000d,2024-02-18T12:04:14+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,rm withdraw_20240218.*,success
aa779cfd65d17697000000000000000d,2024-02-18T12:04:15+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,y,success
aa779cfd65d17697000000000000000d,2024-02-18T12:04:16+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,y,success
aa779cfd65d17697000000000000000d,2024-02-18T12:04:32+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
aa779cfd65d17697000000000000000d,2024-02-18T12:04:46+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,use huobi,success
aa779cfd65d17697000000000000000d,2024-02-18T12:05:02+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,show tables;,success
aa779cfd65d17697000000000000000d,2024-02-18T12:09:48+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,show databases like '%hbg%';,success
aa779cfd65d17697000000000000000d,2024-02-18T12:09:56+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,use hbg_trade;,success
aa779cfd65d17697000000000000000d,2024-02-18T12:09:59+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,show tables;,success
aa779cfd65d17697000000000000000d,2024-02-18T12:10:14+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,desc t_account_manage_fee_snapshot;,success
aa779cfd65d17697000000000000000d,2024-02-18T12:16:09+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select count(*) from t_account_manage_fee_snapshot where f_state=0;,success
aa779cfd65d17697000000000000000d,2024-02-18T12:16:35+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select count(*) from t_account_manage_fee_transfer_detail where f_state=0;,success
aa779cfd65d17697000000000000000d,2024-02-18T12:17:05+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,desc t_account_manage_fee_snapshot;,success
aa779cfd65d17697000000000000000d,2024-02-18T12:18:24+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select count(*) from t_account_manage_fee_snapshot where f_state=0 and f_created_at < *************;,success
aa779cfd65d17697000000000000000d,2024-02-18T12:19:06+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select count(*) from t_account_manage_fee_snapshot where f_state=0 and f_created_at < *************;,success
aa779cfd65d17697000000000000000d,2024-02-18T12:19:29+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select count(*) from t_account_manage_fee_snapshot where f_state=0 and f_created_at < *************;,success
aa779cfd65d17697000000000000000d,2024-02-18T12:20:02+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select count(*) from t_account_manage_fee_snapshot where f_state=0 and f_created_at < *************;,success
aa779cfd65d17697000000000000000d,2024-02-18T12:20:12+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select count(*) from t_account_manage_fee_snapshot where f_state=0 and f_created_at < *************;,success
aa779cfd65d17697000000000000000d,2024-02-18T12:20:32+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select count(*) from t_account_manage_fee_transfer_detail where f_state=0 and f_created_at < *************;,success
aa779cfd65d17697000000000000000d,2024-02-18T12:21:11+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select count(*) from t_account_manage_fee_transfer_detail where f_state=0 and f_created_at < *************;,success
aa779cfd65d17697000000000000000d,2024-02-18T12:21:20+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select count(*) from t_account_manage_fee_transfer_detail where f_state=0 and f_created_at < *************;,success
aa779cfd65d17697000000000000000d,2024-02-18T12:21:42+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select count(*) from t_account_manage_fee_transfer_detail where f_state=0 and f_created_at < *************;,success
aa779cfd65d17697000000000000000d,2024-02-18T12:22:11+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select count(*) from t_account_manage_fee_transfer_detail where f_state=0 and f_created_at < *************;,success
aa779cfd65d17697000000000000000d,2024-02-18T12:22:29+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select count(*) from t_account_manage_fee_transfer_detail where f_state=0 and f_created_at < *************;,success
aa779cfd65d17697000000000000000d,2024-02-18T12:23:44+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select count(*) from t_account_manage_fee_transfer_detail where f_state=0 and f_created_at < *************;,success
aa779cfd65d17697000000000000000d,2024-02-18T12:24:06+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select count(*) from t_account_manage_fee_snapshot where f_state=0 and f_created_at < *************;,success
aa779cfd65d17697000000000000000d,2024-02-18T12:24:42+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select count(*) from t_account_manage_fee_transfer_detail where f_state=0 and f_created_at < *************;,success
aa779cfd65d17697000000000000000d,2024-02-18T12:25:22+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select count(*) from t_account_manage_fee_transfer_detail where f_state=0;,success
aa779cfd65d17697000000000000000d,2024-02-18T12:25:30+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select count(*) from t_account_manage_fee_transfer_detail where f_state=0;,success
aa779cfd65d17697000000000000000d,2024-02-18T12:26:26+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select count(*) from t_account_manage_fee_transfer_detail where f_state=0;,success
aa779cfd65d17697000000000000000d,2024-02-18T12:27:27+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select count(*) from t_account_manage_fee_transfer_detail where f_state=0;,success
aa779cfd65d17697000000000000000d,2024-02-18T12:49:28+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,quit,success
72da705365d1a9f3000000000000000d,2024-02-18T14:55:48+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
72da705365d1a9f3000000000000000d,2024-02-18T14:55:57+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,use huobi,success
72da705365d1a9f3000000000000000d,2024-02-18T14:56:15+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,desc my_channel;,success
72da705365d1a9f3000000000000000d,2024-02-18T14:56:47+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select * from my_channel where user_id in (455283,455283,529135,529135,529135,285553,533977,533977,533977,551173,546898,546898,546608,546898,533977,551173,533977,546898,546608,533977,312943,411958,551303,551303,546898,372069,534775,534775,551303,533977,546898,534775,551303,546898,204567,312943,534775,389409,533977,551303,546898,546608,546608,372069,547335,535275,547335,535275,547335,547335,547335,547335,547335,547335,577075,577075,547335,547335,547335,547335,547335,547335,547335,547335,547335,535275,358034,547335,547335,535275,547335,547335,547335,358034,547335,547335,547335,547335,547335,547335,577075,547335,547335,577075,547335,577075,577075,339400,339400,577075,624850,339400,339400,339400,748686,577075,624850,339400,624850,551173,551173,551173,551173,551173,551173,551173,551173,551173,529135,533977,533977,529135,533977,285553,529135,533977,529135,533977,533977,533977,546898,546898,533977,533977,546898,533977,546898,529135,436944,549841,546608,533977,533977,450510,546898,546898,546898,533977,546898,469813,551303,546898,551303,546898,551173,308946,551303,546608,546898,551303,312943,551303,546898,546898,546898,469813,308946,551303,546898,551173,551173,546608,551303,469813,551173,308946,551173,551303,546898,480287,551303,551303,551303,533977,551303,551303,551173,551303,551173,546898,546898,533977,551303,551303,312943,533977,551303,480287,546898,551173,546898,551303,558305,558305,551303,558822,551173,551173,551173,546898,546898,204567,533977,204567,551303,551303,204567,546898,546898,551303,546898,411958,551303,551303,312943,551303,546898,533977,551173,546898,533977,551303,558305,546898,558305,551173,533977,551303,546898,551303,533977,558305,533977,558822,480287,546898,551173,546898,533977,546608,411958,558305,533977,480287,546898,551303,411958,558305,546898,312943,533977,558305,551173,551303,533977,533977,546898,546898,533977,558305,546608,312943,546898,551303,411958,551303,548251,535902,535902);,success
72da705365d1a9f3000000000000000d,2024-02-18T14:57:05+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select * from my_channel where user_id in (455283,455283,529135,529135,529135,285553,533977,533977,533977,551173,546898,546898,546608,546898,533977,551173,533977,546898,546608,533977,312943,411958,551303,551303,546898,372069,534775,534775,551303,533977,546898,534775,551303,546898,204567,312943,534775,389409,533977,551303,546898,546608,546608,372069,547335,535275,547335,535275,547335,547335,547335,547335,547335,547335,577075,577075,547335,547335,547335,547335,547335,547335,547335,547335,547335,535275,358034,547335,547335,535275,547335,547335,547335,358034,547335,547335,547335,547335,547335,547335,577075,547335,547335,577075,547335,577075,577075,339400,339400,577075,624850,339400,339400,339400,748686,577075,624850,339400,624850,551173,551173,551173,551173,551173,551173,551173,551173,551173,529135,533977,533977,529135,533977,285553,529135,533977,529135,533977,533977,533977,546898,546898,533977,533977,546898,533977,546898,529135,436944,549841,546608,533977,533977,450510,546898,546898,546898,533977,546898,469813,551303,546898,551303,546898,551173,308946,551303,546608,546898,551303,312943,551303,546898,546898,546898,469813,308946,551303,546898,551173,551173,546608,551303,469813,551173,308946,551173,551303,546898,480287,551303,551303,551303,533977,551303,551303,551173,551303,551173,546898,546898,533977,551303,551303,312943,533977,551303,480287,546898,551173,546898,551303,558305,558305,551303,558822,551173,551173,551173,546898,546898,204567,533977,204567,551303,551303,204567,546898,546898,551303,546898,411958,551303,551303,312943,551303,546898,533977,551173,546898,533977,551303,558305,546898,558305,551173,533977,551303,546898,551303,533977,558305,533977,558822,480287,546898,551173,546898,533977,546608,411958,558305,533977,480287,546898,551303,411958,558305,546898,312943,533977,558305,551173,551303,533977,533977,546898,546898,533977,558305,546608,312943,546898,551303,411958,551303,548251,535902,535902) limit 10;,success
72da705365d1a9f3000000000000000d,2024-02-18T15:25:02+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,channel_account,success
72da705365d1a9f3000000000000000d,2024-02-18T15:25:10+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select * from deposit_history where user_id in (455283,455283,529135,529135,529135,285553,533977,533977,533977,551173,546898,546898,546608,546898,533977,551173,533977,546898,546608,533977,312943,411958,551303,551303,546898,372069,534775,534775,551303,533977,546898,534775,551303,546898,204567,312943,534775,389409,533977,551303,546898,546608,546608,372069,547335,535275,547335,535275,547335,547335,547335,547335,547335,547335,577075,577075,547335,547335,547335,547335,547335,547335,547335,547335,547335,535275,358034,547335,547335,535275,547335,547335,547335,358034,547335,547335,547335,547335,547335,547335,577075,547335,547335,577075,547335,577075,577075,339400,339400,577075,624850,339400,339400,339400,748686,577075,624850,339400,624850,551173,551173,551173,551173,551173,551173,551173,551173,551173,529135,533977,533977,529135,533977,285553,529135,533977,529135,533977,533977,533977,546898,546898,533977,533977,546898,533977,546898,529135,436944,549841,546608,533977,533977,450510,546898,546898,546898,533977,546898,469813,551303,546898,551303,546898,551173,308946,551303,546608,546898,551303,312943,551303,546898,546898,546898,469813,308946,551303,546898,551173,551173,546608,551303,469813,551173,308946,551173,551303,546898,480287,551303,551303,551303,533977,551303,551303,551173,551303,551173,546898,546898,533977,551303,551303,312943,533977,551303,480287,546898,551173,546898,551303,558305,558305,551303,558822,551173,551173,551173,546898,546898,204567,533977,204567,551303,551303,204567,546898,546898,551303,546898,411958,551303,551303,312943,551303,546898,533977,551173,546898,533977,551303,558305,546898,558305,551173,533977,551303,546898,551303,533977,558305,533977,558822,480287,546898,551173,546898,533977,546608,411958,558305,533977,480287,546898,551303,411958,558305,546898,312943,533977,558305,551173,551303,533977,533977,546898,546898,533977,558305,546608,312943,546898,551303,411958,551303,548251,535902,535902); limit 10,success
72da705365d1a9f3000000000000000d,2024-02-18T15:45:12+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,show create table deposit_history;,success
72da705365d1a9f3000000000000000d,2024-02-18T15:48:39+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,quit,success
72da705365d1a9f3000000000000000d,2024-02-18T15:48:43+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,cd /tmp,success
72da705365d1a9f3000000000000000d,2024-02-18T15:48:43+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,ll,success
72da705365d1a9f3000000000000000d,2024-02-18T15:48:56+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com huobi -e 'select user_id,channel_id,account,channel_account from my_channel where user_id in (455283,455283,529135,529135,529135,285553,533977,533977,533977,551173,546898,546898,546608,546898,533977,551173,533977,546898,546608,533977,312943,411958,551303,551303,546898,372069,534775,534775,551303,533977,546898,534775,551303,546898,204567,312943,534775,389409,533977,551303,546898,546608,546608,372069,547335,535275,547335,535275,547335,547335,547335,547335,547335,547335,577075,577075,547335,547335,547335,547335,547335,547335,547335,547335,547335,535275,358034,547335,547335,535275,547335,547335,547335,358034,547335,547335,547335,547335,547335,547335,577075,547335,547335,577075,547335,577075,577075,339400,339400,577075,624850,339400,339400,339400,748686,577075,624850,339400,624850,551173,551173,551173,551173,551173,551173,551173,551173,551173,529135,533977,533977,529135,533977,285553,529135,533977,529135,533977,533977,533977,546898,546898,533977,533977,546898,533977,546898,529135,436944,549841,546608,533977,533977,450510,546898,546898,546898,533977,546898,469813,551303,546898,551303,546898,551173,308946,551303,546608,546898,551303,312943,551303,546898,546898,546898,469813,308946,551303,546898,551173,551173,546608,551303,469813,551173,308946,551173,551303,546898,480287,551303,551303,551303,533977,551303,551303,551173,551303,551173,546898,546898,533977,551303,551303,312943,533977,551303,480287,546898,551173,546898,551303,558305,558305,551303,558822,551173,551173,551173,546898,546898,204567,533977,204567,551303,551303,204567,546898,546898,551303,546898,411958,551303,551303,312943,551303,546898,533977,551173,546898,533977,551303,558305,546898,558305,551173,533977,551303,546898,551303,533977,558305,533977,558822,480287,546898,551173,546898,533977,546608,411958,558305,533977,480287,546898,551303,411958,558305,546898,312943,533977,558305,551173,551303,533977,533977,546898,546898,533977,558305,546608,312943,546898,551303,411958,551303,548251,535902,535902);' > bank_info.tsv,success
72da705365d1a9f3000000000000000d,2024-02-18T15:49:03+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,ll,success
72da705365d1a9f3000000000000000d,2024-02-18T15:49:07+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,less bank_info.tsv,success
72da705365d1a9f3000000000000000d,2024-02-18T15:50:19+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,qmysql -u superdba -P 3614 -p -h master-3614.docsl.com huobi -e 'select * from deposit_history where user_id in (455283,455283,529135,529135,529135,285553,533977,533977,533977,551173,546898,546898,546608,546898,533977,551173,533977,546898,546608,533977,312943,411958,551303,551303,546898,372069,534775,534775,551303,533977,546898,534775,551303,546898,204567,312943,534775,389409,533977,551303,546898,546608,546608,372069,547335,535275,547335,535275,547335,547335,547335,547335,547335,547335,577075,577075,547335,547335,547335,547335,547335,547335,547335,547335,547335,535275,358034,547335,547335,535275,547335,547335,547335,358034,547335,547335,547335,547335,547335,547335,577075,547335,547335,577075,547335,577075,577075,339400,339400,577075,624850,339400,339400,339400,748686,577075,624850,339400,624850,551173,551173,551173,551173,551173,551173,551173,551173,551173,529135,533977,533977,529135,533977,285553,529135,533977,529135,533977,533977,533977,546898,546898,533977,533977,546898,533977,546898,529135,436944,549841,546608,533977,533977,450510,546898,546898,546898,533977,546898,469813,551303,546898,551303,546898,551173,308946,551303,546608,546898,551303,312943,551303,546898,546898,546898,469813,308946,551303,546898,551173,551173,546608,551303,469813,551173,308946,551173,551303,546898,480287,551303,551303,551303,533977,551303,551303,551173,551303,551173,546898,546898,533977,551303,551303,312943,533977,551303,480287,546898,551173,546898,551303,558305,558305,551303,558822,551173,551173,551173,546898,546898,204567,533977,204567,551303,551303,204567,546898,546898,551303,546898,411958,551303,551303,312943,551303,546898,533977,551173,546898,533977,551303,558305,546898,558305,551173,533977,551303,546898,551303,533977,558305,533977,558822,480287,546898,551173,546898,533977,546608,411958,558305,533977,480287,546898,551303,411958,558305,546898,312943,533977,558305,551173,551303,533977,533977,546898,546898,533977,558305,546608,312943,546898,551303,411958,551303,548251,535902,535902);' > deposit.tsv,success
72da705365d1a9f3000000000000000d,2024-02-18T15:50:43+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com huobi -e 'select * from withdraw_history where user_id in (455283,455283,529135,529135,529135,285553,533977,533977,533977,551173,546898,546898,546608,546898,533977,551173,533977,546898,546608,533977,312943,411958,551303,551303,546898,372069,534775,534775,551303,533977,546898,534775,551303,546898,204567,312943,534775,389409,533977,551303,546898,546608,546608,372069,547335,535275,547335,535275,547335,547335,547335,547335,547335,547335,577075,577075,547335,547335,547335,547335,547335,547335,547335,547335,547335,535275,358034,547335,547335,535275,547335,547335,547335,358034,547335,547335,547335,547335,547335,547335,577075,547335,547335,577075,547335,577075,577075,339400,339400,577075,624850,339400,339400,339400,748686,577075,624850,339400,624850,551173,551173,551173,551173,551173,551173,551173,551173,551173,529135,533977,533977,529135,533977,285553,529135,533977,529135,533977,533977,533977,546898,546898,533977,533977,546898,533977,546898,529135,436944,549841,546608,533977,533977,450510,546898,546898,546898,533977,546898,469813,551303,546898,551303,546898,551173,308946,551303,546608,546898,551303,312943,551303,546898,546898,546898,469813,308946,551303,546898,551173,551173,546608,551303,469813,551173,308946,551173,551303,546898,480287,551303,551303,551303,533977,551303,551303,551173,551303,551173,546898,546898,533977,551303,551303,312943,533977,551303,480287,546898,551173,546898,551303,558305,558305,551303,558822,551173,551173,551173,546898,546898,204567,533977,204567,551303,551303,204567,546898,546898,551303,546898,411958,551303,551303,312943,551303,546898,533977,551173,546898,533977,551303,558305,546898,558305,551173,533977,551303,546898,551303,533977,558305,533977,558822,480287,546898,551173,546898,533977,546608,411958,558305,533977,480287,546898,551303,411958,558305,546898,312943,533977,558305,551173,551303,533977,533977,546898,546898,533977,558305,546608,312943,546898,551303,411958,551303,548251,535902,535902);' > withdraw.tsv,success
72da705365d1a9f3000000000000000d,2024-02-18T15:50:49+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,ll,success
72da705365d1a9f3000000000000000d,2024-02-18T15:52:05+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,cat bank_info.tsv | sed 's/\t/,/g' > bank_info.csv,success
72da705365d1a9f3000000000000000d,2024-02-18T15:52:10+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,ll,success
72da705365d1a9f3000000000000000d,2024-02-18T15:52:29+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,cat deposit.tsv | sed 's/\t/,/g' > deposit.csv,success
72da705365d1a9f3000000000000000d,2024-02-18T15:52:46+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,cat withdraw.tsv | sed 's/\t/,/g' > withdraw.csv,success
72da705365d1a9f3000000000000000d,2024-02-18T15:52:47+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,ll,success
72da705365d1a9f3000000000000000d,2024-02-18T15:52:58+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,rm -f *.tsv,success
72da705365d1a9f3000000000000000d,2024-02-18T15:52:59+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,ll,success
72da705365d1a9f3000000000000000d,2024-02-18T15:53:50+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
72da705365d1a9f3000000000000000d,2024-02-18T15:54:08+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,%hbg%';,success
72da705365d1a9f3000000000000000d,2024-02-18T15:54:14+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,use hbg_trade,success
72da705365d1a9f3000000000000000d,2024-02-18T15:54:19+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,show tables;,success
72da705365d1a9f3000000000000000d,2024-02-18T15:57:24+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,show create table t_account_manage_fee_snapshot;,success
72da705365d1a9f3000000000000000d,2024-02-18T15:57:44+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,show create table t_account_manage_fee_transfer_detail;,success
72da705365d1a9f3000000000000000d,2024-02-18T16:10:55+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,quit,success
72da705365d1a9f3000000000000000d,2024-02-18T16:10:57+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,ll,success
72da705365d1a9f3000000000000000d,2024-02-18T16:11:09+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com hbg_trade -e 'select f_uid,f_balance from t_account_manage_fee_snapshot where f_user_id in (********,9566279,********,202932,1855634,********,611733,335494,2970431,186249,********,1955427,634758,4608889,3138151,1222328,********,4589975,2823785,1610272,919299,1541374,********,4713982,********,1734310,1643762,2529072,********,5477331,9358595,********,********,********,1428022,1546240,********,********,********,1474221,********,6148400,6148008,9284626,********,5144257,6202889,********,********,********,1234393,3860183,********,2221196,2817482,9702440,********,********,2043678,1460747,2204556,4382418,3520809,2963548,2864943,1555978,3888855,1232313,********,********,********,********,********,********,********,********,********,********,********,********,2657568,2970431,186249,********,1955427,634758,4608889,3138151,1222328,********,4589975,2823785,1610272,919299,1541374,********,********,2653284,1771187,********,29741,********,3825226,********,********,********,********,********,4332407,********,********,********,********,********,********,2043678,1460747,2204556,4382418,3520809,2963548,2864943,9284626,********,2657568,********,********,********,********,********,********,********,********,********,********,********,1546240,********,4713982,********,1734310,1643762,2529072,********,5477331,9358595,********,********,********,1428022,18384042,18384042,6020666,12773278,17677092,22591971,1855634,18979144,********,21316263,19807256,19899786,25166480,18987362,22950646,16701399,23526788,25134186,202932,********,20996370,9566279,********,1546240,9484323,15613267,585103,2653284,18759496,16428347,17104057,16638208,168771,2581180,2864943,2963548,4382418,3566032,4382418,2963548,2864943,2581180,********,********,********,********,********,********,********,********,2970431,186249,********,1955427,634758,4608889,3138151,1222328,********,4589975,2823785,1610272,919299,19760411,19760101,16677353,9284626,1541374,16799136,5687816,11693014,10303804,12620790,3566032,3566032,2930248,2043678,2622466,5306147,2581180,********,********,********,********,********,1460747,2204556,4382418,3520809,2963548,2864943,********,2874860,1754101,9055041,********,14343693,17261628,16967720,12782628,18694295,13211810,18384042,13369767,********,20996370,9566279,********,12773278,17677092,22591971,1855634,18979144,********,21316263,19807256,19899786,25166480,18987362,22950646,16701399,23526788,25134186,202932,2657568,1817375,1794238,5408494,11990025,3818180,4843916,5355439,585103,********,********,15613267,15952731,2377546,15575606,17885833,2970431,186249,********,1955427,634758,4608889,3138151,1222328,********,4589975,2823785,1610272,919299,442719,18384042,3986826,6406480,21106601,17168519,13155352,11561496,3969809,6338843,939177,600515,202442,10303804,12620790,********,1546240,11159106,1541374,9284626,19414397,********,2043678,2622466,5306147,2581180,********,********,********,********,********,1460747,2204556,4382418,3520809,2963548,2864943,********,********,********,********,********,1642948,2612633,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********);' > balance_snapshot.tsv,success
72da705365d1a9f3000000000000000d,2024-02-18T16:11:17+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,ll,success
72da705365d1a9f3000000000000000d,2024-02-18T16:11:49+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,cat balance_snapshot.tsv | sed 's/\t/,/g' > balance_snapshot.csv,success
72da705365d1a9f3000000000000000d,2024-02-18T16:11:51+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,ll,success
72da705365d1a9f3000000000000000d,2024-02-18T16:12:00+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,rm balance_snapshot.tsv,success
72da705365d1a9f3000000000000000d,2024-02-18T16:12:01+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,y,success
72da705365d1a9f3000000000000000d,2024-02-18T16:12:05+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,less bal,success
72da705365d1a9f3000000000000000d,2024-02-18T16:13:42+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,qmysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
72da705365d1a9f3000000000000000d,2024-02-18T16:13:52+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,_trade,success
72da705365d1a9f3000000000000000d,2024-02-18T16:14:11+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select f_uid,f_currency,f_balance from t_account_manage_fee_snapshot where f_user_id in (********,9566279,********,202932,1855634,********,611733,335494,2970431,186249,********,1955427,634758,4608889,3138151,1222328,********,4589975,2823785,1610272,919299,1541374,********,4713982,********,1734310,1643762,2529072,********,5477331,9358595,********,********,********,1428022,1546240,********,********,********,1474221,********,6148400,6148008,9284626,********,5144257,6202889,********,********,********,1234393,3860183,********,2221196,2817482,9702440,********,********,2043678,1460747,2204556,4382418,3520809,2963548,2864943,1555978,3888855,1232313,********,********,********,********,********,********,********,********,********,********,********,********,2657568,2970431,186249,********,1955427,634758,4608889,3138151,1222328,********,4589975,2823785,1610272,919299,1541374,********,********,2653284,1771187,********,29741,********,3825226,********,********,********,********,********,4332407,********,********,********,********,********,********,2043678,1460747,2204556,4382418,3520809,2963548,2864943,9284626,********,2657568,********,********,********,********,********,********,********,********,********,********,********,1546240,********,4713982,********,1734310,1643762,2529072,********,5477331,9358595,********,********,********,1428022,18384042,18384042,6020666,12773278,17677092,22591971,1855634,18979144,********,21316263,19807256,19899786,25166480,18987362,22950646,16701399,23526788,25134186,202932,********,20996370,9566279,********,1546240,9484323,15613267,585103,2653284,18759496,16428347,17104057,16638208,168771,2581180,2864943,2963548,4382418,3566032,4382418,2963548,2864943,2581180,********,********,********,********,********,********,********,********,2970431,186249,********,1955427,634758,4608889,3138151,1222328,********,4589975,2823785,1610272,919299,19760411,19760101,16677353,9284626,1541374,16799136,5687816,11693014,10303804,12620790,3566032,3566032,2930248,2043678,2622466,5306147,2581180,********,********,********,********,********,1460747,2204556,4382418,3520809,2963548,2864943,********,2874860,1754101,9055041,********,14343693,17261628,16967720,12782628,18694295,13211810,18384042,13369767,********,20996370,9566279,********,12773278,17677092,22591971,1855634,18979144,********,21316263,19807256,19899786,25166480,18987362,22950646,16701399,23526788,25134186,202932,2657568,1817375,1794238,5408494,11990025,3818180,4843916,5355439,585103,********,********,15613267,15952731,2377546,15575606,17885833,2970431,186249,********,1955427,634758,4608889,3138151,1222328,********,4589975,2823785,1610272,919299,442719,18384042,3986826,6406480,21106601,17168519,13155352,11561496,3969809,6338843,939177,600515,202442,10303804,12620790,********,1546240,11159106,1541374,9284626,19414397,********,2043678,2622466,5306147,2581180,********,********,********,********,********,1460747,2204556,4382418,3520809,2963548,2864943,********,********,********,********,********,1642948,2612633,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********) limit 10;,success
72da705365d1a9f3000000000000000d,2024-02-18T16:14:42+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,t *,success
72da705365d1a9f3000000000000000d,2024-02-18T16:26:05+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,quit,success
72da705365d1a9f3000000000000000d,2024-02-18T16:26:07+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,ll,success
72da705365d1a9f3000000000000000d,2024-02-18T16:27:37+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,wc -l balance_snapshot.csv,success
72da705365d1a9f3000000000000000d,2024-02-18T16:29:55+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,ossutil cp bank_info.csv  oss://miniglobal-devops/hue_log/,success
72da705365d1a9f3000000000000000d,2024-02-18T16:30:12+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,ossutil cp deposit.csv  oss://miniglobal-devops/hue_log/,success
72da705365d1a9f3000000000000000d,2024-02-18T16:30:18+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,ossutil cp withdraw.csv  oss://miniglobal-devops/hue_log/,success
72da705365d1a9f3000000000000000d,2024-02-18T16:35:59+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,ll,success
72da705365d1a9f3000000000000000d,2024-02-18T16:36:13+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,rm -f bank_info.csv deposit.csv withdraw.csv,success
72da705365d1a9f3000000000000000d,2024-02-18T16:36:15+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,ll,success
72da705365d1a9f3000000000000000d,2024-02-18T16:36:24+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,vim balance_snapshot.csv,success
72da705365d1a9f3000000000000000d,2024-02-18T16:36:37+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,:q,success
72da705365d1a9f3000000000000000d,2024-02-18T16:36:48+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
72da705365d1a9f3000000000000000d,2024-02-18T16:37:03+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,use hbg_trade,success
72da705365d1a9f3000000000000000d,2024-02-18T16:37:07+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,show tables;,success
72da705365d1a9f3000000000000000d,2024-02-18T16:39:14+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select f_uid,count(distinc f_currency) from t_account_manage_fee_snapshot where f_user_id in (********,9566279,********,202932,1855634,********,611733,335494,2970431,186249,********,1955427,634758,4608889,3138151,1222328,********,4589975,2823785,1610272,919299,1541374,********,4713982,********,1734310,1643762,2529072,********,5477331,9358595,********,********,********,1428022,1546240,********,********,********,1474221,********,6148400,6148008,9284626,********,5144257,6202889,********,********,********,1234393,3860183,********,2221196,2817482,9702440,********,********,2043678,1460747,2204556,4382418,3520809,2963548,2864943,1555978,3888855,1232313,********,********,********,********,********,********,********,********,********,********,********,********,2657568,2970431,186249,********,1955427,634758,4608889,3138151,1222328,********,4589975,2823785,1610272,919299,1541374,********,********,2653284,1771187,********,29741,********,3825226,********,********,********,********,********,4332407,********,********,********,********,********,********,2043678,1460747,2204556,4382418,3520809,2963548,2864943,9284626,********,2657568,********,********,********,********,********,********,********,********,********,********,********,1546240,********,4713982,********,1734310,1643762,2529072,********,5477331,9358595,********,********,********,1428022,18384042,18384042,6020666,12773278,17677092,22591971,1855634,18979144,********,21316263,19807256,19899786,25166480,18987362,22950646,16701399,23526788,25134186,202932,********,20996370,9566279,********,1546240,9484323,15613267,585103,2653284,18759496,16428347,17104057,16638208,168771,2581180,2864943,2963548,4382418,3566032,4382418,2963548,2864943,2581180,********,********,********,********,********,********,********,********,2970431,186249,********,1955427,634758,4608889,3138151,1222328,********,4589975,2823785,1610272,919299,19760411,19760101,16677353,9284626,1541374,16799136,5687816,11693014,10303804,12620790,3566032,3566032,2930248,2043678,2622466,5306147,2581180,********,********,********,********,********,1460747,2204556,4382418,3520809,2963548,2864943,********,2874860,1754101,9055041,********,14343693,17261628,16967720,12782628,18694295,13211810,18384042,13369767,********,20996370,9566279,********,12773278,17677092,22591971,1855634,18979144,********,21316263,19807256,19899786,25166480,18987362,22950646,16701399,23526788,25134186,202932,2657568,1817375,1794238,5408494,11990025,3818180,4843916,5355439,585103,********,********,15613267,15952731,2377546,15575606,17885833,2970431,186249,********,1955427,634758,4608889,3138151,1222328,********,4589975,2823785,1610272,919299,442719,18384042,3986826,6406480,21106601,17168519,13155352,11561496,3969809,6338843,939177,600515,202442,10303804,12620790,********,1546240,11159106,1541374,9284626,19414397,********,2043678,2622466,5306147,2581180,********,********,********,********,********,1460747,2204556,4382418,3520809,2963548,2864943,********,********,********,********,********,1642948,2612633,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********) group by f_uid;,success
72da705365d1a9f3000000000000000d,2024-02-18T16:40:00+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select f_uid,count(f_currency) from t_account_manage_fee_snapshot where f_user_id in (********,9566279,********,202932,1855634,********,611733,335494,2970431,186249,********,1955427,634758,4608889,3138151,1222328,********,4589975,2823785,1610272,919299,1541374,********,4713982,********,1734310,1643762,2529072,********,5477331,9358595,********,********,********,1428022,1546240,********,********,********,1474221,********,6148400,6148008,9284626,********,5144257,6202889,********,********,********,1234393,3860183,********,2221196,2817482,9702440,********,********,2043678,1460747,2204556,4382418,3520809,2963548,2864943,1555978,3888855,1232313,********,********,********,********,********,********,********,********,********,********,********,********,2657568,2970431,186249,********,1955427,634758,4608889,3138151,1222328,********,4589975,2823785,1610272,919299,1541374,********,********,2653284,1771187,********,29741,********,3825226,********,********,********,********,********,4332407,********,********,********,********,********,********,2043678,1460747,2204556,4382418,3520809,2963548,2864943,9284626,********,2657568,********,********,********,********,********,********,********,********,********,********,********,1546240,********,4713982,********,1734310,1643762,2529072,********,5477331,9358595,********,********,********,1428022,18384042,18384042,6020666,12773278,17677092,22591971,1855634,18979144,********,21316263,19807256,19899786,25166480,18987362,22950646,16701399,23526788,25134186,202932,********,20996370,9566279,********,1546240,9484323,15613267,585103,2653284,18759496,16428347,17104057,16638208,168771,2581180,2864943,2963548,4382418,3566032,4382418,2963548,2864943,2581180,********,********,********,********,********,********,********,********,2970431,186249,********,1955427,634758,4608889,3138151,1222328,********,4589975,2823785,1610272,919299,19760411,19760101,16677353,9284626,1541374,16799136,5687816,11693014,10303804,12620790,3566032,3566032,2930248,2043678,2622466,5306147,2581180,********,********,********,********,********,1460747,2204556,4382418,3520809,2963548,2864943,********,2874860,1754101,9055041,********,14343693,17261628,16967720,12782628,18694295,13211810,18384042,13369767,********,20996370,9566279,********,12773278,17677092,22591971,1855634,18979144,********,21316263,19807256,19899786,25166480,18987362,22950646,16701399,23526788,25134186,202932,2657568,1817375,1794238,5408494,11990025,3818180,4843916,5355439,585103,********,********,15613267,15952731,2377546,15575606,17885833,2970431,186249,********,1955427,634758,4608889,3138151,1222328,********,4589975,2823785,1610272,919299,442719,18384042,3986826,6406480,21106601,17168519,13155352,11561496,3969809,6338843,939177,600515,202442,10303804,12620790,********,1546240,11159106,1541374,9284626,19414397,********,2043678,2622466,5306147,2581180,********,********,********,********,********,1460747,2204556,4382418,3520809,2963548,2864943,********,********,********,********,********,1642948,2612633,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********) group by f_uid;,success
72da705365d1a9f3000000000000000d,2024-02-18T16:41:10+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,select * from t_account_manage_fee_snapshot where f_uid = ******** limit 10;,success
72da705365d1a9f3000000000000000d,2024-02-18T16:48:33+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,quit,success
72da705365d1a9f3000000000000000d,2024-02-18T16:48:34+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,ll,success
72da705365d1a9f3000000000000000d,2024-02-18T16:48:41+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,rm balance_snapshot.csv,success
72da705365d1a9f3000000000000000d,2024-02-18T16:48:43+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,y,success
72da705365d1a9f3000000000000000d,2024-02-18T16:48:49+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com hbg_trade -e 'select f_uid,f_currency,f_balance from t_account_manage_fee_snapshot where f_user_id in (********,9566279,********,202932,1855634,********,611733,335494,2970431,186249,********,1955427,634758,4608889,3138151,1222328,********,4589975,2823785,1610272,919299,1541374,********,4713982,********,1734310,1643762,2529072,********,5477331,9358595,********,********,********,1428022,1546240,********,********,********,1474221,********,6148400,6148008,9284626,********,5144257,6202889,********,********,********,1234393,3860183,********,2221196,2817482,9702440,********,********,2043678,1460747,2204556,4382418,3520809,2963548,2864943,1555978,3888855,1232313,********,********,********,********,********,********,********,********,********,********,********,********,2657568,2970431,186249,********,1955427,634758,4608889,3138151,1222328,********,4589975,2823785,1610272,919299,1541374,********,********,2653284,1771187,********,29741,********,3825226,********,********,********,********,********,4332407,********,********,********,********,********,********,2043678,1460747,2204556,4382418,3520809,2963548,2864943,9284626,********,2657568,********,********,********,********,********,********,********,********,********,********,********,1546240,********,4713982,********,1734310,1643762,2529072,********,5477331,9358595,********,********,********,1428022,18384042,18384042,6020666,12773278,17677092,22591971,1855634,18979144,********,21316263,19807256,19899786,25166480,18987362,22950646,16701399,23526788,25134186,202932,********,20996370,9566279,********,1546240,9484323,15613267,585103,2653284,18759496,16428347,17104057,16638208,168771,2581180,2864943,2963548,4382418,3566032,4382418,2963548,2864943,2581180,********,********,********,********,********,********,********,********,2970431,186249,********,1955427,634758,4608889,3138151,1222328,********,4589975,2823785,1610272,919299,19760411,19760101,16677353,9284626,1541374,16799136,5687816,11693014,10303804,12620790,3566032,3566032,2930248,2043678,2622466,5306147,2581180,********,********,********,********,********,1460747,2204556,4382418,3520809,2963548,2864943,********,2874860,1754101,9055041,********,14343693,17261628,16967720,12782628,18694295,13211810,18384042,13369767,********,20996370,9566279,********,12773278,17677092,22591971,1855634,18979144,********,21316263,19807256,19899786,25166480,18987362,22950646,16701399,23526788,25134186,202932,2657568,1817375,1794238,5408494,11990025,3818180,4843916,5355439,585103,********,********,15613267,15952731,2377546,15575606,17885833,2970431,186249,********,1955427,634758,4608889,3138151,1222328,********,4589975,2823785,1610272,919299,442719,18384042,3986826,6406480,21106601,17168519,13155352,11561496,3969809,6338843,939177,600515,202442,10303804,12620790,********,1546240,11159106,1541374,9284626,19414397,********,2043678,2622466,5306147,2581180,********,********,********,********,********,1460747,2204556,4382418,3520809,2963548,2864943,********,********,********,********,********,1642948,2612633,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********);' > balance_snapshot.tsv,success
72da705365d1a9f3000000000000000d,2024-02-18T16:48:58+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,ll,success
72da705365d1a9f3000000000000000d,2024-02-18T16:49:02+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,less balance_snapshot.tsv,success
72da705365d1a9f3000000000000000d,2024-02-18T16:49:27+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,cat balance_snapshot.tsv | sed 's/\t/,/g' > balance_snapshot.csv,success
72da705365d1a9f3000000000000000d,2024-02-18T16:49:43+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,ossutil cp balance_snapshot.csv  oss://miniglobal-devops/hue_log/,success
72da705365d1a9f3000000000000000d,2024-02-18T16:50:44+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,rm balance_snapshot.*,success
72da705365d1a9f3000000000000000d,2024-02-18T16:50:45+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,y,success
72da705365d1a9f3000000000000000d,2024-02-18T16:50:46+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,y,success
633207e265d1c4f4000000000000000d,2024-02-18T16:51:11+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,cd /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-avenir/,success
633207e265d1c4f4000000000000000d,2024-02-18T16:51:14+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,make build TAG=PRD_T402181,success
633207e265d1c4f4000000000000000d,2024-02-18T16:51:52+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,ll /build/code/_build.sh,success
633207e265d1c4f4000000000000000d,2024-02-18T16:53:05+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,ll /usr/local/bin/docker-entrypoint.sh,success
633207e265d1c4f4000000000000000d,2024-02-18T16:53:23+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,ll,success
633207e265d1c4f4000000000000000d,2024-02-18T16:54:42+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,vim b.,success
633207e265d1c4f4000000000000000d,2024-02-18T17:24:44+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,:q,success
633207e265d1c4f4000000000000000d,2024-02-18T17:24:50+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,cat Makefile,success
633207e265d1c4f4000000000000000d,2024-02-18T17:25:27+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,cat build.sh,success
633207e265d1c4f4000000000000000d,2024-02-18T17:26:59+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,ll,success
633207e265d1c4f4000000000000000d,2024-02-18T17:27:05+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,ll app/,success
633207e265d1c4f4000000000000000d,2024-02-18T17:27:11+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,ll app/bin/,success
633207e265d1c4f4000000000000000d,2024-02-18T17:27:35+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,cat app/bin/activate,success
633207e265d1c4f4000000000000000d,2024-02-18T17:28:05+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,vim,success
633207e265d1c4f4000000000000000d,2024-02-18T17:28:31+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,:wq,success
633207e265d1c4f4000000000000000d,2024-02-18T17:28:41+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,cat app/bin/install_deps,success
9027ae1765d1ce42000000000000000d,2024-02-18T17:30:45+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,cd /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-avenir/,success
9027ae1765d1ce42000000000000000d,2024-02-18T17:30:47+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,ll,success
9027ae1765d1ce42000000000000000d,2024-02-18T17:30:54+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,make build TAG=PRD_T402181,success
9027ae1765d1ce42000000000000000d,2024-02-18T17:32:45+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,grep -Ril "Docker 构建包" .,success
9027ae1765d1ce42000000000000000d,2024-02-18T17:32:56+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,cat build_command_pkgs/__build.sh,success
9027ae1765d1ce42000000000000000d,2024-02-18T17:42:09+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,ll,success
9027ae1765d1ce42000000000000000d,2024-02-18T17:42:18+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,cd build_command_pkgs/,success
9027ae1765d1ce42000000000000000d,2024-02-18T17:42:19+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,ll,success
9027ae1765d1ce42000000000000000d,2024-02-18T17:42:46+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,less fed-cn-web__build.sh,success
9027ae1765d1ce42000000000000000d,2024-02-18T17:57:35+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,ll m2/,success
9027ae1765d1ce42000000000000000d,2024-02-18T17:58:16+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,hbbu,success
9027ae1765d1ce42000000000000000d,2024-02-18T17:58:17+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,ll,success
9027ae1765d1ce42000000000000000d,2024-02-18T17:58:22+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,ll m2/,success
9027ae1765d1ce42000000000000000d,2024-02-18T17:58:40+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,cat fed-cn-web__build.sh,success
9027ae1765d1ce42000000000000000d,2024-02-18T17:58:49+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,cd -,success
9027ae1765d1ce42000000000000000d,2024-02-18T17:58:52+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,ll,success
9027ae1765d1ce42000000000000000d,2024-02-18T17:59:38+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,less fed-avenir__build.sh/,success
9027ae1765d1ce42000000000000000d,2024-02-18T17:59:44+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,ll fed-avenir__build.sh/,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:00:23+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,rmdir fed-avenir__build.sh/,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:01:03+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,mv fed-cn-web__build.sh fed-avenir__build.sh,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:01:05+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,ll,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:01:08+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,cd ..,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:01:27+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,make build TAG=PRD_T402181,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:03:19+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,ll,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:03:23+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,cd build_command_pkgs/,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:03:24+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,ll,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:03:29+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,cat fed-avenir__build.sh,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:05:48+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,mkdir fed-avenir__build.sh,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:06:32+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,cd ../../fed-hb,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:06:33+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,ll,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:06:42+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,cd build_command_pkgs/,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:06:42+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,ll,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:07:17+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,cd ..,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:07:27+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,grep -Ril "fed-cn-web" .,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:08:35+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,vim build.sh,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:09:14+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,:q,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:09:21+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,vim build_command_pkgs/__build.sh,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:09:36+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,/PKG_NAME,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:10:11+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,:q,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:10:17+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,cd -,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:10:22+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,cd -,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:10:26+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,cd ../fed-avenir/,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:10:27+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,ll,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:10:30+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,vim build.sh,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:11:29+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,:wq,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:11:41+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,cd build_command_pkgs/,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:11:42+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,ll,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:11:51+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,cd ..,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:12:02+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,make build TAG=PRD_T402181,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:12:24+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,cd build_command_pkgs/,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:12:25+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,ll,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:13:37+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,vim __build.sh,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:21:05+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,:q,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:21:20+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,cd ..,success
9027ae1765d1ce42000000000000000d,2024-02-18T18:21:27+08:00,cmd.Command,avenir_user01,**************,miniglobal-deploy,***********,docker run --rm -e GIT_VERSION=PRD_T402181 -w /build/code -v /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-avenir/build_command_pkgs/m2:/build/.m2 -v /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-avenir/build_command_pkgs/fed-avenir:/build/code -v /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-avenir/build_command_pkgs/fed-avenir-web__build.sh:/build/code/_build.sh miniglobal-harbor.cogdocs.com/builder/node:20.11.0 /build/code/_build.sh,success
8ab21cbe65d1e0c9000000000000000d,2024-02-18T18:49:56+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
8ab21cbe65d1e0c9000000000000000d,2024-02-18T18:50:12+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,use hbg_trade,success
8ab21cbe65d1e0c9000000000000000d,2024-02-18T18:50:17+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,show tables;,success
8ab21cbe65d1e0c9000000000000000d,2024-02-18T18:50:32+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,show create table t_account_manage_fee_snapshot;,success
8ab21cbe65d1e0c9000000000000000d,2024-02-18T18:50:59+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,quit,success
8ab21cbe65d1e0c9000000000000000d,2024-02-18T18:51:03+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,cd /tmp,success
8ab21cbe65d1e0c9000000000000000d,2024-02-18T18:51:04+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,ll,success
8ab21cbe65d1e0c9000000000000000d,2024-02-18T18:52:52+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com hbg_trade -e 'select * from t_account_manage_fee_snapshot where f_user_id in (********,9566279,********,202932,1855634,********,611733,335494,2970431,186249,********,1955427,634758,4608889,3138151,1222328,********,4589975,2823785,1610272,919299,1541374,********,4713982,********,1734310,1643762,2529072,********,5477331,9358595,********,********,********,1428022,1546240,********,********,********,1474221,********,6148400,6148008,9284626,********,5144257,6202889,********,********,********,1234393,3860183,********,2221196,2817482,9702440,********,********,2043678,1460747,2204556,4382418,3520809,2963548,2864943,1555978,3888855,1232313,********,********,********,********,********,********,********,********,********,********,********,********,2657568,2970431,186249,********,1955427,634758,4608889,3138151,1222328,********,4589975,2823785,1610272,919299,1541374,********,********,2653284,1771187,********,29741,********,3825226,********,********,********,********,********,4332407,********,********,********,********,********,********,2043678,1460747,2204556,4382418,3520809,2963548,2864943,9284626,********,2657568,********,********,********,********,********,********,********,********,********,********,********,1546240,********,4713982,********,1734310,1643762,2529072,********,5477331,9358595,********,********,********,1428022,18384042,18384042,6020666,12773278,17677092,22591971,1855634,18979144,********,21316263,19807256,19899786,25166480,18987362,22950646,16701399,23526788,25134186,202932,********,20996370,9566279,********,1546240,9484323,15613267,585103,2653284,18759496,16428347,17104057,16638208,168771,2581180,2864943,2963548,4382418,3566032,4382418,2963548,2864943,2581180,********,********,********,********,********,********,********,********,2970431,186249,********,1955427,634758,4608889,3138151,1222328,********,4589975,2823785,1610272,919299,19760411,19760101,16677353,9284626,1541374,16799136,5687816,11693014,10303804,12620790,3566032,3566032,2930248,2043678,2622466,5306147,2581180,********,********,********,********,********,1460747,2204556,4382418,3520809,2963548,2864943,********,2874860,1754101,9055041,********,14343693,17261628,16967720,12782628,18694295,13211810,18384042,13369767,********,20996370,9566279,********,12773278,17677092,22591971,1855634,18979144,********,21316263,19807256,19899786,25166480,18987362,22950646,16701399,23526788,25134186,202932,2657568,1817375,1794238,5408494,11990025,3818180,4843916,5355439,585103,********,********,15613267,15952731,2377546,15575606,17885833,2970431,186249,********,1955427,634758,4608889,3138151,1222328,********,4589975,2823785,1610272,919299,442719,18384042,3986826,6406480,21106601,17168519,13155352,11561496,3969809,6338843,939177,600515,202442,10303804,12620790,********,1546240,11159106,1541374,9284626,19414397,********,2043678,2622466,5306147,2581180,********,********,********,********,********,1460747,2204556,4382418,3520809,2963548,2864943,********,********,********,********,********,1642948,2612633,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********,********);' > balance_snapshot_all_column.tsv,success
8ab21cbe65d1e0c9000000000000000d,2024-02-18T18:53:27+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,c,success
8ab21cbe65d1e0c9000000000000000d,2024-02-18T18:53:52+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,ossutil cp balance_snapshot_all_column.csv  oss://miniglobal-devops/hue_log/,success
8ab21cbe65d1e0c9000000000000000d,2024-02-18T19:11:58+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,ll,success
8ab21cbe65d1e0c9000000000000000d,2024-02-18T19:12:11+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,rm balance_snapshot_all_column.* -f,success
8ab21cbe65d1e0c9000000000000000d,2024-02-18T19:12:12+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,ll,success
9ec4d45165d2b31b0000000000000019,2024-02-19T09:47:14+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
9ec4d45165d2b31b0000000000000019,2024-02-19T09:47:26+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,use proamsdwdb;,success
9ec4d45165d2b31b0000000000000019,2024-02-19T09:47:35+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
9ec4d45165d2b31b0000000000000019,2024-02-19T09:47:35+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where f_state in(3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
9ec4d45165d2b31b0000000000000019,2024-02-19T09:47:35+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,group by f_currency;,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:09:48+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cd /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-avenir/,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:09:49+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,ll,success
9ec4d45165d2b31b0000000000000019,2024-02-19T10:12:35+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,use hbg_trade; ,success
9ec4d45165d2b31b0000000000000019,2024-02-19T10:12:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_state, count(*) as user_count,success
9ec4d45165d2b31b0000000000000019,2024-02-19T10:12:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from t_account_manage_fee_snapshot  ,success
9ec4d45165d2b31b0000000000000019,2024-02-19T10:12:55+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where f_day in('********'),success
9ec4d45165d2b31b0000000000000019,2024-02-19T10:12:56+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,group by f_state;,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:16:26+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,docker login miniglobal-harbor.cogdocs.com,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:16:48+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,make buTAG=PRD_T402181,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:17:43+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,ll,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:17:49+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cat Dockerfile,success
9ec4d45165d2b31b0000000000000019,2024-02-19T10:19:43+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_state, count(*) as user_count from t_account_manage_fee_snapshot   where f_day in('********') group by f_state;,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:20:58+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cat Makefile,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:23:46+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cat build.sh,success
9ec4d45165d2b31b0000000000000019,2024-02-19T10:24:13+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,exit,success
9ec4d45165d2b31b0000000000000019,2024-02-19T10:24:15+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,exit,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:27:14+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cd app,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:27:17+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,ll,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:27:25+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,ll bin/,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:27:47+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,ll lib/fed-cn-web/,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:28:10+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cd lib/,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:28:11+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,ll,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:28:25+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,mv fed-cn-web/ fed-avenir-web/,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:28:26+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,ll,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:28:28+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cd ..,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:28:35+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,ll lib/fed-avenir-web/,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:28:51+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,less lib/fed-avenir-web/nginx/nginx.conf,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:29:24+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,vim lib/fed-avenir-web/nginx/nginx.conf,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:30:25+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,:wq,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:31:49+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cd ..,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:31:50+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,ll,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:32:01+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,less README.md,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:34:20+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,vim build.sh,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:35:49+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,:w,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:36:33+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,:q,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:36:36+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cd build_command_pkgs/,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:36:37+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,ll,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:36:48+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,vim __build.sh,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:37:26+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,:q,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:37:35+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cd ..,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:37:36+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,ll,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:37:41+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,rm -r app/,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:37:43+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,ll,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:37:54+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,make build TAG=PRD_T402181,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:38:39+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cp -r ../fed-hbg-encn/app/ .,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:38:41+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,ll,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:38:46+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cd app/,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:38:47+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,ll,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:38:50+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cd bin/,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:38:50+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,ll,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:38:54+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,vim activate,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:39:19+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,:wq,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:39:24+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,vim install_deps,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:39:29+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,:q,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:39:31+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cd ..,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:39:32+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,ll,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:39:41+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cd lib/,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:39:42+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,ll,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:39:50+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,mv fed-cn-web/ fed-avenir/,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:39:51+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,ll,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:39:54+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cd fed-avenir/,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:39:55+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,ll,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:39:59+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cd nginx/,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:40:00+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,ll,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:40:04+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,vim nginx.conf,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:40:34+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,:wq,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:40:40+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cd ../i18n/,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:40:41+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,ll,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:40:48+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,vim index.html,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:40:57+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,/fed-,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:41:01+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,:q,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:41:04+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cd ../..,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:41:07+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cd ../..,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:41:08+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,ll,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:41:14+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cd build_command_pkgs/,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:41:17+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,vim __build.sh,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:43:19+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,:q,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:43:22+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cd ..,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:43:29+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,make build TAG=PRD_T402181,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:49:14+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,ll,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:49:18+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,ll build_command_pkgs/,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:56:16+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cd ../fed-hbg-encn/build_command_pkgs/,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:56:17+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,ll,success
9ca46a6765d2b85b000000000000000d,2024-02-19T10:56:57+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cat fed-cn-web__build.sh,success
9ca46a6765d2b85b000000000000000d,2024-02-19T11:17:14+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,docker image ls,success
9ca46a6765d2b85b000000000000000d,2024-02-19T11:35:44+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cd ..,success
9ca46a6765d2b85b000000000000000d,2024-02-19T11:36:03+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cd ../fed-avenir/,success
9ca46a6765d2b85b000000000000000d,2024-02-19T11:36:04+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,ll,success
9ca46a6765d2b85b000000000000000d,2024-02-19T11:36:10+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cat Makefile,success
9ca46a6765d2b85b000000000000000d,2024-02-19T11:36:57+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cat Dockerfile,success
9ca46a6765d2b85b000000000000000d,2024-02-19T11:37:23+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cat ../fed-hbg-encn/Dockerfile,success
9ca46a6765d2b85b000000000000000d,2024-02-19T11:37:42+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,echo $ENVIRONMENT_TEMPLATE,success
9ca46a6765d2b85b000000000000000d,2024-02-19T11:37:46+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,set,success
9ca46a6765d2b85b000000000000000d,2024-02-19T11:38:36+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cat Dockerfile,success
9ca46a6765d2b85b000000000000000d,2024-02-19T11:39:27+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,grep -Ril "ENVIRONMENT_TEMPLATE" .,success
9ca46a6765d2b85b000000000000000d,2024-02-19T11:40:01+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cat app/bin/activate,success
9ca46a6765d2b85b000000000000000d,2024-02-19T11:52:20+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,make build TAG=PRD_T402181,success
9ca46a6765d2b85b000000000000000d,2024-02-19T11:59:04+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cd ../fed-hbg-encn/build_command_pkgs/,success
9ca46a6765d2b85b000000000000000d,2024-02-19T11:59:08+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,vim __build.sh,success
9ca46a6765d2b85b000000000000000d,2024-02-19T12:00:29+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,:q,success
9ca46a6765d2b85b000000000000000d,2024-02-19T12:00:49+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,diff __build.sh ../../fed-avenir/build_command_pkgs/__build.sh,success
9ca46a6765d2b85b000000000000000d,2024-02-19T12:01:03+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cd ..,success
9ca46a6765d2b85b000000000000000d,2024-02-19T12:01:16+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,diff build.sh ../fed-avenir/build.sh,success
9ca46a6765d2b85b000000000000000d,2024-02-19T12:02:28+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,cd ../fed-avenir/,success
9ca46a6765d2b85b000000000000000d,2024-02-19T12:02:34+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,vim build.sh,success
9ca46a6765d2b85b000000000000000d,2024-02-19T12:02:47+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,:wq,success
9ca46a6765d2b85b000000000000000d,2024-02-19T12:02:59+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,make build TAG=PRD_T402181,success
9ca46a6765d2b85b000000000000000d,2024-02-19T12:12:13+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,vim build.sh,success
9ca46a6765d2b85b000000000000000d,2024-02-19T12:12:31+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,:wq,success
9ca46a6765d2b85b000000000000000d,2024-02-19T12:12:48+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,make build TAG=PRD_T402181,success
9ca46a6765d2b85b000000000000000d,2024-02-19T12:13:32+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,vim build.sh,success
9ca46a6765d2b85b000000000000000d,2024-02-19T12:13:46+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,:wq,success
9ca46a6765d2b85b000000000000000d,2024-02-19T12:13:49+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,make build TAG=PRD_T402181,success
c0c00d4365d2d5b3000000000000000d,2024-02-19T12:14:45+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
c0c00d4365d2d5b3000000000000000d,2024-02-19T12:14:59+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-devops,***********,use hbgorderdb,success
c0c00d4365d2d5b3000000000000000d,2024-02-19T12:15:14+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-devops,***********,show create table t_point_buy_back;,success
9ca46a6765d2b85b000000000000000d,2024-02-19T12:22:15+08:00,cmd.Command,avenir_user01,223.104.40.81,miniglobal-deploy,***********,docker images,success
d8bce46765d40eef000000000000000d,2024-02-20T10:31:24+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,cd /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-avenir/,success
d8bce46765d40eef000000000000000d,2024-02-20T10:31:30+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,make build TAG=PRD_T402181,success
d8bce46765d40eef000000000000000d,2024-02-20T10:33:56+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,docker images,success
d8bce46765d40eef000000000000000d,2024-02-20T10:34:25+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,docker exec -it 683cfa47c0ac /bin/bash,success
d8bce46765d40eef000000000000000d,2024-02-20T10:37:42+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,docker ps,success
d8bce46765d40eef000000000000000d,2024-02-20T10:37:51+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ll,success
d8bce46765d40eef000000000000000d,2024-02-20T10:37:54+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,cat Dockerfile,success
d8bce46765d40eef000000000000000d,2024-02-20T10:42:21+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,cat ../fed-hbg-encn/Dockerfile,success
d8bce46765d40eef000000000000000d,2024-02-20T10:43:10+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,cat apbiac,success
d8bce46765d40eef000000000000000d,2024-02-20T10:43:15+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,in,success
d8bce46765d40eef000000000000000d,2024-02-20T10:44:21+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,cat applfnn,success
d8bce46765d40eef000000000000000d,2024-02-20T10:48:21+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,cd ../fed-hb,success
d8bce46765d40eef000000000000000d,2024-02-20T10:48:25+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ll,success
d8bce46765d40eef000000000000000d,2024-02-20T10:48:31+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ll build_command_pkgs/,success
d8bce46765d40eef000000000000000d,2024-02-20T10:50:34+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,make build TAG=PRD_T402051 > build_log_20240220.txt,success
d8bce46765d40eef000000000000000d,2024-02-20T10:58:26+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ll,success
d8bce46765d40eef000000000000000d,2024-02-20T10:58:32+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,less build_log_20240220.txt,success
d8bce46765d40eef000000000000000d,2024-02-20T11:28:30+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,cd ../fed-avenir/,success
d8bce46765d40eef000000000000000d,2024-02-20T11:28:31+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ll,success
d8bce46765d40eef000000000000000d,2024-02-20T11:35:57+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,cd /hbdata/soft/miniglobal-apps/,success
d8bce46765d40eef000000000000000d,2024-02-20T11:35:58+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ll,success
d8bce46765d40eef000000000000000d,2024-02-20T11:43:09+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ping 58.87.78.56,success
d8bce46765d40eef000000000000000d,2024-02-20T11:44:42+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ping baidu.com,success
d8bce46765d40eef000000000000000d,2024-02-20T11:44:46+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ping 58.87.78.56,success
d8bce46765d40eef000000000000000d,2024-02-20T11:54:10+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git add .,success
d8bce46765d40eef000000000000000d,2024-02-20T12:01:41+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git commit -m "backup on 20240220",success
d8bce46765d40eef000000000000000d,2024-02-20T12:02:45+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git config --global user.email "<EMAIL>",success
d8bce46765d40eef000000000000000d,2024-02-20T12:02:57+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git config --global user.name "liuchunyang",success
d8bce46765d40eef000000000000000d,2024-02-20T12:03:02+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git commit -m "backup on 20240220",success
d8bce46765d40eef000000000000000d,2024-02-20T12:05:00+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git config --global pack.windowMemory "32m",success
d8bce46765d40eef000000000000000d,2024-02-20T12:05:07+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git push,success
d8bce46765d40eef000000000000000d,2024-02-20T14:03:28+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git pull,success
d8bce46765d40eef000000000000000d,2024-02-20T14:07:25+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ll -a,success
d8bce46765d40eef000000000000000d,2024-02-20T14:07:45+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ll -a apps/,success
d8bce46765d40eef000000000000000d,2024-02-20T14:10:05+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,rm -r .git/,success
d8bce46765d40eef000000000000000d,2024-02-20T14:10:11+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,rm .gitignore,success
d8bce46765d40eef000000000000000d,2024-02-20T14:10:12+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ll,success
d8bce46765d40eef000000000000000d,2024-02-20T14:10:22+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git add .,success
d8bce46765d40eef000000000000000d,2024-02-20T14:13:33+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git status,success
d8bce46765d40eef000000000000000d,2024-02-20T14:18:40+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git init,success
d8bce46765d40eef000000000000000d,2024-02-20T14:18:43+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ll,success
d8bce46765d40eef000000000000000d,2024-02-20T14:19:19+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git remote <NAME_EMAIL>:ops/miniglobal-apps-deploy.git,success
d8bce46765d40eef000000000000000d,2024-02-20T14:19:43+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git add .,success
d8bce46765d40eef000000000000000d,2024-02-20T14:24:39+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git commit -m "New miniglobal-apps backup",success
d8bce46765d40eef000000000000000d,2024-02-20T14:25:38+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git config --global pack.windowMemory "32m",success
d8bce46765d40eef000000000000000d,2024-02-20T14:25:46+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git push,success
d8bce46765d40eef000000000000000d,2024-02-20T14:30:04+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git branch -M master,success
d8bce46765d40eef000000000000000d,2024-02-20T14:30:35+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git push -uf origin master,success
d8bce46765d40eef000000000000000d,2024-02-20T14:33:42+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git push --set-upstream origin master,success
d8bce46765d40eef000000000000000d,2024-02-20T14:34:12+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git pull,success
d8bce46765d40eef000000000000000d,2024-02-20T14:34:40+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git push,success
d8bce46765d40eef000000000000000d,2024-02-20T14:34:49+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git push --set-upstream origin master,success
d8bce46765d40eef000000000000000d,2024-02-20T14:35:44+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git pull,success
d8bce46765d40eef000000000000000d,2024-02-20T14:36:45+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git branch --set-upstream-to=origin/master master,success
d8bce46765d40eef000000000000000d,2024-02-20T14:37:02+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git pull,success
d8bce46765d40eef000000000000000d,2024-02-20T14:37:21+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git push --set-upstream origin master,success
d8bce46765d40eef000000000000000d,2024-02-20T14:37:57+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git branch --set-upstream-to=origin/master,success
d8bce46765d40eef000000000000000d,2024-02-20T14:38:11+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git add .,success
d8bce46765d40eef000000000000000d,2024-02-20T14:38:33+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git commit -m "New miniglobal-apps backup.",success
d8bce46765d40eef000000000000000d,2024-02-20T14:39:23+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git config --global pack.windowMemory "32m",success
d8bce46765d40eef000000000000000d,2024-02-20T14:39:44+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git push -u origin master,success
d8bce46765d40eef000000000000000d,2024-02-20T14:42:23+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git pull origin master,success
d8bce46765d40eef000000000000000d,2024-02-20T14:43:27+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git pull --allow-unrelated-histories origin master,success
d8bce46765d40eef000000000000000d,2024-02-20T15:03:03+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git status,success
d8bce46765d40eef000000000000000d,2024-02-20T15:04:33+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ll,success
d8bce46765d40eef000000000000000d,2024-02-20T15:04:39+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,less README.md,success
d8bce46765d40eef000000000000000d,2024-02-20T15:05:02+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,qgit add .,success
d8bce46765d40eef000000000000000d,2024-02-20T15:05:31+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git commit -m "Update",success
d8bce46765d40eef000000000000000d,2024-02-20T15:05:38+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git push,success
d8bce46765d40eef000000000000000d,2024-02-20T15:49:05+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git pull,success
d8bce46765d40eef000000000000000d,2024-02-20T15:49:26+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,cd apps/miniglobal/fed-avenir/,success
d8bce46765d40eef000000000000000d,2024-02-20T15:49:38+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,make build TAG=PRD_T402181,success
d8bce46765d40eef000000000000000d,2024-02-20T15:50:20+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,cat build.sh,success
d8bce46765d40eef000000000000000d,2024-02-20T16:40:41+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git pull,success
d8bce46765d40eef000000000000000d,2024-02-20T16:41:09+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,make build TAG=PRD_T402181,success
d8bce46765d40eef000000000000000d,2024-02-20T16:51:22+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,git pull,success
d8bce46765d40eef000000000000000d,2024-02-20T16:51:32+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,make build TAG=PRD_T402181,success
d8bce46765d40eef000000000000000d,2024-02-20T16:58:52+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,make build TAG=PRD_T402201,success
d8bce46765d40eef000000000000000d,2024-02-20T17:03:45+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,docker images,success
d8bce46765d40eef000000000000000d,2024-02-20T17:13:08+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********, docker run -t -i miniglobal-harbor.cogdocs.com/builder/node:20.11.0  /bin/bash,success
d8bce46765d40eef000000000000000d,2024-02-20T17:13:41+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,cd /build/code,success
d8bce46765d40eef000000000000000d,2024-02-20T17:13:52+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,cd /build,success
d8bce46765d40eef000000000000000d,2024-02-20T17:14:08+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ls /,success
d8bce46765d40eef000000000000000d,2024-02-20T17:19:18+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,root@miniglobal-deploy:/hbdata/soft/miniglobal-apps/apps/miniglobal/fed-avenir#,success
d8bce46765d40eef000000000000000d,2024-02-20T17:19:19+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ll,success
d8bce46765d40eef000000000000000d,2024-02-20T17:19:31+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,cat Dockerfile,success
d8bce46765d40eef000000000000000d,2024-02-20T17:19:37+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,cd build_command_pkgs/,success
d8bce46765d40eef000000000000000d,2024-02-20T17:19:38+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ll,success
d8bce46765d40eef000000000000000d,2024-02-20T17:20:35+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,cat fed_,success
d8bce46765d40eef000000000000000d,2024-02-20T17:23:52+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,cd ..,success
d8bce46765d40eef000000000000000d,2024-02-20T17:23:57+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,cd -,success
d8bce46765d40eef000000000000000d,2024-02-20T17:23:58+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ll,success
d8bce46765d40eef000000000000000d,2024-02-20T17:24:01+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,cd fed-avenir,success
d8bce46765d40eef000000000000000d,2024-02-20T17:24:03+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ll,success
d8bce46765d40eef000000000000000d,2024-02-20T17:28:49+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,cd ../..,success
d8bce46765d40eef000000000000000d,2024-02-20T17:28:51+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ll,success
d8bce46765d40eef000000000000000d,2024-02-20T17:28:54+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,vim build.sh,success
d8bce46765d40eef000000000000000d,2024-02-20T17:29:06+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,:q,success
d8bce46765d40eef000000000000000d,2024-02-20T17:30:04+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,docker run --rm -e GIT_VERSION=PRD_T402201 -w /build/code -v /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-avenir/build_command_pkgs/m2:/build/.m2 -v /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-avenir/build_command_pkgs/fed-avenir:/build/code  miniglobal-harbor.cogdocs.com/builder/node:20.11.0 /build/code/_build.sh,success
d8bce46765d40eef000000000000000d,2024-02-20T17:30:28+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,docker run --rm -e GIT_VERSION=PRD_T402201 -w /build/code -v /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-avenir/build_command_pkgs/m2:/build/.m2 -v /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-avenir/build_command_pkgs/fed-avenir:/build/code  miniglobal-harbor.cogdocs.com/builder/node:20.11.0,success
d8bce46765d40eef000000000000000d,2024-02-20T17:31:07+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,docker run --rm -e GIT_VERSION=PRD_T402201 -w /build/code -v /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-avenir/build_command_pkgs/m2:/build/.m2 -v /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-avenir/build_command_pkgs/fed-avenir:/build/code  miniglobal-harbor.cogdocs.com/builder/node:20.11.0 /bin/bash,success
d8bce46765d40eef000000000000000d,2024-02-20T17:31:30+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,docker run --rm -e GIT_VERSION=PRD_T402201 -w /build/code -v /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-avenir/build_command_pkgs/m2:/build/.m2 -v /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-avenir/build_command_pkgs/fed-avenir:/build/code  miniglobal-harbor.cogdocs.com/builder/node:20.11.0 -it /bin/bash,success
d8bce46765d40eef000000000000000d,2024-02-20T17:31:52+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,docker run --rm -it -e GIT_VERSION=PRD_T402201 -w /build/code -v /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-avenir/build_command_pkgs/m2:/build/.m2 -v /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-avenir/build_command_pkgs/fed-avenir:/build/code  miniglobal-harbor.cogdocs.com/builder/node:20.11.0  /bin/bash,success
d8bce46765d40eef000000000000000d,2024-02-20T17:31:56+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ll,success
d8bce46765d40eef000000000000000d,2024-02-20T17:31:57+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ls -l,success
d8bce46765d40eef000000000000000d,2024-02-20T17:32:42+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ls -l _build.sh/,success
d8bce46765d40eef000000000000000d,2024-02-20T17:33:01+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ll,success
d8bce46765d40eef000000000000000d,2024-02-20T17:33:03+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,cd bu,success
d8bce46765d40eef000000000000000d,2024-02-20T17:33:04+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ll,success
d8bce46765d40eef000000000000000d,2024-02-20T17:33:10+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,cat fed-avenir__build.sh,success
d8bce46765d40eef000000000000000d,2024-02-20T17:39:30+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ll,success
d8bce46765d40eef000000000000000d,2024-02-20T17:39:34+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,cd fed-avenir,success
d8bce46765d40eef000000000000000d,2024-02-20T17:39:35+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ll,success
d8bce46765d40eef000000000000000d,2024-02-20T17:39:44+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,rm -r _build.sh/,success
d8bce46765d40eef000000000000000d,2024-02-20T17:39:46+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ll,success
d8bce46765d40eef000000000000000d,2024-02-20T17:39:57+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,cd ../..,success
d8bce46765d40eef000000000000000d,2024-02-20T17:40:07+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,docker run --rm -it -e GIT_VERSION=PRD_T402201 -w /build/code -v /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-avenir/build_command_pkgs/m2:/build/.m2 -v /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-avenir/build_command_pkgs/fed-avenir:/build/code  miniglobal-harbor.cogdocs.com/builder/node:20.11.0  /bin/bash,success
d8bce46765d40eef000000000000000d,2024-02-20T17:40:08+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ll,success
d8bce46765d40eef000000000000000d,2024-02-20T17:40:11+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ls -l,success
d8bce46765d40eef000000000000000d,2024-02-20T17:40:34+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,docker run --rm -e GIT_VERSION=PRD_T402201 -w /build/code -v /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-avenir/build_command_pkgs/m2:/build/.m2 -v /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-avenir/build_command_pkgs/fed-avenir:/build/code  miniglobal-harbor.cogdocs.com/builder/node:20.11.0 /build/code/_build.sh,success
d8bce46765d40eef000000000000000d,2024-02-20T17:41:33+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,cd b,success
d8bce46765d40eef000000000000000d,2024-02-20T17:41:35+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ll,success
d8bce46765d40eef000000000000000d,2024-02-20T17:41:49+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,rm -r fed-avenir-web__build.sh/,success
d8bce46765d40eef000000000000000d,2024-02-20T17:41:50+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ll,success
d8bce46765d40eef000000000000000d,2024-02-20T17:41:53+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,cd ..,success
d8bce46765d40eef000000000000000d,2024-02-20T17:41:54+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,ll,success
d8bce46765d40eef000000000000000d,2024-02-20T17:42:12+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,make build TAG=PRD_T402181,success
d8bce46765d40eef000000000000000d,2024-02-20T17:44:09+08:00,cmd.Command,avenir_user01,120.244.194.58,miniglobal-deploy,***********,make build TAG=PRD_T402201,success
9d3ab35465d52edb000000000000000d,2024-02-21T06:59:46+08:00,cmd.Command,avenir_user01,223.104.3.180,miniglobal-deploy,***********,cd /hbdata/soft/miniglobal-apps/,success
9d3ab35465d52edb000000000000000d,2024-02-21T07:00:26+08:00,cmd.Command,avenir_user01,223.104.3.180,miniglobal-deploy,***********,git status,success
9d3ab35465d52edb000000000000000d,2024-02-21T07:01:55+08:00,cmd.Command,avenir_user01,223.104.3.180,miniglobal-deploy,***********,git add .,success
9d3ab35465d52edb000000000000000d,2024-02-21T07:02:29+08:00,cmd.Command,avenir_user01,223.104.3.180,miniglobal-deploy,***********,git commit -m "fed-avenir 打包成功",success
9d3ab35465d52edb000000000000000d,2024-02-21T07:02:38+08:00,cmd.Command,avenir_user01,223.104.3.180,miniglobal-deploy,***********,git push,success
b848fbdc65d561d90000000000000019,2024-02-21T10:37:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
b848fbdc65d561d90000000000000019,2024-02-21T10:37:36+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,use proamsdwdb;,success
b848fbdc65d561d90000000000000019,2024-02-21T10:37:47+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
b848fbdc65d561d90000000000000019,2024-02-21T10:37:47+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where f_state in(3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
b848fbdc65d561d90000000000000019,2024-02-21T10:37:48+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,group by f_currency;,success
b848fbdc65d561d90000000000000019,2024-02-21T10:57:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,use hbg_trade; ,success
b848fbdc65d561d90000000000000019,2024-02-21T10:57:57+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_state, count(*) as user_count,success
b848fbdc65d561d90000000000000019,2024-02-21T10:57:57+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from t_account_manage_fee_snapshot  ,success
b848fbdc65d561d90000000000000019,2024-02-21T10:58:02+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where f_day in('********'),success
b848fbdc65d561d90000000000000019,2024-02-21T10:58:02+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,group by f_state;,success
b848fbdc65d561d90000000000000019,2024-02-21T11:05:12+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,01,success
b848fbdc65d561d90000000000000019,2024-02-21T12:16:28+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,exit,success
b848fbdc65d561d90000000000000019,2024-02-21T12:16:31+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,exit,success
ddf32c3c65d59b2a000000000000000d,2024-02-21T14:41:51+08:00,cmd.Command,avenir_user01,117.129.8.49,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd /var/log/hue,success
ddf32c3c65d59b2a000000000000000d,2024-02-21T14:41:52+08:00,cmd.Command,avenir_user01,117.129.8.49,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
ddf32c3c65d59b2a000000000000000d,2024-02-21T14:43:26+08:00,cmd.Command,avenir_user01,117.129.8.49,miniglobal-bigdata-datanode-1c-02,172.28.3.8,egrep '16897189|17601490|47932522' runcpserver.log*,success
ddf32c3c65d59b2a000000000000000d,2024-02-21T14:43:56+08:00,cmd.Command,avenir_user01,117.129.8.49,miniglobal-bigdata-datanode-1c-02,172.28.3.8,egrep 'bigdata' runcpserver.log*|wc -l,success
82b331c265d59bbe000000000000000d,2024-02-21T14:44:17+08:00,cmd.Command,avenir_user01,117.129.8.49,miniglobal-bigdata-datanode-1c-03,172.28.3.121,cd /var/log/hue,success
82b331c265d59bbe000000000000000d,2024-02-21T14:44:18+08:00,cmd.Command,avenir_user01,117.129.8.49,miniglobal-bigdata-datanode-1c-03,172.28.3.121,ll,success
82b331c265d59bbe000000000000000d,2024-02-21T14:44:20+08:00,cmd.Command,avenir_user01,117.129.8.49,miniglobal-bigdata-datanode-1c-03,172.28.3.121,egrep '16897189|17601490|47932522' runcpserver.log*,success
75a4d2aa65d59bcb000000000000000d,2024-02-21T14:44:31+08:00,cmd.Command,avenir_user01,117.129.8.49,miniglobal-bigdata-datanode-1c-05,172.28.3.93,cd /var/log/hue,success
75a4d2aa65d59bcb000000000000000d,2024-02-21T14:44:34+08:00,cmd.Command,avenir_user01,117.129.8.49,miniglobal-bigdata-datanode-1c-05,172.28.3.93,egrep '16897189|17601490|47932522' runcpserver.log*,success
75a4d2aa65d59bcb000000000000000d,2024-02-21T14:44:36+08:00,cmd.Command,avenir_user01,117.129.8.49,miniglobal-bigdata-datanode-1c-05,172.28.3.93,ll,success
c293b1c565d5b8cf000000000000000d,2024-02-21T16:48:23+08:00,cmd.Command,avenir_user01,117.129.8.49,miniglobal-devops,***********,telnet 172.28.32.101 8080,success
c293b1c565d5b8cf000000000000000d,2024-02-21T16:49:28+08:00,cmd.Command,avenir_user01,117.129.8.49,miniglobal-devops,***********,curl http://172.28.32.101,success
c293b1c565d5b8cf000000000000000d,2024-02-21T16:53:34+08:00,cmd.Command,avenir_user01,117.129.8.49,miniglobal-devops,***********,telnet 172.28.46.62 80,success
7450b18965d5f2a20000000000000013,2024-02-21T20:58:37+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,2,success
7450b18965d5f2a20000000000000013,2024-02-21T20:59:04+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,docker restart forensic_etl,success
7450b18965d5f2a20000000000000013,2024-02-21T20:59:22+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,34:,success
7450b18965d5f2a20000000000000013,2024-02-21T20:59:22+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                            0$ mycli  1$ ~  2$ ~  3-$ ..641bec/result  (4*$vim)  5$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                            ][21/02 12:59],success
7450b18965d5f2a20000000000000013,2024-02-21T20:59:25+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                            0$ mycli  1$ ~  2$ ~  3-$ ..641bec/result  (4*$vim)  5$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                            ][21/02 12:59],success
7450b18965d5f2a20000000000000013,2024-02-21T20:59:26+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                      0$ mycli  1$ ~  2$ ~  3-$ ..641bec/result  (4*$..task/trade_dw)  5$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                      ][21/02 12:59],success
7450b18965d5f2a20000000000000013,2024-02-21T20:59:31+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                      0$ mycli  1$ ~  2$ ~  3-$ ..641bec/result  (4*$..task/trade_dw)  5$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                      ][21/02 12:59],success
7450b18965d5f2a20000000000000013,2024-02-21T20:59:36+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                      0$ mycli  1$ ~  2$ ~  3-$ ..641bec/result  (4*$..task/trade_dw)  5$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                      ][21/02 12:59],success
7450b18965d5f2a20000000000000013,2024-02-21T20:59:37+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                      0$ mycli  1$ ~  2$ ~  3-$ ..641bec/result  (4*$..forensic_task)  5$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                      ][21/02 12:59],success
7450b18965d5f2a20000000000000013,2024-02-21T20:59:38+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                      0$ mycli  1$ ~  2$ ~  3-$ ..641bec/result  (4*$..forensic_task)  5$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                      ][21/02 12:59],success
7450b18965d5f2a20000000000000013,2024-02-21T20:59:38+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                      0$ mycli  1$ ~  2$ ~  3-$ ..641bec/result  (4*$~/forensic_etl)  5$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                       ][21/02 12:59],success
7450b18965d5f2a20000000000000013,2024-02-21T20:59:39+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                      0$ mycli  1$ ~  2$ ~  3-$ ..641bec/result  (4*$~/forensic_etl)  5$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                       ][21/02 12:59],success
7450b18965d5f2a20000000000000013,2024-02-21T20:59:45+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                      0$ mycli  1$ ~  2$ ~  3-$ ..641bec/result  (4*$~/forensic_etl)  5$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                       ][21/02 13:00],success
7450b18965d5f2a20000000000000013,2024-02-21T20:59:48+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                      0$ mycli  1$ ~  2$ ~  3-$ ..641bec/result  (4*$~/forensic_etl)  5$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                       ][21/02 13:00],success
7450b18965d5f2a20000000000000013,2024-02-21T20:59:58+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                      0$ mycli  1$ ~  2$ ~  3-$ ..641bec/result  (4*$~/forensic_etl)  5$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                       ][21/02 13:00],success
7450b18965d5f2a20000000000000013,2024-02-21T21:00:00+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                      0$ mycli  1$ ~  2$ ~  3-$ ..641bec/result  (4*$~/forensic_etl)  5$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                       ][21/02 13:00],success
7450b18965d5f2a20000000000000013,2024-02-21T21:00:01+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                      0$ mycli  1$ ~  2$ ~  3-$ ..641bec/result  (4*$~/forensic_etl)  5$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                       ][21/02 13:00],success
7450b18965d5f2a20000000000000013,2024-02-21T21:00:01+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                      0$ mycli  1$ ~  2$ ~  3-$ ..641bec/result  (4*$..ensic_etl/gen)  5$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                      ][21/02 13:00],success
7450b18965d5f2a20000000000000013,2024-02-21T21:00:08+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,44cd ..,success
7450b18965d5f2a20000000000000013,2024-02-21T21:00:08+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,ls,success
7450b18965d5f2a20000000000000013,2024-02-21T21:00:18+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                      0$ mycli  1$ ~  2$ ~  3$ ..641bec/result  4-$ ~/forensic_etl  (5*$vim)  6$ ~/forensic_etl  7$ ~/forensic_etl                                       ][21/02 13:00],success
7450b18965d5f2a20000000000000013,2024-02-21T21:00:19+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                 0$ mycli  1$ ~  2$ ~  3$ ..641bec/result  4-$ ~/forensic_etl  (5*$~/forensic_etl)  6$ ~/forensic_etl  7$ ~/forensic_etl                                 ][21/02 13:00],success
7450b18965d5f2a20000000000000013,2024-02-21T21:00:20+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                 0$ mycli  1$ ~  2$ ~  3$ ..641bec/result  4-$ ~/forensic_etl  (5*$~/forensic_etl)  6$ ~/forensic_etl  7$ ~/forensic_etl                                 ][21/02 13:00],success
7450b18965d5f2a20000000000000013,2024-02-21T21:00:29+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,GGkkkkkkki# jjjjjjjhxxw,success
7450b18965d5f2a20000000000000013,2024-02-21T21:00:36+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                      0$ mycli  1$ ~  2$ ~  3$ ..641bec/result  4$ ~/forensic_etl  5-$ vim  (6*$~/forensic_etl)  7$ ~/forensic_etl                                       ][21/02 13:00],success
7450b18965d5f2a20000000000000013,2024-02-21T21:01:00+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                      0$ mycli  1$ ~  2$ ~  3$ ..641bec/result  (4*$~/forensic_etl)  5-$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                       ][21/02 13:01],success
7450b18965d5f2a20000000000000013,2024-02-21T21:01:00+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                      0$ mycli  1$ ~  2$ ~  3$ ..641bec/result  (4*$..ensic_etl/gen)  5-$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                      ][21/02 13:01],success
7450b18965d5f2a20000000000000013,2024-02-21T21:01:03+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                      0$ mycli  1$ ~  2$ ~  3$ ..641bec/result  (4*$..ensic_etl/gen)  5-$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                      ][21/02 13:01],success
7450b18965d5f2a20000000000000013,2024-02-21T21:01:13+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,jjjjjkkkkkkkkkkkkkkklllllllkklxxw,success
7450b18965d5f2a20000000000000013,2024-02-21T21:01:18+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                            0$ mycli  1$ ~  2$ ~  3$ ..641bec/result  4$ vim  5-$ vim  (6*$~/forensic_etl)  7$ ~/forensic_etl                                            ][21/02 13:01],success
7450b18965d5f2a20000000000000013,2024-02-21T21:02:31+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,2docker loA,success
7450b18965d5f2a20000000000000013,2024-02-21T21:03:20+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,654kkkkjjjjjjjjjjjjjjjjjjjjjjjjjjjklllllllllllllljjkkkkjkjjjjkkkkkkkkkkkjjjjjjjjhhj:,success
7450b18965d5f2a20000000000000013,2024-02-21T21:03:21+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2$ docker  3$ ..641bec/result  (4*$vim)  5-$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                          ][21/02 13:03],success
7450b18965d5f2a20000000000000013,2024-02-21T21:03:24+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2$ docker  3$ ..641bec/result  (4*$vim)  5-$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                          ][21/02 13:03],success
7450b18965d5f2a20000000000000013,2024-02-21T21:03:28+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,0$ mycli  1$ ~  2$ docker  3$ ..641bec/result  (4*$vim)  5-$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                          ][21/02 13:03],success
7450b18965d5f2a20000000000000013,2024-02-21T21:03:29+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,:q!,success
7450b18965d5f2a20000000000000013,2024-02-21T21:03:30+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                   0$ mycli  1$ ~  2$ docker  3$ ..641bec/result  (4*$..ensic_etl/gen)  5-$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                    ][21/02 13:03],success
7450b18965d5f2a20000000000000013,2024-02-21T21:03:35+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                   0$ mycli  1$ ~  2$ docker  3$ ..641bec/result  (4*$..ensic_etl/gen)  5-$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                    ][21/02 13:03],success
7450b18965d5f2a20000000000000013,2024-02-21T21:04:17+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,kkkkkkkkkkkk,success
7450b18965d5f2a20000000000000013,2024-02-21T21:04:19+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,jjj:,success
7450b18965d5f2a20000000000000013,2024-02-21T21:04:22+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2$ docker  3$ ..641bec/result  (4*$vim)  5-$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                          ][21/02 13:04],success
7450b18965d5f2a20000000000000013,2024-02-21T21:04:24+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                   0$ mycli  1$ ~  2$ docker  3$ ..641bec/result  (4*$..ensic_etl/gen)  5-$ vim  6$ ~/forensic_etl  7$ ~/forensic_etl                                    ][21/02 13:04],success
45c4497c65d69fe7000000000000000d,2024-02-22T09:14:22+08:00,cmd.Command,avenir_user01,223.104.40.228,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd /var/log/hue,success
45c4497c65d69fe7000000000000000d,2024-02-22T09:14:23+08:00,cmd.Command,avenir_user01,223.104.40.228,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
45c4497c65d69fe7000000000000000d,2024-02-22T09:15:30+08:00,cmd.Command,avenir_user01,223.104.40.228,miniglobal-bigdata-datanode-1c-02,172.28.3.8,tar zcvf /tmp/runcpserver.log.20240222-02.tar.gz runcpserver.log runcpserver.log.1 runcpserver.log.2 runcpserver.log.3,success
f706df6b65d6a048000000000000000d,2024-02-22T09:15:57+08:00,cmd.Command,avenir_user01,223.104.40.228,miniglobal-bigdata-datanode-1c-03,172.28.3.121,cd /var/log/hue,success
f706df6b65d6a048000000000000000d,2024-02-22T09:15:59+08:00,cmd.Command,avenir_user01,223.104.40.228,miniglobal-bigdata-datanode-1c-03,172.28.3.121,ll,success
f706df6b65d6a048000000000000000d,2024-02-22T09:16:20+08:00,cmd.Command,avenir_user01,223.104.40.228,miniglobal-bigdata-datanode-1c-03,172.28.3.121,tar zcvf /tmp/runcpserver.log.20240222-03.tar.gz runcpserver.log runcpserver.log.1 runcpserver.log.2 runcpserver.log.3,success
fd23fb9665d6a06c000000000000000d,2024-02-22T09:16:32+08:00,cmd.Command,avenir_user01,223.104.40.228,miniglobal-bigdata-datanode-1c-05,172.28.3.93,cd /var/log/hue,success
fd23fb9665d6a06c000000000000000d,2024-02-22T09:16:35+08:00,cmd.Command,avenir_user01,223.104.40.228,miniglobal-bigdata-datanode-1c-05,172.28.3.93,ll,success
fd23fb9665d6a06c000000000000000d,2024-02-22T09:16:59+08:00,cmd.Command,avenir_user01,223.104.40.228,miniglobal-bigdata-datanode-1c-05,172.28.3.93,tar zcvf /tmp/runcpserver.log.20240222-05.tar.gz runcpserver.log runcpserver.log.1 runcpserver.log.2 runcpserver.log.3,success
a0a80dfc65d6a094000000000000000d,2024-02-22T09:17:11+08:00,cmd.Command,avenir_user01,223.104.40.228,miniglobal-devops,***********,cd /tmp,success
a0a80dfc65d6a094000000000000000d,2024-02-22T09:17:12+08:00,cmd.Command,avenir_user01,223.104.40.228,miniglobal-devops,***********,ll,success
a0a80dfc65d6a094000000000000000d,2024-02-22T09:17:54+08:00,cmd.Command,avenir_user01,223.104.40.228,miniglobal-devops,***********,scp 172.28.3.8:/tmp/runcpserver.log.20240222-02.tar.gz .,success
a0a80dfc65d6a094000000000000000d,2024-02-22T09:18:07+08:00,cmd.Command,avenir_user01,223.104.40.228,miniglobal-devops,***********,scp 172.28.3.121:/tmp/runcpserver.log.20240222-03.tar.gz .,success
a0a80dfc65d6a094000000000000000d,2024-02-22T09:18:21+08:00,cmd.Command,avenir_user01,223.104.40.228,miniglobal-devops,***********,scp 172.28.3.93:/tmp/runcpserver.log.20240222-05.tar.gz .,success
a0a80dfc65d6a094000000000000000d,2024-02-22T09:18:37+08:00,cmd.Command,avenir_user01,223.104.40.228,miniglobal-devops,***********,ossutil cp runcpserver.log.20240222-02.tar.gz  oss://miniglobal-devops/hue_log/,success
a0a80dfc65d6a094000000000000000d,2024-02-22T09:18:43+08:00,cmd.Command,avenir_user01,223.104.40.228,miniglobal-devops,***********,ossutil cp runcpserver.log.20240222-03.tar.gz  oss://miniglobal-devops/hue_log/,success
a0a80dfc65d6a094000000000000000d,2024-02-22T09:18:47+08:00,cmd.Command,avenir_user01,223.104.40.228,miniglobal-devops,***********,ossutil cp runcpserver.log.20240222-05.tar.gz  oss://miniglobal-devops/hue_log/,success
a0a80dfc65d6a094000000000000000d,2024-02-22T09:22:37+08:00,cmd.Command,avenir_user01,223.104.40.228,miniglobal-devops,***********,ll -h,success
a0a80dfc65d6a094000000000000000d,2024-02-22T09:25:46+08:00,cmd.Command,avenir_user01,223.104.40.228,miniglobal-devops,***********,rm -f runcpserver.log.20240222-0*,success
a0a80dfc65d6a094000000000000000d,2024-02-22T09:25:47+08:00,cmd.Command,avenir_user01,223.104.40.228,miniglobal-devops,***********,ll,success
06b2175b65d6a2a3000000000000000d,2024-02-22T09:26:03+08:00,cmd.Command,avenir_user01,223.104.40.228,miniglobal-bigdata-datanode-1c-02,172.28.3.8,rm -f /tmp/runcpserver.log.20240222-02.tar.gz,success
6d8d63bc65d6a2b0000000000000000d,2024-02-22T09:26:13+08:00,cmd.Command,avenir_user01,223.104.40.228,miniglobal-bigdata-datanode-1c-03,172.28.3.121,rm -f /tmp/runcpserver.log.20240222-03.tar.gz,success
986bee2265d6a2b8000000000000000d,2024-02-22T09:26:20+08:00,cmd.Command,avenir_user01,223.104.40.228,miniglobal-bigdata-datanode-1c-05,172.28.3.93,rm -f /tmp/runcpserver.log.20240222-05.tar.gz,success
a3e51b2865d6a88c000000000000000d,2024-02-22T09:51:22+08:00,cmd.Command,avenir_user01,223.104.40.228,miniglobal-deploy,***********,kubectl get node,success
1f9f65d865d6a8ab000000000000000d,2024-02-22T09:51:43+08:00,cmd.Command,avenir_user01,223.104.40.228,miniglobal-devops,***********,kubectl get node,success
1f9f65d865d6a8ab000000000000000d,2024-02-22T09:51:54+08:00,cmd.Command,avenir_user01,223.104.40.228,miniglobal-devops,***********,kubectl get ns,success
1f9f65d865d6a8ab000000000000000d,2024-02-22T09:52:03+08:00,cmd.Command,avenir_user01,223.104.40.228,miniglobal-devops,***********,kubectl -n prd8 get svc,success
182159cf65d6f15f000000000000000d,2024-02-22T15:02:21+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,mysql -h 172.28.5.15 -p -P3614 -u superdba ,success
182159cf65d6f15f000000000000000d,2024-02-22T15:02:59+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,show databases like '%mgt%';,success
182159cf65d6f15f000000000000000d,2024-02-22T15:03:05+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,use mgt_console,success
182159cf65d6f15f000000000000000d,2024-02-22T15:03:10+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,show tables;,success
182159cf65d6f15f000000000000000d,2024-02-22T15:04:01+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,show create table t_virtual_withdraw_order_history;,success
182159cf65d6f15f000000000000000d,2024-02-22T15:05:03+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,select count(*) from t_virtual_withdraw_order_history where f_uid = 15708268;,success
182159cf65d6f15f000000000000000d,2024-02-22T15:05:15+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,select * from t_virtual_withdraw_order_history where f_uid = 15708268;,success
182159cf65d6f15f000000000000000d,2024-02-22T15:07:39+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,select f_uid, f_currency, f_amount, f_to_address, f_tx_hash from t_virtual_withdraw_order_history where f_uid = 15708268;,success
182159cf65d6f15f000000000000000d,2024-02-22T15:14:33+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,select count(*) from t_virtual_withdraw_order_history;,success
182159cf65d6f15f000000000000000d,2024-02-22T15:31:02+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,use hbg_order,success
182159cf65d6f15f000000000000000d,2024-02-22T15:31:15+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,show databases like '%hbg%';,success
182159cf65d6f15f000000000000000d,2024-02-22T15:31:22+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,use hbgorderdb,success
182159cf65d6f15f000000000000000d,2024-02-22T15:31:25+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,show tables;,success
182159cf65d6f15f000000000000000d,2024-02-22T15:32:28+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,CREATE TABLE `t_point_fee_manage_item` (,success
182159cf65d6f15f000000000000000d,2024-02-22T15:32:28+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,  `f_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',,success
182159cf65d6f15f000000000000000d,2024-02-22T15:32:28+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,  `f_user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'userId',,success
182159cf65d6f15f000000000000000d,2024-02-22T15:32:28+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,  `f_u_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'uid',,success
182159cf65d6f15f000000000000000d,2024-02-22T15:32:28+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,  `f_fee_amount` decimal(36,18) NOT NULL DEFAULT '0.000000000000000000' COMMENT '收取的管理费的数量',,success
182159cf65d6f15f000000000000000d,2024-02-22T15:32:28+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,  `f_deduct_before` decimal(36,18) NOT NULL DEFAULT '0.000000000000000000' COMMENT '扣除管理费前的回收总价',,success
182159cf65d6f15f000000000000000d,2024-02-22T15:32:29+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********, `f_created_at` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间',,success
182159cf65d6f15f000000000000000d,2024-02-22T15:32:29+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,  `f_updated_at` bigint(20) NOT NULL DEFAULT '0' COMMENT '修改时间',,success
182159cf65d6f15f000000000000000d,2024-02-22T15:32:29+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,  PRIMARY KEY (`f_id`),,success
182159cf65d6f15f000000000000000d,2024-02-22T15:32:29+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,  UNIQUE KEY `uniq_userId_deductMonth` (`f_user_id`, `f_deduct_month`),success
182159cf65d6f15f000000000000000d,2024-02-22T15:32:29+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,  `f_deduct_month` varchar(6) NOT NULL DEFAULT '' COMMENT '收取管理费的月份',,success
182159cf65d6f15f000000000000000d,2024-02-22T15:32:31+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='点卡管理费扣除明细表';,success
182159cf65d6f15f000000000000000d,2024-02-22T15:32:40+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,show tables;,success
182159cf65d6f15f000000000000000d,2024-02-22T15:33:21+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,quit,success
4522831e65d70ad1000000000000000d,2024-02-22T16:50:28+08:00,cmd.Command,avenir_user01,192.168.10.10,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd /opt,success
4522831e65d70ad1000000000000000d,2024-02-22T16:50:29+08:00,cmd.Command,avenir_user01,192.168.10.10,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ls,success
4522831e65d70ad1000000000000000d,2024-02-22T16:50:33+08:00,cmd.Command,avenir_user01,192.168.10.10,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd trino/,success
4522831e65d70ad1000000000000000d,2024-02-22T16:50:34+08:00,cmd.Command,avenir_user01,192.168.10.10,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ls,success
4522831e65d70ad1000000000000000d,2024-02-22T16:52:00+08:00,cmd.Command,avenir_user01,192.168.10.10,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd bin/,success
4522831e65d70ad1000000000000000d,2024-02-22T16:52:01+08:00,cmd.Command,avenir_user01,192.168.10.10,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ls,success
4522831e65d70ad1000000000000000d,2024-02-22T16:52:20+08:00,cmd.Command,avenir_user01,192.168.10.10,miniglobal-bigdata-datanode-1c-02,172.28.3.8,./launcher restart,success
4522831e65d70ad1000000000000000d,2024-02-22T16:53:21+08:00,cmd.Command,avenir_user01,192.168.10.10,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ps aux,success
4360251a65d70bb0000000000000000d,2024-02-22T16:54:13+08:00,cmd.Command,avenir_user01,192.168.10.10,miniglobal-bigdata-datanode-1c-01,172.28.3.190,ps aux|grep trino,success
4360251a65d70bb0000000000000000d,2024-02-22T16:54:55+08:00,cmd.Command,avenir_user01,192.168.10.10,miniglobal-bigdata-datanode-1c-01,172.28.3.190,/opt/trino/bin/launcher restart,success
4360251a65d70bb0000000000000000d,2024-02-22T16:55:14+08:00,cmd.Command,avenir_user01,192.168.10.10,miniglobal-bigdata-datanode-1c-01,172.28.3.190,ps aux|grep trino,success
85a4576f65d70bfe000000000000000d,2024-02-22T16:55:35+08:00,cmd.Command,avenir_user01,192.168.10.10,miniglobal-bigdata-datanode-1c-03,172.28.3.121,ps aux |grep trino,success
38b5d7e865d70e40000000000000000d,2024-02-22T17:05:06+08:00,cmd.Command,avenir_user01,192.168.10.10,miniglobal-devops,***********,ssh 172.28.9.20,success
38b5d7e865d70e40000000000000000d,2024-02-22T17:06:52+08:00,cmd.Command,avenir_user01,192.168.10.10,miniglobal-devops,***********,ps aux,success
77f591ca65d70f69000000000000000d,2024-02-22T17:10:15+08:00,cmd.Command,avenir_user01,192.168.10.10,miniglobal-bigdata-datanode-1c-03,172.28.3.121,/opt/trino/bin/launcher restart,success
fbcb1edf65d70f81000000000000000d,2024-02-22T17:10:30+08:00,cmd.Command,avenir_user01,192.168.10.10,miniglobal-bigdata-datanode-1c-04,172.28.3.15,/opt/trino/bin/launcher restart,success
a5ccd25a65d70fdc000000000000000d,2024-02-22T17:11:59+08:00,cmd.Command,avenir_user01,192.168.10.10,miniglobal-devops,***********,ssh 172.28.9.20,success
a5ccd25a65d70fdc000000000000000d,2024-02-22T17:12:01+08:00,cmd.Command,avenir_user01,192.168.10.10,miniglobal-devops,***********,df -h,success
a5ccd25a65d70fdc000000000000000d,2024-02-22T17:12:15+08:00,cmd.Command,avenir_user01,192.168.10.10,miniglobal-devops,***********,telnet 10008,success
a5ccd25a65d70fdc000000000000000d,2024-02-22T17:12:54+08:00,cmd.Command,avenir_user01,192.168.10.10,miniglobal-devops,***********,telnet 172.28.9.20 443,success
a5ccd25a65d70fdc000000000000000d,2024-02-22T17:12:58+08:00,cmd.Command,avenir_user01,192.168.10.10,miniglobal-devops,***********,q,success
a0a2c30365d747bf000000000000000d,2024-02-22T21:10:49+08:00,cmd.Command,avenir_user01,192.168.10.6,miniglobal-devops,***********,kubectl -n prd8 logs pod fed-avenir-c8b85964f-84kxx,success
a0a2c30365d747bf000000000000000d,2024-02-22T21:11:00+08:00,cmd.Command,avenir_user01,192.168.10.6,miniglobal-devops,***********,kubectl -n prd8 logs fed-avenir-c8b85964f-84kxx,success
a0a2c30365d747bf000000000000000d,2024-02-22T21:15:08+08:00,cmd.Command,avenir_user01,192.168.10.6,miniglobal-devops,***********,,success
a0a2c30365d747bf000000000000000d,2024-02-22T21:15:52+08:00,cmd.Command,avenir_user01,192.168.10.6,miniglobal-devops,***********,kubectl -n kube-system logs -f nginx-ingress-controller-84f48584f7-97hkr,success
a0a2c30365d747bf000000000000000d,2024-02-22T21:16:16+08:00,cmd.Command,avenir_user01,192.168.10.6,miniglobal-devops,***********,kubectl -n kube-system logs -f nginx-ingress-controller-84f48584f7-97hkr | grep avenir.cogdocs.com,success
a0a2c30365d747bf000000000000000d,2024-02-22T21:24:23+08:00,cmd.Command,avenir_user01,192.168.10.6,miniglobal-devops,***********,ping 172.28.47.17,success
a0a2c30365d747bf000000000000000d,2024-02-22T21:24:30+08:00,cmd.Command,avenir_user01,192.168.10.6,miniglobal-devops,***********,telnet 172.28.47.17 8080,success
a0a2c30365d747bf000000000000000d,2024-02-22T21:24:36+08:00,cmd.Command,avenir_user01,192.168.10.6,miniglobal-devops,***********,curl 172.28.47.17,success
a0a2c30365d747bf000000000000000d,2024-02-22T21:24:57+08:00,cmd.Command,avenir_user01,192.168.10.6,miniglobal-devops,***********,curl 172.28.47.17 80,success
a0a2c30365d747bf000000000000000d,2024-02-22T21:25:08+08:00,cmd.Command,avenir_user01,192.168.10.6,miniglobal-devops,***********,telnet 172.28.47.17 80,success
a0a2c30365d747bf000000000000000d,2024-02-22T21:26:42+08:00,cmd.Command,avenir_user01,192.168.10.6,miniglobal-devops,***********,telnet 172.28.32.62 8080,success
a0a2c30365d747bf000000000000000d,2024-02-22T21:26:47+08:00,cmd.Command,avenir_user01,192.168.10.6,miniglobal-devops,***********,telnet 172.28.32.62 80,success
a0a2c30365d747bf000000000000000d,2024-02-22T21:26:52+08:00,cmd.Command,avenir_user01,192.168.10.6,miniglobal-devops,***********,q,success
a0a2c30365d747bf000000000000000d,2024-02-22T21:27:00+08:00,cmd.Command,avenir_user01,192.168.10.6,miniglobal-devops,***********,telnet 172.28.32.62 443,success
a0a2c30365d747bf000000000000000d,2024-02-22T21:27:04+08:00,cmd.Command,avenir_user01,192.168.10.6,miniglobal-devops,***********,q,success
2db3c86765d7fd490000000000000019,2024-02-23T10:05:04+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
2db3c86765d7fd490000000000000019,2024-02-23T10:05:35+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,use proamsdwdb;,success
2db3c86765d7fd490000000000000019,2024-02-23T10:05:42+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
2db3c86765d7fd490000000000000019,2024-02-23T10:05:42+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where f_state in(3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
2db3c86765d7fd490000000000000019,2024-02-23T10:05:43+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,group by f_currency;,success
2db3c86765d7fd490000000000000019,2024-02-23T14:31:19+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,use hbg_trade; ,success
2db3c86765d7fd490000000000000019,2024-02-23T14:31:30+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from t_account_manage_fee_snapshot,success
2db3c86765d7fd490000000000000019,2024-02-23T14:31:30+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_currency,sum(f_amount),success
2db3c86765d7fd490000000000000019,2024-02-23T14:31:30+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where f_day in('********', '********')  and f_state = 1 ,success
2db3c86765d7fd490000000000000019,2024-02-23T14:31:30+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,and f_currency in ('btc', 'usdt', 'eth', 'ht', 'hbpoint', 'shib', ,success
2db3c86765d7fd490000000000000019,2024-02-23T14:31:30+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,'doge', 'tfuel', 'xrp', 'eos', 'ltc', 'trx', 'husd', 'fil', 'ada', ,success
2db3c86765d7fd490000000000000019,2024-02-23T14:31:30+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,'etc', 'don', 'seele', 'iq', 'bcc', 'ong', 'elf', 'bsv', 'dot', 'cvnt', ,success
2db3c86765d7fd490000000000000019,2024-02-23T14:31:30+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,'beth', 'xec', 'link', 'iost', 'nest', 'sgb', 'btt',success
2db3c86765d7fd490000000000000019,2024-02-23T14:31:30+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,),success
2db3c86765d7fd490000000000000019,2024-02-23T14:31:32+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,group by f_currency order by f_currency;,success
2db3c86765d7fd490000000000000019,2024-02-23T14:38:56+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_currency,sum(f_amount) from t_account_manage_fee_snapshot where f_day in('********', '********')  and f_state = 1  group by f_currency order by f_currency;,success
ef66471265d8411b000000000000000d,2024-02-23T14:54:29+08:00,cmd.Command,avenir_user01,114.243.101.38,miniglobal-deploy,***********,cd /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-avenir/,success
ef66471265d8411b000000000000000d,2024-02-23T14:54:30+08:00,cmd.Command,avenir_user01,114.243.101.38,miniglobal-deploy,***********,ll,success
ef66471265d8411b000000000000000d,2024-02-23T14:54:37+08:00,cmd.Command,avenir_user01,114.243.101.38,miniglobal-deploy,***********,cat build.sh,success
2db3c86765d7fd490000000000000019,2024-02-23T15:03:39+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
2db3c86765d7fd490000000000000019,2024-02-23T15:03:39+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
2db3c86765d7fd490000000000000019,2024-02-23T15:03:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
2db3c86765d7fd490000000000000019,2024-02-23T15:03:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
2db3c86765d7fd490000000000000019,2024-02-23T15:03:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
2db3c86765d7fd490000000000000019,2024-02-23T15:03:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
2db3c86765d7fd490000000000000019,2024-02-23T15:03:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
2db3c86765d7fd490000000000000019,2024-02-23T15:03:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
2db3c86765d7fd490000000000000019,2024-02-23T15:03:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-02-08',success
2db3c86765d7fd490000000000000019,2024-02-23T15:03:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
2db3c86765d7fd490000000000000019,2024-02-23T15:03:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
2db3c86765d7fd490000000000000019,2024-02-23T15:03:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 100;,success
2db3c86765d7fd490000000000000019,2024-02-23T15:03:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
2db3c86765d7fd490000000000000019,2024-02-23T15:03:45+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,use proamsdwdb;,success
2db3c86765d7fd490000000000000019,2024-02-23T15:03:47+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-02-08'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 100;,success
2db3c86765d7fd490000000000000019,2024-02-23T15:04:23+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-02-08'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 1000;,success
2db3c86765d7fd490000000000000019,2024-02-23T15:04:52+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-02-08'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 10000;,success
2db3c86765d7fd490000000000000019,2024-02-23T15:05:26+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
2db3c86765d7fd490000000000000019,2024-02-23T15:05:26+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
2db3c86765d7fd490000000000000019,2024-02-23T15:05:26+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
2db3c86765d7fd490000000000000019,2024-02-23T15:05:26+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
2db3c86765d7fd490000000000000019,2024-02-23T15:05:26+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
2db3c86765d7fd490000000000000019,2024-02-23T15:05:26+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
2db3c86765d7fd490000000000000019,2024-02-23T15:05:26+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
2db3c86765d7fd490000000000000019,2024-02-23T15:05:26+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) > '2024-02-01' ,success
2db3c86765d7fd490000000000000019,2024-02-23T15:05:26+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-02-08',success
2db3c86765d7fd490000000000000019,2024-02-23T15:05:26+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
2db3c86765d7fd490000000000000019,2024-02-23T15:05:26+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
2db3c86765d7fd490000000000000019,2024-02-23T15:05:26+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
2db3c86765d7fd490000000000000019,2024-02-23T15:05:27+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 100;,success
2db3c86765d7fd490000000000000019,2024-02-23T15:06:35+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
2db3c86765d7fd490000000000000019,2024-02-23T15:06:35+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
2db3c86765d7fd490000000000000019,2024-02-23T15:06:35+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
2db3c86765d7fd490000000000000019,2024-02-23T15:06:36+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
2db3c86765d7fd490000000000000019,2024-02-23T15:06:36+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
2db3c86765d7fd490000000000000019,2024-02-23T15:06:36+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
2db3c86765d7fd490000000000000019,2024-02-23T15:06:36+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
2db3c86765d7fd490000000000000019,2024-02-23T15:06:36+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
2db3c86765d7fd490000000000000019,2024-02-23T15:06:36+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
2db3c86765d7fd490000000000000019,2024-02-23T15:06:36+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
2db3c86765d7fd490000000000000019,2024-02-23T15:06:36+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
2db3c86765d7fd490000000000000019,2024-02-23T15:06:36+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-02-15',success
2db3c86765d7fd490000000000000019,2024-02-23T15:06:37+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 100;,success
2db3c86765d7fd490000000000000019,2024-02-23T15:07:08+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,0,success
2db3c86765d7fd490000000000000019,2024-02-23T15:07:38+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-02-15'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 10000;,success
2db3c86765d7fd490000000000000019,2024-02-23T15:08:14+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
2db3c86765d7fd490000000000000019,2024-02-23T15:08:14+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
2db3c86765d7fd490000000000000019,2024-02-23T15:08:14+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
2db3c86765d7fd490000000000000019,2024-02-23T15:08:14+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
2db3c86765d7fd490000000000000019,2024-02-23T15:08:14+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
2db3c86765d7fd490000000000000019,2024-02-23T15:08:14+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
2db3c86765d7fd490000000000000019,2024-02-23T15:08:14+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
2db3c86765d7fd490000000000000019,2024-02-23T15:08:14+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) > '2024-02-08' ,success
2db3c86765d7fd490000000000000019,2024-02-23T15:08:14+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-02-15',success
2db3c86765d7fd490000000000000019,2024-02-23T15:08:14+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
2db3c86765d7fd490000000000000019,2024-02-23T15:08:14+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
2db3c86765d7fd490000000000000019,2024-02-23T15:08:14+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
2db3c86765d7fd490000000000000019,2024-02-23T15:08:15+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 100;,success
2db3c86765d7fd490000000000000019,2024-02-23T15:08:32+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
2db3c86765d7fd490000000000000019,2024-02-23T15:08:32+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
2db3c86765d7fd490000000000000019,2024-02-23T15:08:32+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
2db3c86765d7fd490000000000000019,2024-02-23T15:08:32+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
2db3c86765d7fd490000000000000019,2024-02-23T15:08:32+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
2db3c86765d7fd490000000000000019,2024-02-23T15:08:32+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
2db3c86765d7fd490000000000000019,2024-02-23T15:08:32+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
2db3c86765d7fd490000000000000019,2024-02-23T15:08:32+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
2db3c86765d7fd490000000000000019,2024-02-23T15:08:32+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-02-22',success
2db3c86765d7fd490000000000000019,2024-02-23T15:08:32+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
2db3c86765d7fd490000000000000019,2024-02-23T15:08:32+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
2db3c86765d7fd490000000000000019,2024-02-23T15:08:32+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
2db3c86765d7fd490000000000000019,2024-02-23T15:08:33+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 100;,success
2db3c86765d7fd490000000000000019,2024-02-23T15:09:06+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,0,success
2db3c86765d7fd490000000000000019,2024-02-23T15:09:34+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-02-22'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 10000;,success
2db3c86765d7fd490000000000000019,2024-02-23T15:10:12+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
2db3c86765d7fd490000000000000019,2024-02-23T15:10:12+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
2db3c86765d7fd490000000000000019,2024-02-23T15:10:12+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
2db3c86765d7fd490000000000000019,2024-02-23T15:10:12+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
2db3c86765d7fd490000000000000019,2024-02-23T15:10:12+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
2db3c86765d7fd490000000000000019,2024-02-23T15:10:12+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
2db3c86765d7fd490000000000000019,2024-02-23T15:10:12+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
2db3c86765d7fd490000000000000019,2024-02-23T15:10:12+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) > '2024-02-15' ,success
2db3c86765d7fd490000000000000019,2024-02-23T15:10:12+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-02-22',success
2db3c86765d7fd490000000000000019,2024-02-23T15:10:12+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
2db3c86765d7fd490000000000000019,2024-02-23T15:10:12+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
2db3c86765d7fd490000000000000019,2024-02-23T15:10:12+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
2db3c86765d7fd490000000000000019,2024-02-23T15:10:13+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 100;,success
ef66471265d8411b000000000000000d,2024-02-23T15:22:43+08:00,cmd.Command,avenir_user01,114.243.101.38,miniglobal-deploy,***********,ll,success
ef66471265d8411b000000000000000d,2024-02-23T15:22:49+08:00,cmd.Command,avenir_user01,114.243.101.38,miniglobal-deploy,***********,make build TAG=PRD_T402231,success
03d5f15d65d851f7000000000000000d,2024-02-23T16:06:19+08:00,cmd.Command,avenir_user01,114.243.101.38,miniglobal-devops,***********,ping 172.28.47.186,success
03d5f15d65d851f7000000000000000d,2024-02-23T16:06:31+08:00,cmd.Command,avenir_user01,114.243.101.38,miniglobal-devops,***********,telnet 172.28.47.186 5672,success
03d5f15d65d851f7000000000000000d,2024-02-23T16:06:47+08:00,cmd.Command,avenir_user01,114.243.101.38,miniglobal-devops,***********,telnet 172.28.47.186 15672,success
2db3c86765d7fd490000000000000019,2024-02-23T16:41:15+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,exit,success
2db3c86765d7fd490000000000000019,2024-02-23T16:41:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,exit,success
8b7390c665d8677b000000000000000d,2024-02-23T17:38:07+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-deploy,***********,cd /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-avenir/,success
8b7390c665d8677b000000000000000d,2024-02-23T17:38:20+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-deploy,***********,make build TAG=PRD_T402231,success
8b7390c665d8677b000000000000000d,2024-02-23T17:44:15+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-deploy,***********,make build TAG=PRD_T402232,success
dd92812865d961c8000000000000000d,2024-02-24T11:26:30+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,kubectl -n kube-system get pod ack-node-problem-detector-daemonset-2dkp7 -o yaml,success
95553c5365dafa4b0000000000000013,2024-02-25T16:29:13+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,2,success
95553c5365dafa4b0000000000000013,2024-02-25T16:29:27+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                   0$ mycli  1$ ~  2$ docker  3$ ..641bec/result  4$ ..ensic_etl/gen  5-$ vim  (6*$~/forensic_etl)  7$ ~/forensic_etl                                    ][25/02  8:29],success
95553c5365dafa4b0000000000000013,2024-02-25T16:29:41+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                   (0*$mycli)  1$ ~  2$ docker  3$ ..641bec/result  4$ ..ensic_etl/gen  5$ vim  6-$ ~/forensic_etl  7$ ~/forensic_etl                                    ][25/02  8:29],success
95553c5365dafa4b0000000000000013,2024-02-25T16:29:42+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,q,success
95553c5365dafa4b0000000000000013,2024-02-25T16:29:52+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,AAAAA,success
95553c5365dafa4b0000000000000013,2024-02-25T16:29:56+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,Bq,success
95553c5365dafa4b0000000000000013,2024-02-25T16:29:59+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,AAB,success
95553c5365dafa4b0000000000000013,2024-02-25T16:30:35+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,200296158 \G,success
95553c5365dafa4b0000000000000013,2024-02-25T16:35:56+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,q,success
95553c5365dafa4b0000000000000013,2024-02-25T16:36:08+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,A,success
95553c5365dafa4b0000000000000013,2024-02-25T16:36:11+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,AA,success
95553c5365dafa4b0000000000000013,2024-02-25T16:36:15+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,use soc,success
95553c5365dafa4b0000000000000013,2024-02-25T16:36:16+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,A,success
95553c5365dafa4b0000000000000013,2024-02-25T16:36:17+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,;,success
95553c5365dafa4b0000000000000013,2024-02-25T16:36:21+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,q,success
058a683565dbee870000000000000019,2024-02-26T09:51:11+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
058a683565dbee870000000000000019,2024-02-26T09:52:06+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,use proamsdwdb;,success
058a683565dbee870000000000000019,2024-02-26T09:52:26+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
058a683565dbee870000000000000019,2024-02-26T09:52:26+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where f_state in(3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
058a683565dbee870000000000000019,2024-02-26T09:52:26+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,group by f_currency;,success
058a683565dbee870000000000000019,2024-02-26T10:39:26+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,exit,success
058a683565dbee870000000000000019,2024-02-26T10:39:27+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,exit,success
cb031e7865dc4dec000000000000000d,2024-02-26T16:38:07+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
cb031e7865dc4dec000000000000000d,2024-02-26T16:38:26+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,use huobi,success
cb031e7865dc4dec000000000000000d,2024-02-26T16:40:00+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select count(*) from deposit_history where user_id = 188620;,success
cb031e7865dc4dec000000000000000d,2024-02-26T16:40:10+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,desc deposit_history;,success
cb031e7865dc4dec000000000000000d,2024-02-26T16:43:26+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from deposit_history where user_id = 188620;,success
cb031e7865dc4dec000000000000000d,2024-02-26T16:44:14+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,show create table deposit_history;,success
cb031e7865dc4dec000000000000000d,2024-02-26T16:45:22+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from deposit_history where user_id = 188620 and status=1;,success
df56202f65dd29f0000000000000000d,2024-02-27T08:16:51+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,cd /tmp,success
df56202f65dd29f0000000000000000d,2024-02-27T08:16:52+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ll,success
df56202f65dd29f0000000000000000d,2024-02-27T08:16:58+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com huobi -e "select * from deposit_history where user_id = 188620 and status = 1;" > uid_1886205.tsv,success
df56202f65dd29f0000000000000000d,2024-02-27T08:17:07+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ll,success
df56202f65dd29f0000000000000000d,2024-02-27T08:17:11+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,less uid_1886205.tsv,success
df56202f65dd29f0000000000000000d,2024-02-27T08:17:49+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,  qcat uid_1886205.tsv | sed 's/\t/,/g' > uid_1886205.csv,success
df56202f65dd29f0000000000000000d,2024-02-27T08:17:56+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,less uid_1886205.csv,success
df56202f65dd29f0000000000000000d,2024-02-27T08:18:31+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com huobi,success
df56202f65dd29f0000000000000000d,2024-02-27T08:18:44+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,show tables;,success
df56202f65dd29f0000000000000000d,2024-02-27T08:21:03+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select *, a.from_unixtime(deposit_time), a.from_unixtime(finish_time) from deposit_history a where user_id = 188620 and status = 1;,success
df56202f65dd29f0000000000000000d,2024-02-27T08:21:34+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select *, from_unixtime(a.deposit_time), from_unixtime(a.finish_time) from deposit_history a where user_id = 188620 and status = 1;,success
df56202f65dd29f0000000000000000d,2024-02-27T08:22:14+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,quit,success
df56202f65dd29f0000000000000000d,2024-02-27T08:22:15+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ll,success
df56202f65dd29f0000000000000000d,2024-02-27T08:22:37+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com huobi -e "select *, from_unixtime(a.deposit_time), from_unixtime(a.finish_time) from deposit_history a where user_id = 188620 and status = 1;" > uid_1886205.tsv,success
df56202f65dd29f0000000000000000d,2024-02-27T08:22:50+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,cat uid_1886205.tsv | sed 's/\t/,/g' > uid_1886205.csv,success
df56202f65dd29f0000000000000000d,2024-02-27T08:22:56+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,less uid_1886205.csv,success
df56202f65dd29f0000000000000000d,2024-02-27T08:25:29+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,BBBBBAAjjjkkkkkjjjjjjqll,success
df56202f65dd29f0000000000000000d,2024-02-27T08:25:57+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,wc -l uid_1886205.csv,success
b440e03d65dd5136000000000000000d,2024-02-27T11:04:28+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com huobi,success
b440e03d65dd5136000000000000000d,2024-02-27T11:04:57+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,use hbg_trade,success
b440e03d65dd5136000000000000000d,2024-02-27T11:05:01+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,show tables;,success
b440e03d65dd5136000000000000000d,2024-02-27T11:06:43+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,show databases like '%trade%';,success
b440e03d65dd5136000000000000000d,2024-02-27T11:07:01+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,show databases like '%hbg%';,success
b440e03d65dd5136000000000000000d,2024-02-27T11:07:09+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,use hbgorderdb,success
b440e03d65dd5136000000000000000d,2024-02-27T11:07:13+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,show tables;,success
b440e03d65dd5136000000000000000d,2024-02-27T11:07:35+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,desc t_point_fee_manage_item;,success
b440e03d65dd5136000000000000000d,2024-02-27T11:07:55+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ALTER TABLE t_point_fee_manage_item   ,success
b440e03d65dd5136000000000000000d,2024-02-27T11:07:57+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ADD COLUMN f_state TINYINT(3) NOT NULL DEFAULT '1' COMMENT '状态 0:未处理 1:已收取 2:已触达';,success
b440e03d65dd5136000000000000000d,2024-02-27T11:08:04+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,desc t_point_fee_manage_item;,success
3c11a5a165dd7ce9000000000000000d,2024-02-27T14:10:55+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com huobi,success
3c11a5a165dd7ce9000000000000000d,2024-02-27T14:12:16+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select count(*) from dep_hi where user_id in (30564, 352625, 455283, 479925, 480209, 516321, 529487, 530872, 543915, 551173, 552677, 554608, 554856, 555110, 560517, 561451, 564575, 565439, 575884, 586822, 603161, 618371, 634247, 677175, 690058, 695446, 712233, 721572, 790804, 884823, 1081626, 1087125, 1087468, 1185769, 1285025, 1422735, 1540762, 1621051, 1787857, 2256500, 2289628, 5685394, 6104688, 9583119, ********, ********, ********, ********);,success
3c11a5a165dd7ce9000000000000000d,2024-02-27T14:14:37+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from deposit_history where user_id in (30564, 352625, 455283, 479925, 480209, 516321, 529487, 530872, 543915, 551173, 552677, 554608, 554856, 555110, 560517, 561451, 564575, 565439, 575884, 586822, 603161, 618371, 634247, 677175, 690058, 695446, 712233, 721572, 790804, 884823, 1081626, 1087125, 1087468, 1185769, 1285025, 1422735, 1540762, 1621051, 1787857, 2256500, 2289628, 5685394, 6104688, 9583119, ********, ********, ********, ********);,success
3c11a5a165dd7ce9000000000000000d,2024-02-27T14:15:44+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,count(*), user_id group by user_id,success
3c11a5a165dd7ce9000000000000000d,2024-02-27T14:20:53+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,quit,success
3c11a5a165dd7ce9000000000000000d,2024-02-27T14:20:56+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,cd /tmp,success
3c11a5a165dd7ce9000000000000000d,2024-02-27T14:20:58+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ll,success
3c11a5a165dd7ce9000000000000000d,2024-02-27T14:21:05+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,rm -f uid_1886205.*,success
3c11a5a165dd7ce9000000000000000d,2024-02-27T14:21:05+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ll,success
3c11a5a165dd7ce9000000000000000d,2024-02-27T14:24:06+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com huobi -e "select count(*), user_id from deposit_history where user_id in (30564, 352625, 455283, 479925, 480209, 516321, 529487, 530872, 543915, 551173, 552677, 554608, 554856, 555110, 560517, 561451, 564575, 565439, 575884, 586822, 603161, 618371, 634247, 677175, 690058, 695446, 712233, 721572, 790804, 884823, 1081626, 1087125, 1087468, 1185769, 1285025, 1422735, 1540762, 1621051, 1787857, 2256500, 2289628, 5685394, 6104688, 9583119, ********, ********, ********, ********) group by user_id;" > baise_7-8.tsv,success
3c11a5a165dd7ce9000000000000000d,2024-02-27T14:24:14+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ll,success
3c11a5a165dd7ce9000000000000000d,2024-02-27T14:25:05+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,cat baise_7-8.tsv | sed 's/\t/,/g' > baise_7-8.csv,success
3c11a5a165dd7ce9000000000000000d,2024-02-27T14:25:09+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,less baise_7-8.csv,success
3c11a5a165dd7ce9000000000000000d,2024-02-27T17:08:31+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,rm ball,success
3c11a5a165dd7ce9000000000000000d,2024-02-27T17:08:39+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,rm baise_7-8.* -f,success
3c11a5a165dd7ce9000000000000000d,2024-02-27T17:08:40+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ll,success
3c11a5a165dd7ce9000000000000000d,2024-02-27T17:11:47+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com huobi -e "select user_id,channel_id,account,channel_account from my_channel where user_id in (352625, 455283, 543915, 575884, 690058, 677175, 561451, 564575, 30564, 721572, 1087125, 1540762, 618371, 1285025, 1422735, 479925, 1081626, 1087468, user id, 884823, 530872, 480209, 529487, 560517, 554856, 1185769, 58    6822, 1787857, ********, ********, 5685394, 790804, 695446, 603161, 552677, 634247, 9583119, 551173, 6104688, 712233, 2289628, 516321, 554608, 1621051, 565439, ********, ********, 2256500, 555110);" > baise_7-8_bank_20240227.tsv,success
3c11a5a165dd7ce9000000000000000d,2024-02-27T17:13:00+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com huobi -e "select user_id,channel_id,account,channel_account from my_channel where user_id in (352625, 455283, 543915, 575884, 690058, 677175, 561451, 564575, 30564, 721572, 1087125, 1540762, 618371, 1285025, 1422735, 479925, 1081626, 1087468, user id, 884823, 530872, 480209, 529487, 560517, 554856, 1185769, 586822, 1787857, ********, ********, 5685394, 790804, 695446, 603161, 552677, 634247, 9583119, 551173, 6104688, 712233, 2289628, 516321, 554608, 1621051, 565439, ********, ********, 2256500, 555110);" > baise_7-8_bank_20240227.tsv,success
d8f5eb1a65ddbd2b000000000000000d,2024-02-27T18:45:02+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,cd /tmp,success
d8f5eb1a65ddbd2b000000000000000d,2024-02-27T18:45:03+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,,success
d8f5eb1a65ddbd2b000000000000000d,2024-02-27T18:45:33+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com huobi -e "select user_id,channel_id,account,channel_account from my_channel where user_id in (352625, 455283, 543915, 575884, 690058, 677175, 561451, 564575, 30564, 721572, 1087125, 1540762, 618371, 1285025, 1422735, 479925, 1081626, 1087468, 884823, 530872, 480209, 529487, 560517, 554856, 1185769, 586822, 1787857, ********, ********, 5685394, 790804, 695446, 603161, 552677, 634247, 9583119, 551173, 6104688, 712233, 2289628, 516321, 554608, 1621051, 565439, ********, ********, 2256500, 555110);" > baise_7-8_bank_20240227.tsv,success
d8f5eb1a65ddbd2b000000000000000d,2024-02-27T18:45:42+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,ll,success
d8f5eb1a65ddbd2b000000000000000d,2024-02-27T18:45:48+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,less baise_7-8_bank_20240227.tsv,success
d8f5eb1a65ddbd2b000000000000000d,2024-02-27T18:47:56+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,qmysql -u superdba -P 3614 -p -h master-3614.docsl.com huobi -e "select * from deposit_history where user_id in (352625, 455283, 543915, 575884, 690058, 677175, 561451, 564575, 30564, 721572, 1087125, 1540762, 618371, 1285025, 1422735, 479925, 1081626, 1087468, 884823, 530872, 480209, 529487, 560517, 554856, 1185769, 586822, 1787857, ********, ********, 5685394, 790804, 695446, 603161, 552677, 634247, 9583119, 551173, 6104688, 712233, 2289628, 516321, 554608, 1621051, 565439, ********, ********, 2256500, 555110);" > baise_7-8_deposit_20240227.tsv,success
d8f5eb1a65ddbd2b000000000000000d,2024-02-27T18:48:07+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,ll,success
d8f5eb1a65ddbd2b000000000000000d,2024-02-27T18:48:20+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com huobi -e "select * from withdraw_history where user_id in (352625, 455283, 543915, 575884, 690058, 677175, 561451, 564575, 30564, 721572, 1087125, 1540762, 618371, 1285025, 1422735, 479925, 1081626, 1087468, 884823, 530872, 480209, 529487, 560517, 554856, 1185769, 586822, 1787857, ********, ********, 5685394, 790804, 695446, 603161, 552677, 634247, 9583119, 551173, 6104688, 712233, 2289628, 516321, 554608, 1621051, 565439, ********, ********, 2256500, 555110);" > baise_7-8_withdraw_20240227.tsv,success
d8f5eb1a65ddbd2b000000000000000d,2024-02-27T18:48:29+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,ll,success
d8f5eb1a65ddbd2b000000000000000d,2024-02-27T18:48:56+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,cat baise_7-8_bank_20240227.tsv | sed 's/\t/,/g' > baise_7-8_bank_20240227.csv,success
d8f5eb1a65ddbd2b000000000000000d,2024-02-27T18:49:22+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,cat baise_7-8_deposit_20240227.tsv | sed 's/\t/,/g' > baise_7-8_deposit_20240227.csv,success
d8f5eb1a65ddbd2b000000000000000d,2024-02-27T18:49:45+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,cat baise_7-8_withdraw_20240227.tsv | sed 's/\t/,/g' > baise_7-8_withdraw_20240227.csv,success
d8f5eb1a65ddbd2b000000000000000d,2024-02-27T18:50:17+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,ll,success
d8f5eb1a65ddbd2b000000000000000d,2024-02-27T18:50:43+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,rm -f *.tsv,success
d8f5eb1a65ddbd2b000000000000000d,2024-02-27T18:50:44+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,ll,success
d8f5eb1a65ddbd2b000000000000000d,2024-02-27T18:51:25+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,tar zcvf baise_7-8.tar.gz baise_7-8_bank_20240227.csv baise_7-8_deposit_20240227.csv baise_7-8_withdraw_20240227.csv,success
d8f5eb1a65ddbd2b000000000000000d,2024-02-27T18:51:27+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,ll,success
d8f5eb1a65ddbd2b000000000000000d,2024-02-27T18:51:44+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,ossutil cp baise_7-8.tar.gz  oss://miniglobal-devops/hue_log/,success
d8f5eb1a65ddbd2b000000000000000d,2024-02-27T18:55:01+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,ll,success
d8f5eb1a65ddbd2b000000000000000d,2024-02-27T18:55:09+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,rm baise_7-8* -f,success
d8f5eb1a65ddbd2b000000000000000d,2024-02-27T18:55:10+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,ll,success
adce3fe365de94e10000000000000019,2024-02-28T10:05:28+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
adce3fe365de94e10000000000000019,2024-02-28T10:05:48+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,use proamsdwdb;,success
adce3fe365de94e10000000000000019,2024-02-28T10:05:58+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
adce3fe365de94e10000000000000019,2024-02-28T10:05:58+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,where f_state in(3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
adce3fe365de94e10000000000000019,2024-02-28T10:05:58+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,group by f_currency;,success
010f887165de9866000000000000000d,2024-02-28T10:20:33+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,cd /hbdata/soft/miniglobal-apps/apps/miniglobal/miniglobal-hbg-account-manage/,success
010f887165de9866000000000000000d,2024-02-28T10:20:34+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,ll,success
010f887165de9866000000000000000d,2024-02-28T10:20:51+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,rm market.json,success
010f887165de9866000000000000000d,2024-02-28T10:21:04+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,rm -rf k8s_resources/,success
010f887165de9866000000000000000d,2024-02-28T10:21:07+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,vim README.md,success
010f887165de9866000000000000000d,2024-02-28T10:21:17+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,:q,success
010f887165de9866000000000000000d,2024-02-28T10:21:20+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,vim Makefile,success
010f887165de9866000000000000000d,2024-02-28T10:22:05+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,:wq,success
010f887165de9866000000000000000d,2024-02-28T10:22:08+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,ll,success
010f887165de9866000000000000000d,2024-02-28T10:22:13+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,cd build_command_pkgs/,success
010f887165de9866000000000000000d,2024-02-28T10:22:14+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,ll,success
010f887165de9866000000000000000d,2024-02-28T10:22:31+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,rm -rf hbg-account-manage,success
010f887165de9866000000000000000d,2024-02-28T10:22:34+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,cd ..,success
010f887165de9866000000000000000d,2024-02-28T10:22:43+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,make build TAG=PRD_T20240228,success
65c39aa765de9c12000000000000000d,2024-02-28T10:36:05+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,cd /tmp,success
65c39aa765de9c12000000000000000d,2024-02-28T10:36:06+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ll,success
65c39aa765de9c12000000000000000d,2024-02-28T10:36:49+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com hbgorderdb -e "select a.*, from_unixtime(f_created_at/1000, '%Y-%m-%d %h:%i:%s') as create_time, from_unixtime(f_updated_at/1000, '%Y-%m-%d %h:%i:%s') as update_time from t_point_buy_back a;" > hbgorderdb.t_point_buy_back_20240228.tsv,success
65c39aa765de9c12000000000000000d,2024-02-28T10:37:01+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ll,success
65c39aa765de9c12000000000000000d,2024-02-28T10:37:25+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,cat hbgorderdb.t_point_buy_back_20240228.tsv | sed 's/\t/,/g' > hbgorderdb.t_point_buy_back_20240228.csv,success
65c39aa765de9c12000000000000000d,2024-02-28T10:37:28+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ll,success
65c39aa765de9c12000000000000000d,2024-02-28T10:37:34+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ll -h,success
65c39aa765de9c12000000000000000d,2024-02-28T10:38:01+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,tar zcvf point.tar.gz hbgorderdb.t_point_buy_back_20240228.csv,success
65c39aa765de9c12000000000000000d,2024-02-28T10:38:05+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ll -h,success
65c39aa765de9c12000000000000000d,2024-02-28T10:38:19+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ossutil cp point.tar.gz oss://miniglobal-devops/hue_log/,success
65c39aa765de9c12000000000000000d,2024-02-28T10:40:28+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ll,success
65c39aa765de9c12000000000000000d,2024-02-28T10:40:34+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,rm point.tar.gz,success
65c39aa765de9c12000000000000000d,2024-02-28T10:40:39+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,y,success
65c39aa765de9c12000000000000000d,2024-02-28T10:40:44+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,rm -f ht,success
65c39aa765de9c12000000000000000d,2024-02-28T10:40:47+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,less hbgorderdb.t_point_buy_back_20240228.csv,success
65c39aa765de9c12000000000000000d,2024-02-28T10:41:07+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,qll,success
65c39aa765de9c12000000000000000d,2024-02-28T10:41:11+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,rm hbgorderdb.t_point_buy_back_20240228.csv,success
65c39aa765de9c12000000000000000d,2024-02-28T10:41:13+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,y,success
65c39aa765de9c12000000000000000d,2024-02-28T10:41:15+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ll,success
6150f23d65de9f03000000000000000d,2024-02-28T10:48:52+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,cd /hbdata/soft/miniglobal-apps/apps/miniglobal/miniglobal-profit-web/,success
6150f23d65de9f03000000000000000d,2024-02-28T10:48:53+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,ll,success
6150f23d65de9f03000000000000000d,2024-02-28T10:48:58+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,rm market.json,success
6150f23d65de9f03000000000000000d,2024-02-28T10:49:03+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,rm -r k8s_resources/,success
6150f23d65de9f03000000000000000d,2024-02-28T10:49:09+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,vim README.md,success
6150f23d65de9f03000000000000000d,2024-02-28T10:49:17+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,:wq,success
6150f23d65de9f03000000000000000d,2024-02-28T10:49:21+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,vim Makefile,success
6150f23d65de9f03000000000000000d,2024-02-28T10:49:30+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,:wq,success
6150f23d65de9f03000000000000000d,2024-02-28T10:49:35+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,ll,success
6150f23d65de9f03000000000000000d,2024-02-28T10:49:35+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,cd build_command_pkgs/,success
6150f23d65de9f03000000000000000d,2024-02-28T10:49:41+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,rm -r miniglobal-profit,success
6150f23d65de9f03000000000000000d,2024-02-28T10:49:44+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,cd ..,success
6150f23d65de9f03000000000000000d,2024-02-28T10:49:45+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,ll,success
6150f23d65de9f03000000000000000d,2024-02-28T10:49:58+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,make build TAG=PRD_T20240228,success
6150f23d65de9f03000000000000000d,2024-02-28T10:51:09+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,less build.sh,success
8a454ca465dea05a000000000000000d,2024-02-28T10:55:02+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
8a454ca465dea05a000000000000000d,2024-02-28T10:55:36+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,use hbgorderdb,success
8a454ca465dea05a000000000000000d,2024-02-28T10:55:40+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,show tables;,success
8a454ca465dea05a000000000000000d,2024-02-28T10:56:26+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,desc t_point_buy_back;,success
8a454ca465dea05a000000000000000d,2024-02-28T10:56:56+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_buy_back where f_u_id = 19654085;,success
8a454ca465dea05a000000000000000d,2024-02-28T10:57:33+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select count(*) from t_point_buy_back;,success
8a454ca465dea05a000000000000000d,2024-02-28T10:57:55+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select count(*) from t_point_buy_back where f_state=0;,success
8a454ca465dea05a000000000000000d,2024-02-28T11:01:19+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_buy_back where f_u_id = 19654085;,success
8a454ca465dea05a000000000000000d,2024-02-28T11:01:46+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_buy_back where f_u_id = 19654085;,success
8a454ca465dea05a000000000000000d,2024-02-28T11:02:19+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_buy_back where f_u_id = 19654085;,success
ecb2d08f65dea2fd000000000000000d,2024-02-28T11:05:46+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,cd /hbdata/soft/miniglobal-apps/apps/miniglobal/miniglobal-profit-web/,success
ecb2d08f65dea2fd000000000000000d,2024-02-28T11:05:47+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,ll,success
ecb2d08f65dea2fd000000000000000d,2024-02-28T11:06:28+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,make build TAG=PRD_T20240228_1,success
8a454ca465dea05a000000000000000d,2024-02-28T11:07:59+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_buy_back where f_u_id = 19654085;,success
8a454ca465dea05a000000000000000d,2024-02-28T11:20:30+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,show tables;,success
8a454ca465dea05a000000000000000d,2024-02-28T11:20:41+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,desc t_point_buy_back_itemdesc t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T11:20:54+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T11:28:23+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T11:30:35+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,use hbg_trade,success
8a454ca465dea05a000000000000000d,2024-02-28T11:30:38+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,show tables;,success
8a454ca465dea05a000000000000000d,2024-02-28T11:30:47+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,desc t_hbg_message;,success
8a454ca465dea05a000000000000000d,2024-02-28T11:30:59+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_hbg_message order by f_id desc limit 1;,success
8a454ca465dea05a000000000000000d,2024-02-28T11:33:51+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_hbg_message order by f_id desc limit 1;,success
ecb2d08f65dea2fd000000000000000d,2024-02-28T11:40:28+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,cd ../miniglobal-hbg-account-manage/,success
ecb2d08f65dea2fd000000000000000d,2024-02-28T11:40:39+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,make build TAG=PRD_T20240228_1,success
8a454ca465dea05a000000000000000d,2024-02-28T11:42:25+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_hbg_message order by f_id desc limit 1;,success
8a454ca465dea05a000000000000000d,2024-02-28T11:44:03+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,use hbgorderdb,success
8a454ca465dea05a000000000000000d,2024-02-28T11:44:06+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,show tables;,success
8a454ca465dea05a000000000000000d,2024-02-28T11:44:17+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T11:44:42+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,update t_point_fee_manage_item set f_state=1 where f_id=1;,success
8a454ca465dea05a000000000000000d,2024-02-28T11:44:44+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T11:45:09+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T11:45:55+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T11:46:07+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T11:46:13+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T11:46:34+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T11:46:46+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T11:46:56+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
ecb2d08f65dea2fd000000000000000d,2024-02-28T11:47:22+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,cd -,success
ecb2d08f65dea2fd000000000000000d,2024-02-28T11:47:29+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,make build TAG=PRD_T20240228_2,success
8a454ca465dea05a000000000000000d,2024-02-28T11:48:49+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T11:49:00+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
ecb2d08f65dea2fd000000000000000d,2024-02-28T11:55:13+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,make build TAG=PRD_T20240228_3,success
8a454ca465dea05a000000000000000d,2024-02-28T11:56:52+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,update t_point_fee_manage_item set f_state=1 where f_id=1;,success
8a454ca465dea05a000000000000000d,2024-02-28T11:56:54+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T11:56:57+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T11:57:06+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T11:57:50+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,use hbg_trade,success
8a454ca465dea05a000000000000000d,2024-02-28T11:57:54+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,show tables;,success
8a454ca465dea05a000000000000000d,2024-02-28T11:58:24+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_hbg_message order by f_id desc limit 1;,success
ecb2d08f65dea2fd000000000000000d,2024-02-28T12:00:19+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-deploy,***********,make build TAG=PRD_T20240228_4,success
8a454ca465dea05a000000000000000d,2024-02-28T12:06:42+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,use hbgorderdb,success
8a454ca465dea05a000000000000000d,2024-02-28T12:06:49+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T12:06:59+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,update t_point_fee_manage_item set f_state=1 where f_id=1;,success
8a454ca465dea05a000000000000000d,2024-02-28T12:07:02+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T12:07:10+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T12:07:15+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T12:07:19+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T12:07:21+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T12:07:32+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T12:07:39+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T12:07:51+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T12:08:01+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T12:08:12+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T12:08:24+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T12:08:53+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T12:09:37+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T12:09:42+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T12:09:49+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T12:10:00+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T12:10:05+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T12:10:20+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T12:10:36+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T12:10:37+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T12:10:47+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T12:10:51+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T12:11:01+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T12:11:27+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from hbg_trade.t_hbg_message order by f_id desc limit 2;,success
8a454ca465dea05a000000000000000d,2024-02-28T12:12:24+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T12:13:21+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
8a454ca465dea05a000000000000000d,2024-02-28T12:13:36+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,select * from hbg_trade.t_hbg_message order by f_id desc limit 2;,success
8a454ca465dea05a000000000000000d,2024-02-28T12:37:25+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,use hbgorderdb,success
8a454ca465dea05a000000000000000d,2024-02-28T12:37:38+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,desc t_point_fee_manage_item;,success
2587d64165def46d000000000000000d,2024-02-28T16:53:10+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-deploy,***********,cd /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-hbg-encn/,success
2587d64165def46d000000000000000d,2024-02-28T16:54:23+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-deploy,***********,make build TAG=PRD_T402281,success
2587d64165def46d000000000000000d,2024-02-28T16:54:29+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-deploy,***********,ll,success
2587d64165def46d000000000000000d,2024-02-28T16:55:37+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-deploy,***********,vim R,success
2587d64165def46d000000000000000d,2024-02-28T16:55:43+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-deploy,***********,:q,success
2587d64165def46d000000000000000d,2024-02-28T16:55:48+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-deploy,***********,less README.md,success
2587d64165def46d000000000000000d,2024-02-28T16:56:10+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-deploy,***********,make build TAG=PRD_T402281,success
2587d64165def46d000000000000000d,2024-02-28T16:56:19+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-deploy,***********,vim Makefile,success
2587d64165def46d000000000000000d,2024-02-28T16:56:55+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-deploy,***********,ll,success
2587d64165def46d000000000000000d,2024-02-28T16:56:57+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-deploy,***********,vim README.md,success
2587d64165def46d000000000000000d,2024-02-28T16:57:04+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-deploy,***********,:q,success
2587d64165def46d000000000000000d,2024-02-28T16:57:09+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-deploy,***********,rm market.json,success
2587d64165def46d000000000000000d,2024-02-28T16:57:14+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-deploy,***********,rm -r k8s_resources/,success
2587d64165def46d000000000000000d,2024-02-28T16:57:17+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-deploy,***********,ll,success
2587d64165def46d000000000000000d,2024-02-28T16:57:24+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-deploy,***********,cd build_command_pkgs/,success
2587d64165def46d000000000000000d,2024-02-28T16:57:24+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-deploy,***********,ll,success
2587d64165def46d000000000000000d,2024-02-28T16:57:34+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-deploy,***********,cd ..,success
2587d64165def46d000000000000000d,2024-02-28T16:57:41+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-deploy,***********,make build TAG=PRD_T402281,success
f9362abe65def5a0000000000000000d,2024-02-28T16:58:10+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
f9362abe65def5a0000000000000000d,2024-02-28T16:58:30+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,use hbgorderdb,success
f9362abe65def5a0000000000000000d,2024-02-28T16:58:33+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,show tables;,success
f9362abe65def5a0000000000000000d,2024-02-28T16:58:50+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
f9362abe65def5a0000000000000000d,2024-02-28T16:59:14+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,update t_point_fee_manage_item set f_state=1 where f_id=1;,success
f9362abe65def5a0000000000000000d,2024-02-28T16:59:17+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
f9362abe65def5a0000000000000000d,2024-02-28T16:59:24+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
f9362abe65def5a0000000000000000d,2024-02-28T16:59:35+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
f9362abe65def5a0000000000000000d,2024-02-28T16:59:51+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
f9362abe65def5a0000000000000000d,2024-02-28T17:00:00+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
2587d64165def46d000000000000000d,2024-02-28T17:03:57+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-deploy,***********,cd ../..,success
2587d64165def46d000000000000000d,2024-02-28T17:04:00+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-deploy,***********,ll,success
2587d64165def46d000000000000000d,2024-02-28T17:04:03+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-deploy,***********,cd ..,success
2587d64165def46d000000000000000d,2024-02-28T17:04:04+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-deploy,***********,ll,success
2587d64165def46d000000000000000d,2024-02-28T17:04:11+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-deploy,***********,git add .,success
2587d64165def46d000000000000000d,2024-02-28T17:04:56+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-deploy,***********,git commit -m "Update",success
2587d64165def46d000000000000000d,2024-02-28T17:05:05+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-deploy,***********,git push,success
2587d64165def46d000000000000000d,2024-02-28T17:05:29+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-deploy,***********,cd apps/miniglobal/fed-hbg-encn/,success
2587d64165def46d000000000000000d,2024-02-28T17:05:30+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-deploy,***********,ll,success
f9362abe65def5a0000000000000000d,2024-02-28T17:10:07+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
f9362abe65def5a0000000000000000d,2024-02-28T17:10:20+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,update t_point_fee_manage_item set f_state=1 where f_id=1;,success
f9362abe65def5a0000000000000000d,2024-02-28T17:10:24+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
f9362abe65def5a0000000000000000d,2024-02-28T17:10:36+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
f9362abe65def5a0000000000000000d,2024-02-28T17:10:50+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
f9362abe65def5a0000000000000000d,2024-02-28T17:11:15+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,select * from t_point_fee_manage_item;,success
f9362abe65def5a0000000000000000d,2024-02-28T17:16:53+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,quit,success
89b6cd8965dfcaf7000000000000000d,2024-02-29T08:08:27+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd /var/log/hue,success
89b6cd8965dfcaf7000000000000000d,2024-02-29T08:08:28+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
89b6cd8965dfcaf7000000000000000d,2024-02-29T08:09:03+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-bigdata-datanode-1c-02,172.28.3.8,tar zcvf /tmp/runcpserver.log.20240229-02.tar.gz runcpserver.log runcpserver.log.1 runcpserver.log.2,success
82860f1865dfcb27000000000000000d,2024-02-29T08:09:17+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-bigdata-datanode-1c-03,172.28.3.121,cd /var/log/hue,success
82860f1865dfcb27000000000000000d,2024-02-29T08:09:18+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-bigdata-datanode-1c-03,172.28.3.121,ll,success
82860f1865dfcb27000000000000000d,2024-02-29T08:09:35+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-bigdata-datanode-1c-03,172.28.3.121,tar zcvf /tmp/runcpserver.log.20240229-03.tar.gz runcpserver.log runcpserver.log.1 runcpserver.log.2,success
8aa7643f65dfcb46000000000000000d,2024-02-29T08:09:46+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-bigdata-datanode-1c-05,172.28.3.93,cd /var/log/hue,success
8aa7643f65dfcb46000000000000000d,2024-02-29T08:09:47+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-bigdata-datanode-1c-05,172.28.3.93,ll,success
8aa7643f65dfcb46000000000000000d,2024-02-29T08:10:09+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-bigdata-datanode-1c-05,172.28.3.93,tar zcvf /tmp/runcpserver.log.20240229-05.tar.gz runcpserver.log runcpserver.log.1,success
5dfc5ae265dfcb67000000000000000d,2024-02-29T08:10:17+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,cd /tmp,success
5dfc5ae265dfcb67000000000000000d,2024-02-29T08:10:26+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ll,success
5dfc5ae265dfcb67000000000000000d,2024-02-29T08:10:30+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,scp 172.28.3.8:/tmp/runcpserver.log.20240229-02.tar.gz .,success
5dfc5ae265dfcb67000000000000000d,2024-02-29T08:10:38+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,scp 172.28.3.121:/tmp/runcpserver.log.20240229-03.tar.gz .,success
5dfc5ae265dfcb67000000000000000d,2024-02-29T08:10:49+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,scp 172.28.3.93:/tmp/runcpserver.log.20240229-05.tar.gz .,success
5dfc5ae265dfcb67000000000000000d,2024-02-29T08:11:02+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ossutil cp runcpserver.log.20240229-02.tar.gz oss://miniglobal-devops/hue_log/,success
5dfc5ae265dfcb67000000000000000d,2024-02-29T08:11:10+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ossutil cp runcpserver.log.20240229-03.tar.gz oss://miniglobal-devops/hue_log/,success
5dfc5ae265dfcb67000000000000000d,2024-02-29T08:11:18+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ossutil cp runcpserver.log.20240229-05.tar.gz oss://miniglobal-devops/hue_log/,success
5dfc5ae265dfcb67000000000000000d,2024-02-29T08:14:32+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ll,success
5dfc5ae265dfcb67000000000000000d,2024-02-29T08:14:37+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,rm -f runcpserver.log.20240229-0*,success
5dfc5ae265dfcb67000000000000000d,2024-02-29T08:14:40+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ll,success
9b1fff0265dfcc75000000000000000d,2024-02-29T08:14:49+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-bigdata-datanode-1c-02,172.28.3.8,rm -f /tmp/runcpserver.log.20240229-02.tar.gz,success
a35ece2565dfcc7d000000000000000d,2024-02-29T08:14:57+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-bigdata-datanode-1c-03,172.28.3.121,rm -f /tmp/runcpserver.log.20240229-03.tar.gz,success
57f2514e65dfcc86000000000000000d,2024-02-29T08:15:08+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-bigdata-datanode-1c-05,172.28.3.93,rm -f /tmp/runcpserver.log.20240229-05.tar.gz,success
e75412e565dff453000000000000000d,2024-02-29T11:05:21+08:00,cmd.Command,avenir_user01,223.104.41.167,miniglobal-devops,***********,mysql -p -P3614 -h 172.28.5.15 -usuperdba,success
e75412e565dff453000000000000000d,2024-02-29T11:05:32+08:00,cmd.Command,avenir_user01,223.104.41.167,miniglobal-devops,***********,use huobi,success
e75412e565dff453000000000000000d,2024-02-29T11:05:45+08:00,cmd.Command,avenir_user01,223.104.41.167,miniglobal-devops,***********,show tables like '%match%';,success
e75412e565dff453000000000000000d,2024-02-29T11:06:10+08:00,cmd.Command,avenir_user01,223.104.41.167,miniglobal-devops,***********,select * from match_result limit 5;,success
e75412e565dff453000000000000000d,2024-02-29T11:06:57+08:00,cmd.Command,avenir_user01,223.104.41.167,miniglobal-devops,***********,select * from match_result where order_buy_user_id = 30564;,success
e75412e565dff453000000000000000d,2024-02-29T11:07:20+08:00,cmd.Command,avenir_user01,223.104.41.167,miniglobal-devops,***********,show create table match_result;,success
e75412e565dff453000000000000000d,2024-02-29T11:08:07+08:00,cmd.Command,avenir_user01,223.104.41.167,miniglobal-devops,***********,show tables like '%order%';,success
e75412e565dff453000000000000000d,2024-02-29T11:08:25+08:00,cmd.Command,avenir_user01,223.104.41.167,miniglobal-devops,***********,show create table order_history;,success
e75412e565dff453000000000000000d,2024-02-29T11:09:00+08:00,cmd.Command,avenir_user01,223.104.41.167,miniglobal-devops,***********,select * from order_history where user_id = 30564;,success
e75412e565dff453000000000000000d,2024-02-29T11:09:16+08:00,cmd.Command,avenir_user01,223.104.41.167,miniglobal-devops,***********,select * from order_history limit 1;,success
e75412e565dff453000000000000000d,2024-02-29T11:11:15+08:00,cmd.Command,avenir_user01,223.104.41.167,miniglobal-devops,***********,select * from match_result where order_sell_user_id = 30564;,success
e75412e565dff453000000000000000d,2024-02-29T11:13:07+08:00,cmd.Command,avenir_user01,223.104.41.167,miniglobal-devops,***********,select * from match_result where order_sell_user_id = 30564 group by input_order_type;,success
e75412e565dff453000000000000000d,2024-02-29T11:14:41+08:00,cmd.Command,avenir_user01,223.104.41.167,miniglobal-devops,***********,select sum(processed_amount), input_order_type, from_unixtime(processed_time) from match_result where order_sell_user_id = 30564 group by input_order_type;,success
e75412e565dff453000000000000000d,2024-02-29T11:21:41+08:00,cmd.Command,avenir_user01,223.104.41.167,miniglobal-devops,***********,quit,success
e75412e565dff453000000000000000d,2024-02-29T11:21:44+08:00,cmd.Command,avenir_user01,223.104.41.167,miniglobal-devops,***********,cd /tmp,success
e75412e565dff453000000000000000d,2024-02-29T11:21:45+08:00,cmd.Command,avenir_user01,223.104.41.167,miniglobal-devops,***********,ll,success
e75412e565dff453000000000000000d,2024-02-29T11:22:06+08:00,cmd.Command,avenir_user01,223.104.41.167,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com huobi -e "select a.*, from_unixtime(a.processed_time) as time from match_result a where order_sell_user_id = 30564;" > order_sell_user_id_30564.tsv,success
e75412e565dff453000000000000000d,2024-02-29T11:22:13+08:00,cmd.Command,avenir_user01,223.104.41.167,miniglobal-devops,***********,ll,success
e75412e565dff453000000000000000d,2024-02-29T11:22:25+08:00,cmd.Command,avenir_user01,223.104.41.167,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com huobi -e "select a.*, from_unixtime(a.processed_time) as time from match_result a where order_buy_user_id = 30564;" > order_buy_user_id_30564.tsv,success
e75412e565dff453000000000000000d,2024-02-29T11:22:31+08:00,cmd.Command,avenir_user01,223.104.41.167,miniglobal-devops,***********,ll,success
e75412e565dff453000000000000000d,2024-02-29T11:22:36+08:00,cmd.Command,avenir_user01,223.104.41.167,miniglobal-devops,***********,less order_buy_user_id_30564.tsv,success
e75412e565dff453000000000000000d,2024-02-29T11:23:05+08:00,cmd.Command,avenir_user01,223.104.41.167,miniglobal-devops,***********,cat order_buy_user_id_30564.tsv | sed 's/\t/,/g' > order_buy_user_id_30564.csv,success
e75412e565dff453000000000000000d,2024-02-29T11:23:42+08:00,cmd.Command,avenir_user01,223.104.41.167,miniglobal-devops,***********,cat order_sell_user_id_30564.tsv | sed 's/\t/,/g' > order_sell_user_id_30564.csv,success
e75412e565dff453000000000000000d,2024-02-29T11:23:44+08:00,cmd.Command,avenir_user01,223.104.41.167,miniglobal-devops,***********,ll,success
e75412e565dff453000000000000000d,2024-02-29T11:23:58+08:00,cmd.Command,avenir_user01,223.104.41.167,miniglobal-devops,***********,less order_buy_user_id_30564.csv,success
e75412e565dff453000000000000000d,2024-02-29T11:24:28+08:00,cmd.Command,avenir_user01,223.104.41.167,miniglobal-devops,***********,tar zcvf order_30564.tar.gz order_buy_user_id_30564.csv order_sell_user_id_30564.csv,success
e75412e565dff453000000000000000d,2024-02-29T11:24:45+08:00,cmd.Command,avenir_user01,223.104.41.167,miniglobal-devops,***********,ossutil cp order_30564.tar.gz  oss://miniglobal-devops/hue_log/,success
50acfc7465dfff460000000000000017,2024-02-29T12:08:31+08:00,file.Upload,shaopeng.jiao,111.33.237.222,miniglobal-sec-sep-1c,172.28.1.162,ocr.txt,success
50acfc7465dfff460000000000000017,2024-02-29T12:08:31+08:00,file.UploadSave,shaopeng.jiao,111.33.237.222,miniglobal-sec-sep-1c,172.28.1.162,keyboard.txt,success
26f1701965e046f5000000000000000d,2024-02-29T16:57:26+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,cd /tmp,success
26f1701965e046f5000000000000000d,2024-02-29T16:57:28+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,ll,success
26f1701965e046f5000000000000000d,2024-02-29T16:57:38+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,rm -f order_*,success
26f1701965e046f5000000000000000d,2024-02-29T16:58:48+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
26f1701965e046f5000000000000000d,2024-02-29T16:58:58+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,use huobi,success
26f1701965e046f5000000000000000d,2024-02-29T16:59:13+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,select u.uid, wh.out_trade_id, wh.withdraw_account from withdraw_history wh left join user u on wh.user_id = u.id where wh.withdraw_account in ("8a41f87c8e5a47296b591a5b94221b75bddc5a94eecff78cf629065fb3fea3f1", "dc3fbc5755935e8c74f6bae5798198d816e41a3c0da1384ffdc7fae826243c46", "8c8bba4cce362e1b99043722b03736b96e4a4c7031ca30df4aee21dc46598f80", "0d3d9ba1c43c226148ce2cff6f56a22c5715f50cadb9c54c8810eab4a3f9c716", "255717516c0c0c73c4f6e38ef4ecb007f2f0e961f9becbb6c0fd8fbcb3151921", "c2794bd2bfaf6bdd07dee1d6459bf9b94fbafa2ad45063e7aa0be0189d5fe147", "a07007b088de2ab81ba783b0902abd55970f8ddf02d7a86a415406cfda9e2560", "13fac7c1b8fe12d4eb747fd31edda38f902b4087dd9e4fd59f8ed8d75fe883e5", "7b77856123079f710ba8763499ddc5bdd666c7a83241e67ab638f1a38943d95e", "ca80e8373f40144d5e3b7e46a44675cebc0ea9366e622ae667f018b230cd6de8", "1897bc8059f6094ffd6db92732c98ce8dc160c58515e97a0f24c5e5ba6d11107", "f7b56b13db368e28acd73677a3418fc076a1c56555e5bc1a716519c46b889b81", "6277bba8256a9b33c9abebd090d251429c24829fd504b761a333986e090b38f7", "bf2ec24627f0ee3ef5f4b4b0a1c35cf9c93a1310ef8d6c4f9ed89ece44eca7f8", "bb9a4e08cb1b85e0acdb634b524238be1897bdb3e1ab64e9b3237b1a2b7266e1", "ab2ee58a717723eab52969b56526bd5451416200aab21c74e10e84463833fa13", "2534744528d503bb24056645bec6cc3123fc7ccf07e906b8b06e3a34688cda74", "b067e1bb88e4fa31af732f0c16a92115a147f041a04453b05ff63c35d62115dd", "8c0b66d9ed3986d1c00f808ed53128eece3da2207beefbdc0543ad49629a95aa", "e9f9a707f6015ca5ed51d6a9ab42f77aabe4c735a081c5bda9367b758094e42f", "bcd3429750fa44bcb89f9ad38f3292403ee708312fde0529d55aff2d90f1dff4", "362fd9a7382fe920f309587062c39ba74a6e5116eedcd41083227ff25551abf0", "df5c85c214137bf81aa5bb5bc511c4ea159e774c400137715c0b95dc949686a9", "eaaedf7cceb36918665ef2523315ba73e46984b7e1781f8f3dfadcbd25c06196", "842f364432aabe024784f8515df868d08046e09dc45abf7761b8fca6b8adeb27", "11411b9fa6a770bde622a7ae39ddd4b770ecfd8bf2d5efaf518766485ab6f568", "c058ffce2b9ca7c7f18872fde096cba7a37c9d334e17c9fedc7cbefd947e7b83", "27b8d9e28aa4c79f5833aa76eca3f61db5b3a8feb7cc3657346679d994494eab", "f09d430bfc9245053c07549115cfcd2167ffbc15facb7c38aa594f16d6d86c4a", "81c69aeaf05d626dc07e5cb68105b571b7c0689460cc53fe25a89839e37cad81", "ca441dcfc4481de4f45982d69f62ed02bee3025dab35e18a3a62963c9550d077", "cff0871ce11027db00da02a145e51248f54e17ada9c08137d9a71ec47806ab02", "18abbb0e431e746b8adbd401859f64608f937699c744c987a23d56beb0deab77", "2d480450f772e5f40400b67b08e41710f3729df4f8bc8da1dd016526d44d88a8", "299fb78fed4e731f7d3e0f5076e8af9b0a269982ecb2b9e380d744bf10cc22a5", "9b28a44d75a78f859fba9e95d44217946ee01af57ad2bdec036c3dbbf6933aa5", "a33933734142496a9d60b0f7258ca0b7e50dd172f4962afbce2a238c43049795", "b00111e5e7c3f95250eb288e13b1e3855ab5b2b72d3d5bb0ecb93e705e0758e6", "6409ab9a1dda110299b6e94b00db92a9ec15d7c5b3184013e31c205aadbfc33a", "6b6894bff7ce5f245d741bf0c312d8ce1f172613ee2af862c013d7eb53224235", "58ff9be02821b37fb5b354d974d4766a039e2c585322072c9869198232b192f0", "a353d331bf148bd9e6ee765e49c1dc2d4a21ba595be81321a4af1f9b3db7e74b", "dcad0ebdffddfacb90ccdbd2d6059f05353bec09f85484f649021d60fb145053", "5d13ae65cda8e90e13f77f9da61aacec372827cd9f36c8da664584d758481506", "a376384e9b897f12656a64186c3561e52ac2bbe22e68c17788f1eb266d612110", "f8d7e18b4674fb3ccfa309a37fdbee1aafd2ecfe3a9a4736fb45fb48b4481078", "9f0972a82a27e7fe9333673232ca3c732d7737cb39d2c347daef6fe931c6a042", "52997addd2b801b666fedbbd77d34c3ef38cc730beb50cadf7468352dd17abaa", "0582cfd093e01d316177653b292f1368ffb26d6c18c0afe58dc0ddd80469ace8", "c7650f51a4a60e30c45aa4a86b99dfcd914ceb90367c53b799be989e7f0e4145", "42d308d2c070b184a9b13a585c1486f58dc748a1b075dd8c5b9b98814d0f23d7", "9392f04ca566f0016d17faf3d5ce8697f0c4be55b2f0f35130ba39e7557e8dc1", "9c482cdb40de1fc34170507f51b02a3ad93f8c21f2718b5c478ba723983d796f", "d6e5b4bdfee225b1d58d84fcdcd77ea51a195a7c04668b29566c5a0c891e9e9a", "e15bc3a95cdd7051888ff1a20f3053830dd6b97d2ce64ab5ab8cd4d2667b8937", "45550f378e1b06000bbbc5662e75c1e4b75bd64e34d53b72423876634c000de7", "de0cceab3793ae884b66f5f2c83ee609cb0eaf1747f39f8866926b98034486fa", "838dcd57f67a09dc08439ea26f9025963ee61ec6df2c22f479d8eb128e42d281", "94b77774c8b921573ed452a6d67a38bac1d00218b55695acafb3d230e20de32e", "f2a81ca2b277228dd5c96e4a3e22e7c262408e1c97474f2c1c4ac2574e961efb", "4034c1f5d49583dcbe0617f5433924a6bb17d6ffa7cdb39338dc5564fcafbe8b", "7148a39c7a6d67357bbeeebea4cb0fdc454044e09ba8a9dff96398aca62c1d50", "da95c6d74820093a057e5eb819d36ab52230bc04329cb73d6a3c410848659a37", "54bef52e526dd79d642025fbc98d1ae0fb294de2dceaf6c01b79b1fc6f16dbb0", "e04d3d00c63202a9c83a7d2c26e8a9ebaab2754476b4803803375c5d02f17616", "805e5e02311444ec06e22726ccd5c6eef080a170ac629d300f6c2eeb9ded03aa", "70fd3dfdcb429d8acdf8c6d4e7e0b398799f633ff50db42db692180e13625d74", "608188f1ebf2162e61267e4e3dce4b0a939256be77130354342b4c1024b12a85", "b12571b61877345fe18dfad4da61e94e5b8e5682373004608155f161b88c9fd7", "cad65392d126ef740bd31d18f5238aa1680685d86bdddc769387335fa4f14453", "13e38c0a7e777ebdcd1c8f827904a06cde15d22f156f3fa437b47a94f90c69fa", "d9771fb8f273f0bd2d465b03b94fb72d81a1a43b8c599f53e74436017c3ce337", "1d30fcf514a633fc43bd70d960a1d3f4b5b0cb39a72072f5281cfc6cf5ba627f", "db887fd321,success
26f1701965e046f5000000000000000d,2024-02-29T17:00:01+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,show tables like '%history%';,success
26f1701965e046f5000000000000000d,2024-02-29T17:01:30+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,select * from withdraw_history limit 5;,success
26f1701965e046f5000000000000000d,2024-02-29T17:02:38+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,select * from withdraw_history limit 5 \G;,success
26f1701965e046f5000000000000000d,2024-02-29T17:04:01+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,select u.uid, wh.out_trade_id, wh.withdraw_account from withdraw_history wh left join user u on wh.user_id = u.id where wh.out_trade_id in ("8a41f87c8e5a47296b591a5b94221b75bddc5a94eecff78cf629065fb3fea3f1", "dc3fbc5755935e8c74f6bae5798198d816e41a3c0da1384ffdc7fae826243c46", "8c8bba4cce362e1b99043722b03736b96e4a4c7031ca30df4aee21dc46598f80", "0d3d9ba1c43c226148ce2cff6f56a22c5715f50cadb9c54c8810eab4a3f9c716", "255717516c0c0c73c4f6e38ef4ecb007f2f0e961f9becbb6c0fd8fbcb3151921", "c2794bd2bfaf6bdd07dee1d6459bf9b94fbafa2ad45063e7aa0be0189d5fe147", "a07007b088de2ab81ba783b0902abd55970f8ddf02d7a86a415406cfda9e2560", "13fac7c1b8fe12d4eb747fd31edda38f902b4087dd9e4fd59f8ed8d75fe883e5", "7b77856123079f710ba8763499ddc5bdd666c7a83241e67ab638f1a38943d95e", "ca80e8373f40144d5e3b7e46a44675cebc0ea9366e622ae667f018b230cd6de8", "1897bc8059f6094ffd6db92732c98ce8dc160c58515e97a0f24c5e5ba6d11107", "f7b56b13db368e28acd73677a3418fc076a1c56555e5bc1a716519c46b889b81", "6277bba8256a9b33c9abebd090d251429c24829fd504b761a333986e090b38f7", "bf2ec24627f0ee3ef5f4b4b0a1c35cf9c93a1310ef8d6c4f9ed89ece44eca7f8", "bb9a4e08cb1b85e0acdb634b524238be1897bdb3e1ab64e9b3237b1a2b7266e1", "ab2ee58a717723eab52969b56526bd5451416200aab21c74e10e84463833fa13", "2534744528d503bb24056645bec6cc3123fc7ccf07e906b8b06e3a34688cda74", "b067e1bb88e4fa31af732f0c16a92115a147f041a04453b05ff63c35d62115dd", "8c0b66d9ed3986d1c00f808ed53128eece3da2207beefbdc0543ad49629a95aa", "e9f9a707f6015ca5ed51d6a9ab42f77aabe4c735a081c5bda9367b758094e42f", "bcd3429750fa44bcb89f9ad38f3292403ee708312fde0529d55aff2d90f1dff4", "362fd9a7382fe920f309587062c39ba74a6e5116eedcd41083227ff25551abf0", "df5c85c214137bf81aa5bb5bc511c4ea159e774c400137715c0b95dc949686a9", "eaaedf7cceb36918665ef2523315ba73e46984b7e1781f8f3dfadcbd25c06196", "842f364432aabe024784f8515df868d08046e09dc45abf7761b8fca6b8adeb27", "11411b9fa6a770bde622a7ae39ddd4b770ecfd8bf2d5efaf518766485ab6f568", "c058ffce2b9ca7c7f18872fde096cba7a37c9d334e17c9fedc7cbefd947e7b83", "27b8d9e28aa4c79f5833aa76eca3f61db5b3a8feb7cc3657346679d994494eab", "f09d430bfc9245053c07549115cfcd2167ffbc15facb7c38aa594f16d6d86c4a", "81c69aeaf05d626dc07e5cb68105b571b7c0689460cc53fe25a89839e37cad81", "ca441dcfc4481de4f45982d69f62ed02bee3025dab35e18a3a62963c9550d077", "cff0871ce11027db00da02a145e51248f54e17ada9c08137d9a71ec47806ab02", "18abbb0e431e746b8adbd401859f64608f937699c744c987a23d56beb0deab77", "2d480450f772e5f40400b67b08e41710f3729df4f8bc8da1dd016526d44d88a8", "299fb78fed4e731f7d3e0f5076e8af9b0a269982ecb2b9e380d744bf10cc22a5", "9b28a44d75a78f859fba9e95d44217946ee01af57ad2bdec036c3dbbf6933aa5", "a33933734142496a9d60b0f7258ca0b7e50dd172f4962afbce2a238c43049795", "b00111e5e7c3f95250eb288e13b1e3855ab5b2b72d3d5bb0ecb93e705e0758e6", "6409ab9a1dda110299b6e94b00db92a9ec15d7c5b3184013e31c205aadbfc33a", "6b6894bff7ce5f245d741bf0c312d8ce1f172613ee2af862c013d7eb53224235", "58ff9be02821b37fb5b354d974d4766a039e2c585322072c9869198232b192f0", "a353d331bf148bd9e6ee765e49c1dc2d4a21ba595be81321a4af1f9b3db7e74b", "dcad0ebdffddfacb90ccdbd2d6059f05353bec09f85484f649021d60fb145053", "5d13ae65cda8e90e13f77f9da61aacec372827cd9f36c8da664584d758481506", "a376384e9b897f12656a64186c3561e52ac2bbe22e68c17788f1eb266d612110", "f8d7e18b4674fb3ccfa309a37fdbee1aafd2ecfe3a9a4736fb45fb48b4481078", "9f0972a82a27e7fe9333673232ca3c732d7737cb39d2c347daef6fe931c6a042", "52997addd2b801b666fedbbd77d34c3ef38cc730beb50cadf7468352dd17abaa", "0582cfd093e01d316177653b292f1368ffb26d6c18c0afe58dc0ddd80469ace8", "c7650f51a4a60e30c45aa4a86b99dfcd914ceb90367c53b799be989e7f0e4145", "42d308d2c070b184a9b13a585c1486f58dc748a1b075dd8c5b9b98814d0f23d7", "9392f04ca566f0016d17faf3d5ce8697f0c4be55b2f0f35130ba39e7557e8dc1", "9c482cdb40de1fc34170507f51b02a3ad93f8c21f2718b5c478ba723983d796f", "d6e5b4bdfee225b1d58d84fcdcd77ea51a195a7c04668b29566c5a0c891e9e9a", "e15bc3a95cdd7051888ff1a20f3053830dd6b97d2ce64ab5ab8cd4d2667b8937", "45550f378e1b06000bbbc5662e75c1e4b75bd64e34d53b72423876634c000de7", "de0cceab3793ae884b66f5f2c83ee609cb0eaf1747f39f8866926b98034486fa", "838dcd57f67a09dc08439ea26f9025963ee61ec6df2c22f479d8eb128e42d281", "94b77774c8b921573ed452a6d67a38bac1d00218b55695acafb3d230e20de32e", "f2a81ca2b277228dd5c96e4a3e22e7c262408e1c97474f2c1c4ac2574e961efb", "4034c1f5d49583dcbe0617f5433924a6bb17d6ffa7cdb39338dc5564fcafbe8b", "7148a39c7a6d67357bbeeebea4cb0fdc454044e09ba8a9dff96398aca62c1d50", "da95c6d74820093a057e5eb819d36ab52230bc04329cb73d6a3c410848659a37", "54bef52e526dd79d642025fbc98d1ae0fb294de2dceaf6c01b79b1fc6f16dbb0", "e04d3d00c63202a9c83a7d2c26e8a9ebaab2754476b4803803375c5d02f17616", "805e5e02311444ec06e22726ccd5c6eef080a170ac629d300f6c2eeb9ded03aa", "70fd3dfdcb429d8acdf8c6d4e7e0b398799f633ff50db42db692180e13625d74", "608188f1ebf2162e61267e4e3dce4b0a939256be77130354342b4c1024b12a85", "b12571b61877345fe18dfad4da61e94e5b8e5682373004608155f161b88c9fd7", "cad65392d126ef740bd31d18f5238aa1680685d86bdddc769387335fa4f14453", "13e38c0a7e777ebdcd1c8f827904a06cde15d22f156f3fa437b47a94f90c69fa", "d9771fb8f273f0bd2d465b03b94fb72d81a1a43b8c599f53e74436017c3ce337", "1d30fcf514a633fc43bd70d960a1d3f4b5b0cb39a72072f5281cfc6cf5ba627f", "db887fd321101a,success
26f1701965e046f5000000000000000d,2024-02-29T17:04:42+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,quit,success
26f1701965e046f5000000000000000d,2024-02-29T17:04:47+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,ll,success
26f1701965e046f5000000000000000d,2024-02-29T17:37:53+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com huobi -e "select u.uid, wh.out_trade_id, wh.withdraw_account from withdraw_history wh left join user u on wh.user_id = u.id where wh.out_trade_id in ("8a41f87c8e5a47296b591a5b94221b75bddc5a94eecff78cf629065fb3fea3f1", "dc3fbc5755935e8c74f6bae5798198d816e41a3c0da1384ffdc7fae826243c46", "8c8bba4cce362e1b99043722b03736b96e4a4c7031ca30df4aee21dc46598f80", "0d3d9ba1c43c226148ce2cff6f56a22c5715f50cadb9c54c8810eab4a3f9c716", "255717516c0c0c73c4f6e38ef4ecb007f2f0e961f9becbb6c0fd8fbcb3151921", "c2794bd2bfaf6bdd07dee1d6459bf9b94fbafa2ad45063e7aa0be0189d5fe147", "a07007b088de2ab81ba783b0902abd55970f8ddf02d7a86a415406cfda9e2560", "13fac7c1b8fe12d4eb747fd31edda38f902b4087dd9e4fd59f8ed8d75fe883e5", "7b77856123079f710ba8763499ddc5bdd666c7a83241e67ab638f1a38943d95e", "ca80e8373f40144d5e3b7e46a44675cebc0ea9366e622ae667f018b230cd6de8", "1897bc8059f6094ffd6db92732c98ce8dc160c58515e97a0f24c5e5ba6d11107", "f7b56b13db368e28acd73677a3418fc076a1c56555e5bc1a716519c46b889b81", "6277bba8256a9b33c9abebd090d251429c24829fd504b761a333986e090b38f7", "bf2ec24627f0ee3ef5f4b4b0a1c35cf9c93a1310ef8d6c4f9ed89ece44eca7f8", "bb9a4e08cb1b85e0acdb634b524238be1897bdb3e1ab64e9b3237b1a2b7266e1", "ab2ee58a717723eab52969b56526bd5451416200aab21c74e10e84463833fa13", "2534744528d503bb24056645bec6cc3123fc7ccf07e906b8b06e3a34688cda74", "b067e1bb88e4fa31af732f0c16a92115a147f041a04453b05ff63c35d62115dd", "8c0b66d9ed3986d1c00f808ed53128eece3da2207beefbdc0543ad49629a95aa", "e9f9a707f6015ca5ed51d6a9ab42f77aabe4c735a081c5bda9367b758094e42f", "bcd3429750fa44bcb89f9ad38f3292403ee708312fde0529d55aff2d90f1dff4", "362fd9a7382fe920f309587062c39ba74a6e5116eedcd41083227ff25551abf0", "df5c85c214137bf81aa5bb5bc511c4ea159e774c400137715c0b95dc949686a9", "eaaedf7cceb36918665ef2523315ba73e46984b7e1781f8f3dfadcbd25c06196", "842f364432aabe024784f8515df868d08046e09dc45abf7761b8fca6b8adeb27", "11411b9fa6a770bde622a7ae39ddd4b770ecfd8bf2d5efaf518766485ab6f568", "c058ffce2b9ca7c7f18872fde096cba7a37c9d334e17c9fedc7cbefd947e7b83", "27b8d9e28aa4c79f5833aa76eca3f61db5b3a8feb7cc3657346679d994494eab", "f09d430bfc9245053c07549115cfcd2167ffbc15facb7c38aa594f16d6d86c4a", "81c69aeaf05d626dc07e5cb68105b571b7c0689460cc53fe25a89839e37cad81", "ca441dcfc4481de4f45982d69f62ed02bee3025dab35e18a3a62963c9550d077", "cff0871ce11027db00da02a145e51248f54e17ada9c08137d9a71ec47806ab02", "18abbb0e431e746b8adbd401859f64608f937699c744c987a23d56beb0deab77", "2d480450f772e5f40400b67b08e41710f3729df4f8bc8da1dd016526d44d88a8", "299fb78fed4e731f7d3e0f5076e8af9b0a269982ecb2b9e380d744bf10cc22a5", "9b28a44d75a78f859fba9e95d44217946ee01af57ad2bdec036c3dbbf6933aa5", "a33933734142496a9d60b0f7258ca0b7e50dd172f4962afbce2a238c43049795", "b00111e5e7c3f95250eb288e13b1e3855ab5b2b72d3d5bb0ecb93e705e0758e6", "6409ab9a1dda110299b6e94b00db92a9ec15d7c5b3184013e31c205aadbfc33a", "6b6894bff7ce5f245d741bf0c312d8ce1f172613ee2af862c013d7eb53224235", "58ff9be02821b37fb5b354d974d4766a039e2c585322072c9869198232b192f0", "a353d331bf148bd9e6ee765e49c1dc2d4a21ba595be81321a4af1f9b3db7e74b", "dcad0ebdffddfacb90ccdbd2d6059f05353bec09f85484f649021d60fb145053", "5d13ae65cda8e90e13f77f9da61aacec372827cd9f36c8da664584d758481506", "a376384e9b897f12656a64186c3561e52ac2bbe22e68c17788f1eb266d612110", "f8d7e18b4674fb3ccfa309a37fdbee1aafd2ecfe3a9a4736fb45fb48b4481078", "9f0972a82a27e7fe9333673232ca3c732d7737cb39d2c347daef6fe931c6a042", "52997addd2b801b666fedbbd77d34c3ef38cc730beb50cadf7468352dd17abaa", "0582cfd093e01d316177653b292f1368ffb26d6c18c0afe58dc0ddd80469ace8", "c7650f51a4a60e30c45aa4a86b99dfcd914ceb90367c53b799be989e7f0e4145", "42d308d2c070b184a9b13a585c1486f58dc748a1b075dd8c5b9b98814d0f23d7", "9392f04ca566f0016d17faf3d5ce8697f0c4be55b2f0f35130ba39e7557e8dc1", "9c482cdb40de1fc34170507f51b02a3ad93f8c21f2718b5c478ba723983d796f", "d6e5b4bdfee225b1d58d84fcdcd77ea51a195a7c04668b29566c5a0c891e9e9a", "e15bc3a95cdd7051888ff1a20f3053830dd6b97d2ce64ab5ab8cd4d2667b8937", "45550f378e1b06000bbbc5662e75c1e4b75bd64e34d53b72423876634c000de7", "de0cceab3793ae884b66f5f2c83ee609cb0eaf1747f39f8866926b98034486fa", "838dcd57f67a09dc08439ea26f9025963ee61ec6df2c22f479d8eb128e42d281", "94b77774c8b921573ed452a6d67a38bac1d00218b55695acafb3d230e20de32e", "f2a81ca2b277228dd5c96e4a3e22e7c262408e1c97474f2c1c4ac2574e961efb", "4034c1f5d49583dcbe0617f5433924a6bb17d6ffa7cdb39338dc5564fcafbe8b", "7148a39c7a6d67357bbeeebea4cb0fdc454044e09ba8a9dff96398aca62c1d50", "da95c6d74820093a057e5eb819d36ab52230bc04329cb73d6a3c410848659a37", "54bef52e526dd79d642025fbc98d1ae0fb294de2dceaf6c01b79b1fc6f16dbb0", "e04d3d00c63202a9c83a7d2c26e8a9ebaab2754476b4803803375c5d02f17616", "805e5e02311444ec06e22726ccd5c6eef080a170ac629d300f6c2eeb9ded03aa", "70fd3dfdcb429d8acdf8c6d4e7e0b398799f633ff50db42db692180e13625d74", "608188f1ebf2162e61267e4e3dce4b0a939256be77130354342b4c1024b12a85", "b12571b61877345fe18dfad4da61e94e5b8e5682373004608155f161b88c9fd7", "cad65392d126ef740bd31d18f5238aa1680685d86bdddc769387335fa4f14453", "13e38c0a7e777ebdcd1c8f827904a06cde15d22f156f3fa437b47a94f90c69fa", "d9771fb8f273f0bd2d465b03b94fb72d81a1a43b8c599f53e74436017c3ce337", "1d30fcf514a633fc43,success
26f1701965e046f5000000000000000d,2024-02-29T17:41:02+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,ll,success
26f1701965e046f5000000000000000d,2024-02-29T17:41:08+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com huobi -e 'select u.uid, wh.out_trade_id, wh.withdraw_account from withdraw_history wh left join user u on wh.user_id = u.id where wh.out_trade_id in ("8a41f87c8e5a47296b591a5b94221b75bddc5a94eecff78cf629065fb3fea3f1", "dc3fbc5755935e8c74f6bae5798198d816e41a3c0da1384ffdc7fae826243c46", "8c8bba4cce362e1b99043722b03736b96e4a4c7031ca30df4aee21dc46598f80", "0d3d9ba1c43c226148ce2cff6f56a22c5715f50cadb9c54c8810eab4a3f9c716", "255717516c0c0c73c4f6e38ef4ecb007f2f0e961f9becbb6c0fd8fbcb3151921", "c2794bd2bfaf6bdd07dee1d6459bf9b94fbafa2ad45063e7aa0be0189d5fe147", "a07007b088de2ab81ba783b0902abd55970f8ddf02d7a86a415406cfda9e2560", "13fac7c1b8fe12d4eb747fd31edda38f902b4087dd9e4fd59f8ed8d75fe883e5", "7b77856123079f710ba8763499ddc5bdd666c7a83241e67ab638f1a38943d95e", "ca80e8373f40144d5e3b7e46a44675cebc0ea9366e622ae667f018b230cd6de8", "1897bc8059f6094ffd6db92732c98ce8dc160c58515e97a0f24c5e5ba6d11107", "f7b56b13db368e28acd73677a3418fc076a1c56555e5bc1a716519c46b889b81", "6277bba8256a9b33c9abebd090d251429c24829fd504b761a333986e090b38f7", "bf2ec24627f0ee3ef5f4b4b0a1c35cf9c93a1310ef8d6c4f9ed89ece44eca7f8", "bb9a4e08cb1b85e0acdb634b524238be1897bdb3e1ab64e9b3237b1a2b7266e1", "ab2ee58a717723eab52969b56526bd5451416200aab21c74e10e84463833fa13", "2534744528d503bb24056645bec6cc3123fc7ccf07e906b8b06e3a34688cda74", "b067e1bb88e4fa31af732f0c16a92115a147f041a04453b05ff63c35d62115dd", "8c0b66d9ed3986d1c00f808ed53128eece3da2207beefbdc0543ad49629a95aa", "e9f9a707f6015ca5ed51d6a9ab42f77aabe4c735a081c5bda9367b758094e42f", "bcd3429750fa44bcb89f9ad38f3292403ee708312fde0529d55aff2d90f1dff4", "362fd9a7382fe920f309587062c39ba74a6e5116eedcd41083227ff25551abf0", "df5c85c214137bf81aa5bb5bc511c4ea159e774c400137715c0b95dc949686a9", "eaaedf7cceb36918665ef2523315ba73e46984b7e1781f8f3dfadcbd25c06196", "842f364432aabe024784f8515df868d08046e09dc45abf7761b8fca6b8adeb27", "11411b9fa6a770bde622a7ae39ddd4b770ecfd8bf2d5efaf518766485ab6f568", "c058ffce2b9ca7c7f18872fde096cba7a37c9d334e17c9fedc7cbefd947e7b83", "27b8d9e28aa4c79f5833aa76eca3f61db5b3a8feb7cc3657346679d994494eab", "f09d430bfc9245053c07549115cfcd2167ffbc15facb7c38aa594f16d6d86c4a", "81c69aeaf05d626dc07e5cb68105b571b7c0689460cc53fe25a89839e37cad81", "ca441dcfc4481de4f45982d69f62ed02bee3025dab35e18a3a62963c9550d077", "cff0871ce11027db00da02a145e51248f54e17ada9c08137d9a71ec47806ab02", "18abbb0e431e746b8adbd401859f64608f937699c744c987a23d56beb0deab77", "2d480450f772e5f40400b67b08e41710f3729df4f8bc8da1dd016526d44d88a8", "299fb78fed4e731f7d3e0f5076e8af9b0a269982ecb2b9e380d744bf10cc22a5", "9b28a44d75a78f859fba9e95d44217946ee01af57ad2bdec036c3dbbf6933aa5", "a33933734142496a9d60b0f7258ca0b7e50dd172f4962afbce2a238c43049795", "b00111e5e7c3f95250eb288e13b1e3855ab5b2b72d3d5bb0ecb93e705e0758e6", "6409ab9a1dda110299b6e94b00db92a9ec15d7c5b3184013e31c205aadbfc33a", "6b6894bff7ce5f245d741bf0c312d8ce1f172613ee2af862c013d7eb53224235", "58ff9be02821b37fb5b354d974d4766a039e2c585322072c9869198232b192f0", "a353d331bf148bd9e6ee765e49c1dc2d4a21ba595be81321a4af1f9b3db7e74b", "dcad0ebdffddfacb90ccdbd2d6059f05353bec09f85484f649021d60fb145053", "5d13ae65cda8e90e13f77f9da61aacec372827cd9f36c8da664584d758481506", "a376384e9b897f12656a64186c3561e52ac2bbe22e68c17788f1eb266d612110", "f8d7e18b4674fb3ccfa309a37fdbee1aafd2ecfe3a9a4736fb45fb48b4481078", "9f0972a82a27e7fe9333673232ca3c732d7737cb39d2c347daef6fe931c6a042", "52997addd2b801b666fedbbd77d34c3ef38cc730beb50cadf7468352dd17abaa", "0582cfd093e01d316177653b292f1368ffb26d6c18c0afe58dc0ddd80469ace8", "c7650f51a4a60e30c45aa4a86b99dfcd914ceb90367c53b799be989e7f0e4145", "42d308d2c070b184a9b13a585c1486f58dc748a1b075dd8c5b9b98814d0f23d7", "9392f04ca566f0016d17faf3d5ce8697f0c4be55b2f0f35130ba39e7557e8dc1", "9c482cdb40de1fc34170507f51b02a3ad93f8c21f2718b5c478ba723983d796f", "d6e5b4bdfee225b1d58d84fcdcd77ea51a195a7c04668b29566c5a0c891e9e9a", "e15bc3a95cdd7051888ff1a20f3053830dd6b97d2ce64ab5ab8cd4d2667b8937", "45550f378e1b06000bbbc5662e75c1e4b75bd64e34d53b72423876634c000de7", "de0cceab3793ae884b66f5f2c83ee609cb0eaf1747f39f8866926b98034486fa", "838dcd57f67a09dc08439ea26f9025963ee61ec6df2c22f479d8eb128e42d281", "94b77774c8b921573ed452a6d67a38bac1d00218b55695acafb3d230e20de32e", "f2a81ca2b277228dd5c96e4a3e22e7c262408e1c97474f2c1c4ac2574e961efb", "4034c1f5d49583dcbe0617f5433924a6bb17d6ffa7cdb39338dc5564fcafbe8b", "7148a39c7a6d67357bbeeebea4cb0fdc454044e09ba8a9dff96398aca62c1d50", "da95c6d74820093a057e5eb819d36ab52230bc04329cb73d6a3c410848659a37", "54bef52e526dd79d642025fbc98d1ae0fb294de2dceaf6c01b79b1fc6f16dbb0", "e04d3d00c63202a9c83a7d2c26e8a9ebaab2754476b4803803375c5d02f17616", "805e5e02311444ec06e22726ccd5c6eef080a170ac629d300f6c2eeb9ded03aa", "70fd3dfdcb429d8acdf8c6d4e7e0b398799f633ff50db42db692180e13625d74", "608188f1ebf2162e61267e4e3dce4b0a939256be77130354342b4c1024b12a85", "b12571b61877345fe18dfad4da61e94e5b8e5682373004608155f161b88c9fd7", "cad65392d126ef740bd31d18f5238aa1680685d86bdddc769387335fa4f14453", "13e38c0a7e777ebdcd1c8f827904a06cde15d22f156f3fa437b47a94f90c69fa", "d9771fb8f273f0bd2d465b03b94fb72d81a1a43b8c599f53e74436017c3ce337", "1d30fcf514a633fc43,success
26f1701965e046f5000000000000000d,2024-02-29T17:41:19+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,ll,success
26f1701965e046f5000000000000000d,2024-02-29T17:41:29+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,less baise_2.28.tsv,success
26f1701965e046f5000000000000000d,2024-02-29T17:42:11+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,cat baise_2.28.tsv | sed 's/\t/,/g' > baise_2.28.csv,success
26f1701965e046f5000000000000000d,2024-02-29T17:42:55+08:00,cmd.Command,avenir_user01,120.244.194.68,miniglobal-devops,***********,ossutil cp baise_2.28.csv  oss://miniglobal-devops/hue_log/,success
0bb0b53a65e05c1a000000000000000d,2024-02-29T18:27:40+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,cd /tmp,success
0bb0b53a65e05c1a000000000000000d,2024-02-29T18:27:41+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ll,success
0bb0b53a65e05c1a000000000000000d,2024-02-29T18:27:54+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com huobi -e 'select user_id,channel_id,account,channel_account from my_channel where user_id in (286630, 248249, 438238, 444371, 446065, 209481, 410879, 288376, 490520, 312943, 566671, 204567, 548251, 411958, 405191, 561445, 577298, 311441, 570449, 522551, 571968, 592895, 552602, 586370, 521363, 393067, 606560, 59    4555, 617594, 617283, 616920, 632062, 557496, 800087);' > baise_2.28_bank.tsv,success
0bb0b53a65e05c1a000000000000000d,2024-02-29T18:28:37+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ll,success
0bb0b53a65e05c1a000000000000000d,2024-02-29T18:28:42+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com huobi -e 'select user_id,channel_id,account,channel_account from my_channel where user_id in (286630, 248249, 438238, 444371, 446065, 209481, 410879, 288376, 490520, 312943, 566671, 204567, 548251, 411958, 405191, 561445, 577298, 311441, 570449, 522551, 571968, 592895, 552602, 586370, 521363, 393067, 606560, 594555, 617594, 617283, 616920, 632062, 557496, 800087);' > baise_2.28_bank.tsv,success
0bb0b53a65e05c1a000000000000000d,2024-02-29T18:28:48+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ll,success
0bb0b53a65e05c1a000000000000000d,2024-02-29T18:29:25+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,cat baise_2.28_bank.tsv  | sed 's/\t/,/g' > baise_2.28_bank.csv,success
0bb0b53a65e05c1a000000000000000d,2024-02-29T18:29:34+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,less baise_2.28_bank.csv,success
0bb0b53a65e05c1a000000000000000d,2024-02-29T18:29:49+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,jjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjqll,success
0bb0b53a65e05c1a000000000000000d,2024-02-29T18:31:22+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com huobi -e 'select * from deposit_history where user_id in (286630, 248249, 438238, 444371, 446065, 209481, 410879, 288376, 490520, 312943, 566671, 204567, 548251, 411958, 405191, 561445, 577298, 311441, 570449, 522551, 571968, 592895, 552602, 586370, 521363, 393067, 606560, 594555, 617594, 617283, 616920, 632062, 557496, 800087);' > baise_2.28_deposit.tsv,success
0bb0b53a65e05c1a000000000000000d,2024-02-29T18:31:32+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ll,success
0bb0b53a65e05c1a000000000000000d,2024-02-29T18:31:43+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com huobi -e 'select * from withdraw_history where user_id in (286630, 248249, 438238, 444371, 446065, 209481, 410879, 288376, 490520, 312943, 566671, 204567, 548251, 411958, 405191, 561445, 577298, 311441, 570449, 522551, 571968, 592895, 552602, 586370, 521363, 393067, 606560, 594555, 617594, 617283, 616920, 632062, 557496, 800087);' > baise_2.28_withdraw.tsv,success
0bb0b53a65e05c1a000000000000000d,2024-02-29T18:31:47+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ll,success
0bb0b53a65e05c1a000000000000000d,2024-02-29T18:32:09+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,cat baise_2.28_deposit.tsv  | sed 's/\t/,/g' > baise_2.28_deposit.csv,success
0bb0b53a65e05c1a000000000000000d,2024-02-29T18:32:26+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,cat baise_2.28_withdraw.tsv  | sed 's/\t/,/g' > baise_2.28_withdraw.csv,success
0bb0b53a65e05c1a000000000000000d,2024-02-29T18:32:27+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ll,success
0bb0b53a65e05c1a000000000000000d,2024-02-29T18:32:34+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,rm -f *.tsv,success
0bb0b53a65e05c1a000000000000000d,2024-02-29T18:32:35+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ll,success
0bb0b53a65e05c1a000000000000000d,2024-02-29T18:32:53+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,vim baise_2.28.csv,success
0bb0b53a65e05c1a000000000000000d,2024-02-29T18:33:16+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,:q,success
0bb0b53a65e05c1a000000000000000d,2024-02-29T18:34:32+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,tar zcvf baise_2.28.tar.gz baise_2.28_bank.csv baise_2.28_deposit.csv baise_2.28_withdraw.csv,success
0bb0b53a65e05c1a000000000000000d,2024-02-29T18:34:45+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ossutil cp baise_2.28.tar.gz  oss://miniglobal-devops/hue_log/,success
0bb0b53a65e05c1a000000000000000d,2024-02-29T19:29:34+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,kubectl describe pvc -n otc-middleware elasticsearch-data-elasticsearch-otc-es-data-0,success
0bb0b53a65e05c1a000000000000000d,2024-02-29T19:30:12+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,ssh 172.28.46.171,success
fc8a593765e06af2000000000000000d,2024-02-29T19:31:04+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,kubectl describe pvc -n otc-middleware elasticsearch-data-elasticsearch-otc-es-data-0,success
fc8a593765e06af2000000000000000d,2024-02-29T19:31:24+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,kubectl describe pvc -n otc-middleware elasticsearch-data-elasticsearch-otc-es-data-1,success
fc8a593765e06af2000000000000000d,2024-02-29T19:31:29+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,kubectl describe pvc -n otc-middleware elasticsearch-data-elasticsearch-otc-es-data-2,success
fc8a593765e06af2000000000000000d,2024-02-29T20:57:13+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,kubectl -n otc-middleware logs elasticsearch-data-elasticsearch-otc-es-data-0,success
fc8a593765e06af2000000000000000d,2024-02-29T20:57:43+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,kubectl -n otc-middleware get pods -o wide,success
fc8a593765e06af2000000000000000d,2024-02-29T20:57:56+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,kubectl -n otc-middleware logs elasticsearch-otc-es-data-0,success
fc8a593765e06af2000000000000000d,2024-02-29T20:59:41+08:00,cmd.Command,avenir_user01,120.244.194.1,miniglobal-devops,***********,kubectl get nodes,success
