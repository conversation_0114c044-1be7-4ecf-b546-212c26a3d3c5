SessionID,EventTime,EventType,<PERSON>rna<PERSON>,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,Result
95862f0c65920084000000000000000d,2024-01-01T08:00:17+08:00,cmd.Command,avenir_user01,124.64.177.192,miniglobal-devops,***********,cd /tmp,success
95862f0c65920084000000000000000d,2024-01-01T08:00:18+08:00,cmd.Command,avenir_user01,124.64.177.192,miniglobal-devops,***********,ll,success
95862f0c65920084000000000000000d,2024-01-01T08:00:45+08:00,cmd.Command,avenir_user01,124.64.177.192,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h 172.28.5.15 finance_daily_report -e "select * from finance_daily_report.t_currency_rate where f_entry_time=1704038400  order by f_id desc;" > t_currency_rate_********.tsv,success
95862f0c65920084000000000000000d,2024-01-01T08:00:56+08:00,cmd.Command,avenir_user01,124.64.177.192,miniglobal-devops,***********,ll,success
95862f0c65920084000000000000000d,2024-01-01T08:01:07+08:00,cmd.Command,avenir_user01,124.64.177.192,miniglobal-devops,***********,cat t_currency_rate_********.tsv | sed 's/\t/,/g' > t_currency_rate_********.csv,success
95862f0c65920084000000000000000d,2024-01-01T08:01:10+08:00,cmd.Command,avenir_user01,124.64.177.192,miniglobal-devops,***********,ll,success
95862f0c65920084000000000000000d,2024-01-01T08:03:34+08:00,cmd.Command,avenir_user01,124.64.177.192,miniglobal-devops,***********,ossutil cp t_currency_rate_********.csv oss://miniglobal-devops/hue_log/,success
95862f0c65920084000000000000000d,2024-01-01T08:06:18+08:00,cmd.Command,avenir_user01,124.64.177.192,miniglobal-devops,***********,rm -f t_currency_rate_********.*,success
db4c02ec6592e0100000000000000019,2024-01-01T23:54:02+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
db4c02ec6592e0100000000000000019,2024-01-01T23:55:55+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,use proacctdb_1;,success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:07+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    ,sum(f_suspense) as f_suspense,success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:07+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    ,sum(f_balance) as f_balance,success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:07+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,SELECT f_currency,success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:07+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    , `f_date` ,success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:07+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,from t_currency_stat,success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:07+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,where,success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:07+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,f_date = DATE_FORMAT(NOW(),'%Y-%m-%d'),success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:07+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,and f_currency not in(,success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:07+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    'a1b1', 'a1b2', 'a2b2', 'a3b3', 'a3b4', 'a6b6', 'a7a7', 'a7b7', 'a8a8', 'a8s8', 'a9a9', 'b1b1', 'b1b2', 'b1s1', 'b2b2', 'b2b3', 'b3b4', 'b3l3', 'b3s3', 'c2d2', 'c3d3', 'c4d4', 'c5d5', 'c6d6', 'c7d7', 'c8d8', 'c9d9', 'csb001', 'csb002', 'csb003', 'csb005', 'csb006', 'csb007', 'd8f8', 'e1f1', 'e2f2', 'e3f3', 'e4f4', 'e5f5', 'e6f6', 'e7f7', 'e8f8', 'e9f9', 'h1h1', 'h2h2', 'h3h3', 'h4h4', 'h5h5', 'h6h6', 'j1j1', 'j2j2', 'j3j3', 'j4j4', 'j5j5', 'q1q2', 't1t1', 't2t2', 'v1v2', 'v2v3', 'v3v4', 'v4v5', 'w1j1', 'w2j2', 'w3j3', 'w4j4', 'w5j5', 'w6j6', 'x6z7', 'x8z9', 'y1y2', 'y2y3', 'y3y4', 'z1z2',success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:07+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    ),success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:07+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,group by `f_currency`, `f_date`;,success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:29+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,use hbgorderdb;,success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:35+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,select * from t_point_stat ,success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:35+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,where f_date = DATE_FORMAT(NOW(),'%Y-%m-%d');,success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:51+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,use proacctdb_1;,success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:59+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,, DATE_FORMAT(NOW(),'%Y-%m-%d') as `f_date` ,success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:59+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,`f_db`,success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:59+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,, `f_currency`,success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:59+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,, `f_balance`,success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:59+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,, `f_suspense`,success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:59+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,, `f_non_margin_suspense`,success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:59+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,, `f_clearing`,success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:59+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,insert into t_currency_stat(,success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:59+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,SELECT ,success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:59+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,`f_db`,success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:59+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,, `f_currency`,success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:59+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,, `f_balance`,success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:59+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,, `f_suspense`,success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:59+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,, `f_non_margin_suspense`,success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:59+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,, `f_clearing`,success
db4c02ec6592e0100000000000000019,2024-01-01T23:56:59+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,, `f_date`),success
db4c02ec6592e0100000000000000019,2024-01-01T23:57:00+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,from v_proacctdb_t_subaccount_stat;,success
db4c02ec6592e0100000000000000019,2024-01-01T23:58:05+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,use hbgorderdb;,success
db4c02ec6592e0100000000000000019,2024-01-01T23:58:11+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,insert into t_point_stat(,success
db4c02ec6592e0100000000000000019,2024-01-01T23:58:11+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,f_state,success
db4c02ec6592e0100000000000000019,2024-01-01T23:58:11+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,,f_total_point,success
db4c02ec6592e0100000000000000019,2024-01-01T23:58:11+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,,f_limit_point,success
db4c02ec6592e0100000000000000019,2024-01-01T23:58:11+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,,f_normal_point,success
db4c02ec6592e0100000000000000019,2024-01-01T23:58:11+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,,f_total_price,success
db4c02ec6592e0100000000000000019,2024-01-01T23:58:11+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,,f_limit_price,success
db4c02ec6592e0100000000000000019,2024-01-01T23:58:12+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,),success
db4c02ec6592e0100000000000000019,2024-01-01T23:58:12+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,,f_revoke,success
db4c02ec6592e0100000000000000019,2024-01-01T23:58:12+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,,f_date,success
db4c02ec6592e0100000000000000019,2024-01-01T23:58:12+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,,f_normal_price,success
db4c02ec6592e0100000000000000019,2024-01-01T23:58:12+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,select ,success
db4c02ec6592e0100000000000000019,2024-01-01T23:58:12+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,f_state,success
db4c02ec6592e0100000000000000019,2024-01-01T23:58:12+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,, DATE_FORMAT(NOW(),'%Y-%m-%d') as `f_date` ,success
db4c02ec6592e0100000000000000019,2024-01-01T23:58:12+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,,f_limit_point,success
db4c02ec6592e0100000000000000019,2024-01-01T23:58:12+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,,f_normal_point,success
db4c02ec6592e0100000000000000019,2024-01-01T23:58:12+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,,f_total_price,success
db4c02ec6592e0100000000000000019,2024-01-01T23:58:12+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,,f_limit_price,success
db4c02ec6592e0100000000000000019,2024-01-01T23:58:12+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,,f_normal_price,success
db4c02ec6592e0100000000000000019,2024-01-01T23:58:12+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,,f_revoke,success
db4c02ec6592e0100000000000000019,2024-01-01T23:58:12+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,,f_total_point,success
db4c02ec6592e0100000000000000019,2024-01-01T23:58:13+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,from v_hbgorderdb_t_point_buy_back_stat;,success
db4c02ec6592e0100000000000000019,2024-01-02T00:00:11+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,use proacctdb_1;,success
db4c02ec6592e0100000000000000019,2024-01-02T00:00:18+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,SELECT ,success
db4c02ec6592e0100000000000000019,2024-01-02T00:00:18+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,`f_db`,success
db4c02ec6592e0100000000000000019,2024-01-02T00:00:18+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,, `f_currency`,success
db4c02ec6592e0100000000000000019,2024-01-02T00:00:18+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,, `f_balance`,success
db4c02ec6592e0100000000000000019,2024-01-02T00:00:18+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,, `f_suspense`,success
db4c02ec6592e0100000000000000019,2024-01-02T00:00:18+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,, `f_non_margin_suspense`,success
db4c02ec6592e0100000000000000019,2024-01-02T00:00:18+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,, `f_clearing`,success
db4c02ec6592e0100000000000000019,2024-01-02T00:00:18+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,, `f_date`),success
db4c02ec6592e0100000000000000019,2024-01-02T00:00:18+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,insert into t_currency_stat(,success
db4c02ec6592e0100000000000000019,2024-01-02T00:00:18+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,`f_db`,success
db4c02ec6592e0100000000000000019,2024-01-02T00:00:18+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,, `f_currency`,success
db4c02ec6592e0100000000000000019,2024-01-02T00:00:18+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,, `f_balance`,success
db4c02ec6592e0100000000000000019,2024-01-02T00:00:18+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,, `f_suspense`,success
db4c02ec6592e0100000000000000019,2024-01-02T00:00:18+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,, `f_non_margin_suspense`,success
db4c02ec6592e0100000000000000019,2024-01-02T00:00:18+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,, `f_clearing`,success
db4c02ec6592e0100000000000000019,2024-01-02T00:00:19+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,, DATE_FORMAT(DATE_ADD(CURDATE(), INTERVAL -1 DAY),'%Y-%m-%d') as `f_date` ,success
db4c02ec6592e0100000000000000019,2024-01-02T00:00:19+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,from v_proacctdb_t_subaccount_stat;,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:25+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,use hbgorderdb;,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:32+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,,f_total_point,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:32+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,f_state,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:32+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,insert into t_point_stat(,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,,f_limit_point,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,,f_normal_point,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,,f_total_price,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,,f_limit_price,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,,f_normal_price,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,,f_revoke,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,,f_date,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,),success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,select ,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,f_state,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,,f_total_point,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,,f_limit_point,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,,f_normal_point,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,,f_total_price,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,,f_limit_price,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,,f_revoke,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,,f_normal_price,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:34+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,from v_hbgorderdb_t_point_buy_back_stat;,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:34+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,, DATE_FORMAT(DATE_ADD(CURDATE(), INTERVAL -1 DAY),'%Y-%m-%d') as `f_date` ,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:47+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,use proacctdb_1;,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:53+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,SELECT f_currency,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:54+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    ),success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:54+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    ,sum(f_balance) as f_balance,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:54+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    ,sum(f_suspense) as f_suspense,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:54+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    , `f_date` ,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:54+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,from t_currency_stat,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:54+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,where,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:54+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,and f_currency not in(,success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:54+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    'a1b1', 'a1b2', 'a2b2', 'a3b3', 'a3b4', 'a6b6', 'a7a7', 'a7b7', 'a8a8', 'a8s8', 'a9a9', 'b1b1', 'b1b2', 'b1s1', 'b2b2', 'b2b3', 'b3b4', 'b3l3', 'b3s3', 'c2d2', 'c3d3', 'c4d4', 'c5d5', 'c6d6', 'c7d7', 'c8d8', 'c9d9', 'csb001', 'csb002', 'csb003', 'csb005', 'csb006', 'csb007', 'd8f8', 'e1f1', 'e2f2', 'e3f3', 'e4f4', 'e5f5', 'e6f6', 'e7f7', 'e8f8', 'e9f9', 'h1h1', 'h2h2', 'h3h3', 'h4h4', 'h5h5', 'h6h6', 'j1j1', 'j2j2', 'j3j3', 'j4j4', 'j5j5', 'q1q2', 't1t1', 't2t2', 'v1v2', 'v2v3', 'v3v4', 'v4v5', 'w1j1', 'w2j2', 'w3j3', 'w4j4', 'w5j5', 'w6j6', 'x6z7', 'x8z9', 'y1y2', 'y2y3', 'y3y4', 'z1z2',success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:54+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,f_date = DATE_FORMAT(NOW(),'%Y-%m-%d'),success
db4c02ec6592e0100000000000000019,2024-01-02T00:01:55+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,group by `f_currency`, `f_date`;,success
db4c02ec6592e0100000000000000019,2024-01-02T00:02:06+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,use proacctdb_1;,success
db4c02ec6592e0100000000000000019,2024-01-02T00:02:13+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,SELECT f_currency,success
db4c02ec6592e0100000000000000019,2024-01-02T00:02:13+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    ,sum(f_balance) as f_balance,success
db4c02ec6592e0100000000000000019,2024-01-02T00:02:13+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    ,sum(f_suspense) as f_suspense,success
db4c02ec6592e0100000000000000019,2024-01-02T00:02:13+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    , `f_date` ,success
db4c02ec6592e0100000000000000019,2024-01-02T00:02:14+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,from t_currency_stat,success
db4c02ec6592e0100000000000000019,2024-01-02T00:02:14+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,where,success
db4c02ec6592e0100000000000000019,2024-01-02T00:02:14+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,f_date = DATE_FORMAT(DATE_ADD(CURDATE(), INTERVAL -1 DAY),'%Y-%m-%d'),success
db4c02ec6592e0100000000000000019,2024-01-02T00:02:14+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,and f_currency not in(,success
db4c02ec6592e0100000000000000019,2024-01-02T00:02:14+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    'a1b1', 'a1b2', 'a2b2', 'a3b3', 'a3b4', 'a6b6', 'a7a7', 'a7b7', 'a8a8', 'a8s8', 'a9a9', 'b1b1', 'b1b2', 'b1s1', 'b2b2', 'b2b3', 'b3b4', 'b3l3', 'b3s3', 'c2d2', 'c3d3', 'c4d4', 'c5d5', 'c6d6', 'c7d7', 'c8d8', 'c9d9', 'csb001', 'csb002', 'csb003', 'csb005', 'csb006', 'csb007', 'd8f8', 'e1f1', 'e2f2', 'e3f3', 'e4f4', 'e5f5', 'e6f6', 'e7f7', 'e8f8', 'e9f9', 'h1h1', 'h2h2', 'h3h3', 'h4h4', 'h5h5', 'h6h6', 'j1j1', 'j2j2', 'j3j3', 'j4j4', 'j5j5', 'q1q2', 't1t1', 't2t2', 'v1v2', 'v2v3', 'v3v4', 'v4v5', 'w1j1', 'w2j2', 'w3j3', 'w4j4', 'w5j5', 'w6j6', 'x6z7', 'x8z9', 'y1y2', 'y2y3', 'y3y4', 'z1z2',success
db4c02ec6592e0100000000000000019,2024-01-02T00:02:14+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    ),success
db4c02ec6592e0100000000000000019,2024-01-02T00:02:14+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,group by `f_currency`, `f_date`;,success
db4c02ec6592e0100000000000000019,2024-01-02T00:02:40+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,exit,success
db4c02ec6592e0100000000000000019,2024-01-02T00:02:43+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,exit,success
3a110d3e659370c7000000000000000d,2024-01-02T10:11:36+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,cd /hbdata/soft/miniglobal-apps/apps/miniglobal/miniglobal-hbg-account-manage/,success
3a110d3e659370c7000000000000000d,2024-01-02T10:11:37+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,ll,success
3a110d3e659370c7000000000000000d,2024-01-02T10:13:11+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,make build TAG=PRD_T20240102,success
6da091f46593d6e40000000000000013,2024-01-02T17:27:34+08:00,cmd.Command,avenir_forensic_deploy,218.12.19.37,sec-forensic-01,************,sec-forensic-01][         0$ mycli  1-$ ~  (2*$~)  3$ ..2322a9/result  4$ vim  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl         ][02/01  9:27],success
6da091f46593d6e40000000000000013,2024-01-02T17:28:02+08:00,cmd.Command,avenir_forensic_deploy,218.12.19.37,sec-forensic-01,************,use soc,success
6da091f46593d6e40000000000000013,2024-01-02T17:28:04+08:00,cmd.Command,avenir_forensic_deploy,218.12.19.37,sec-forensic-01,************,;,success
6da091f46593d6e40000000000000013,2024-01-02T17:28:30+08:00,cmd.Command,avenir_forensic_deploy,218.12.19.37,sec-forensic-01,************,select * from forensic_event where id=4746 \G,success
6da091f46593d6e40000000000000013,2024-01-02T17:28:52+08:00,cmd.Command,avenir_forensic_deploy,218.12.19.37,sec-forensic-01,************,docker restart oret,success
6da091f46593d6e40000000000000013,2024-01-02T17:29:04+08:00,cmd.Command,avenir_forensic_deploy,218.12.19.37,sec-forensic-01,************,sec-forensic-01][         0-$ mycli  1$ ~  (2*$~)  3$ ..2322a9/result  4$ vim  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl         ][02/01  9:29],success
bb1c500b6593d9190000000000000013,2024-01-02T17:36:26+08:00,cmd.Command,avenir_forensic_deploy,218.12.17.123,sec-forensic-01,************,y,success
bb1c500b6593d9190000000000000013,2024-01-02T17:36:39+08:00,cmd.Command,avenir_forensic_deploy,218.12.17.123,sec-forensic-01,************,╰─[:)] # screen -Dr f,success
bb1c500b6593d9190000000000000013,2024-01-02T17:37:58+08:00,cmd.Command,avenir_forensic_deploy,218.12.17.123,sec-forensic-01,************,sec-forensic-01][      0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$vim)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl       ][02/01  9:38],success
bb1c500b6593d9190000000000000013,2024-01-02T17:37:59+08:00,cmd.Command,avenir_forensic_deploy,218.12.17.123,sec-forensic-01,************,sec-forensic-01][  0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$..c_etl/cs_task)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl  ][02/01  9:,success
bb1c500b6593d9190000000000000013,2024-01-02T17:38:03+08:00,cmd.Command,avenir_forensic_deploy,218.12.17.123,sec-forensic-01,************,sec-forensic-01][  0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$..c_etl/cs_task)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl  ][02/01  9:,success
bb1c500b6593d9190000000000000013,2024-01-02T17:38:04+08:00,cmd.Command,avenir_forensic_deploy,218.12.17.123,sec-forensic-01,************,sec-forensic-01][  0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$~/forensic_etl)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl  ][02/01  9:3,success
bb1c500b6593d9190000000000000013,2024-01-02T17:38:06+08:00,cmd.Command,avenir_forensic_deploy,218.12.17.123,sec-forensic-01,************,sec-forensic-01][  0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$~/forensic_etl)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl  ][02/01  9:3,success
bb1c500b6593d9190000000000000013,2024-01-02T17:38:07+08:00,cmd.Command,avenir_forensic_deploy,218.12.17.123,sec-forensic-01,************,sec-forensic-01][  0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$..forensic_task)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl  ][02/01  9:,success
bb1c500b6593d9190000000000000013,2024-01-02T17:38:19+08:00,cmd.Command,avenir_forensic_deploy,218.12.17.123,sec-forensic-01,************,sec-forensic-01][  0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$..forensic_task)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl  ][02/01  9:,success
bb1c500b6593d9190000000000000013,2024-01-02T17:38:27+08:00,cmd.Command,avenir_forensic_deploy,218.12.17.123,sec-forensic-01,************,sec-forensic-01][      0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$vim)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl       ][02/01  9:38],success
bb1c500b6593d9190000000000000013,2024-01-02T17:38:30+08:00,cmd.Command,avenir_forensic_deploy,218.12.17.123,sec-forensic-01,************,sec-forensic-01][  0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$..forensic_task)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl  ][02/01  9:,success
bb1c500b6593d9190000000000000013,2024-01-02T17:38:33+08:00,cmd.Command,avenir_forensic_deploy,218.12.17.123,sec-forensic-01,************,sec-forensic-01][  0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$~/forensic_etl)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl  ][02/01  9:3,success
bb1c500b6593d9190000000000000013,2024-01-02T17:38:35+08:00,cmd.Command,avenir_forensic_deploy,218.12.17.123,sec-forensic-01,************,sec-forensic-01][  0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$..sic_etl/model)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl  ][02/01  9:,success
bb1c500b6593d9190000000000000013,2024-01-02T17:38:39+08:00,cmd.Command,avenir_forensic_deploy,218.12.17.123,sec-forensic-01,************,sec-forensic-01][  0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$..sic_etl/model)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl  ][02/01  9:,success
bb1c500b6593d9190000000000000013,2024-01-02T17:38:40+08:00,cmd.Command,avenir_forensic_deploy,218.12.17.123,sec-forensic-01,************,sec-forensic-01][  0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$..etl/model/otc)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl  ][02/01  9:,success
bb1c500b6593d9190000000000000013,2024-01-02T17:38:55+08:00,cmd.Command,avenir_forensic_deploy,218.12.17.123,sec-forensic-01,************,sec-forensic-01][  0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$..etl/model/otc)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl  ][02/01  9:,success
bb1c500b6593d9190000000000000013,2024-01-02T17:38:56+08:00,cmd.Command,avenir_forensic_deploy,218.12.17.123,sec-forensic-01,************,sec-forensic-01][  0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$~/forensic_etl)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl  ][02/01  9:3,success
bb1c500b6593d9190000000000000013,2024-01-02T17:38:58+08:00,cmd.Command,avenir_forensic_deploy,218.12.17.123,sec-forensic-01,************,sec-forensic-01][  0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$~/forensic_etl)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl  ][02/01  9:3,success
bb1c500b6593d9190000000000000013,2024-01-02T17:39:10+08:00,cmd.Command,avenir_forensic_deploy,218.12.17.123,sec-forensic-01,************,sec-forensic-01][      0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$vim)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl       ][02/01  9:39],success
bb1c500b6593d9190000000000000013,2024-01-02T17:39:27+08:00,cmd.Command,avenir_forensic_deploy,218.12.17.123,sec-forensic-01,************,sec-forensic-01][      0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$vim)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl       ][02/01  9:39],success
bb1c500b6593d9190000000000000013,2024-01-02T17:39:35+08:00,cmd.Command,avenir_forensic_deploy,218.12.17.123,sec-forensic-01,************,sec-forensic-01][  0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$~/forensic_etl)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl  ][02/01  9:3,success
bb1c500b6593d9190000000000000013,2024-01-02T17:40:06+08:00,cmd.Command,avenir_forensic_deploy,218.12.17.123,sec-forensic-01,************,sec-forensic-01][      0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$vim)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl       ][02/01  9:40],success
bb1c500b6593d9190000000000000013,2024-01-02T17:40:10+08:00,cmd.Command,avenir_forensic_deploy,218.12.17.123,sec-forensic-01,************,sec-forensic-01][  0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$~/forensic_etl)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl  ][02/01  9:4,success
bb1c500b6593d9190000000000000013,2024-01-02T17:40:14+08:00,cmd.Command,avenir_forensic_deploy,218.12.17.123,sec-forensic-01,************,sec-forensic-01][  0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$~/forensic_etl)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl  ][02/01  9:4,success
bb1c500b6593d9190000000000000013,2024-01-02T17:40:17+08:00,cmd.Command,avenir_forensic_deploy,218.12.17.123,sec-forensic-01,************,sec-forensic-01][  0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$~/forensic_etl)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl  ][02/01  9:4,success
bb1c500b6593d9190000000000000013,2024-01-02T17:40:19+08:00,cmd.Command,avenir_forensic_deploy,218.12.17.123,sec-forensic-01,************,sec-forensic-01][  0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$~/forensic_etl)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl  ][02/01  9:4,success
bb1c500b6593d9190000000000000013,2024-01-02T17:40:28+08:00,cmd.Command,avenir_forensic_deploy,218.12.17.123,sec-forensic-01,************,sec-forensic-01][      0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$vim)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl       ][02/01  9:40],success
bb1c500b6593d9190000000000000013,2024-01-02T17:40:35+08:00,cmd.Command,avenir_forensic_deploy,218.12.17.123,sec-forensic-01,************,sec-forensic-01][      0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$vim)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl       ][02/01  9:40],success
bb1c500b6593d9190000000000000013,2024-01-02T17:40:38+08:00,cmd.Command,avenir_forensic_deploy,218.12.17.123,sec-forensic-01,************,sec-forensic-01][  0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$~/forensic_etl)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl  ][02/01  9:4,success
b60b30896593f486000000000000000d,2024-01-02T19:33:30+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
b60b30896593f486000000000000000d,2024-01-02T19:35:19+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,use huobi,success
b60b30896593f486000000000000000d,2024-01-02T19:35:22+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,show tables;,success
b60b30896593f486000000000000000d,2024-01-02T19:37:07+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,show create table bank_transfer;,success
b60b30896593f486000000000000000d,2024-01-02T19:37:52+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from bank_transfer where user_id = 1008654 limit 2;,success
b60b30896593f486000000000000000d,2024-01-02T19:37:59+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from bank_transfer where user_id = ******** limit 2;,success
b60b30896593f486000000000000000d,2024-01-02T19:38:24+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,desc bank_transfer_receipt_history;,success
b60b30896593f486000000000000000d,2024-01-02T19:39:14+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,desc bityes_balance_transfer;,success
b60b30896593f486000000000000000d,2024-01-02T19:39:35+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from bityes_balance_transfer where user_id=1008654 limit 2;,success
b60b30896593f486000000000000000d,2024-01-02T19:39:39+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from bityes_balance_transfer where user_id=******** limit 2;,success
b60b30896593f486000000000000000d,2024-01-02T19:39:59+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,desc deposit_history;,success
b60b30896593f486000000000000000d,2024-01-02T19:40:19+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from deposit_history where user_id=1008654 limit 2;,success
b60b30896593f486000000000000000d,2024-01-02T19:41:12+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,show create table deposit_history;,success
b60b30896593f486000000000000000d,2024-01-02T19:42:25+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from deposit_history where user_id=1008654 limit 2 \G;,success
b60b30896593f486000000000000000d,2024-01-02T19:44:13+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,show create table withdraw_history;,success
b60b30896593f486000000000000000d,2024-01-02T19:45:47+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from withdraw_history where withdraw_account='1NpNDLx5Koq4uwpywSJWnwvggsh8zR45tz' limit 2;,success
b60b30896593f486000000000000000d,2024-01-02T19:46:42+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from withdraw_history where out_trade_id = '0ce676c4542737d5ba655323c38ef658f346e0f7665373d78441e551d2b21afc';,success
b60b30896593f486000000000000000d,2024-01-02T19:47:15+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,seselect count(*) from withdraw_history;,success
b60b30896593f486000000000000000d,2024-01-02T19:47:28+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select count(*) from withdraw_history_0;,success
b60b30896593f486000000000000000d,2024-01-02T19:47:46+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select count(*) from withdraw_history_o;,success
b60b30896593f486000000000000000d,2024-01-02T19:49:01+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,show tables like '%channel%';,success
b60b30896593f486000000000000000d,2024-01-02T19:50:21+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from channel limit 10;,success
b60b30896593f486000000000000000d,2024-01-02T19:50:29+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from my_channel limit 10;,success
b60b30896593f486000000000000000d,2024-01-02T19:52:30+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from withdraw_history where user_id = 1008654 limit 10;,success
b60b30896593f486000000000000000d,2024-01-02T19:59:58+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from my_channel where user_id=1008654;,success
b60b30896593f486000000000000000d,2024-01-02T20:01:22+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,show tables;,success
b60b30896593f486000000000000000d,2024-01-02T20:03:33+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,show tables like '%ltc%';,success
b60b30896593f486000000000000000d,2024-01-02T20:03:55+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,show tables like '%auth%';,success
b60b30896593f486000000000000000d,2024-01-02T20:04:05+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,desc auth_items;,success
b60b30896593f486000000000000000d,2024-01-02T20:04:17+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,desc auth_user;,success
b60b30896593f486000000000000000d,2024-01-02T20:04:45+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,;,success
b60b30896593f486000000000000000d,2024-01-02T20:05:02+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,user;,success
b60b30896593f486000000000000000d,2024-01-02T20:05:32+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,desc user_auth_bankcard;,success
b60b30896593f486000000000000000d,2024-01-02T20:05:46+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from user_auth_bankcard where user_id=1008654;,success
b60b30896593f486000000000000000d,2024-01-02T20:05:51+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from user_auth_bankcard where user_id=********;,success
b60b30896593f486000000000000000d,2024-01-02T20:06:18+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,show tables like '%bank%';,success
b60b30896593f486000000000000000d,2024-01-02T20:06:25+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,show tables like '%order%';,success
b60b30896593f486000000000000000d,2024-01-02T20:10:21+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from my_channel where account = '1NpNDLx5Koq4uwpywSJWnwvggsh8zR45tz' limit 10;,success
b60b30896593f486000000000000000d,2024-01-02T20:10:40+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from my_channel where account like '1NpNDLx5Koq4uwpywSJWnwvggsh8zR45tz' limit 10;,success
b60b30896593f486000000000000000d,2024-01-02T20:11:28+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from withdraw_history where withdraw_account like '%1NpNDLx5Koq4uwpywSJWnwvggsh8zR45tz%' limit 2;,success
b60b30896593f486000000000000000d,2024-01-02T20:16:42+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,desc login_history;,success
b60b30896593f486000000000000000d,2024-01-02T20:17:08+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from login_history where user_id=1008654 order by id desc limit 10;,success
b60b30896593f486000000000000000d,2024-01-02T20:18:05+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from login_history where user_id=1008654 order by id asc limit 10;,success
b60b30896593f486000000000000000d,2024-01-02T20:19:53+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from login_history where user_id=1008654 order by id desc limit 20;,success
b60b30896593f486000000000000000d,2024-01-02T20:24:15+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,quit,success
1ce125906594c63b0000000000000019,2024-01-03T10:28:17+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
1ce125906594c63b0000000000000019,2024-01-03T10:28:31+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********,use proamsdwdb;,success
1ce125906594c63b0000000000000019,2024-01-03T10:28:39+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********,where f_state in(3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
1ce125906594c63b0000000000000019,2024-01-03T10:28:39+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********,group by f_currency;,success
1ce125906594c63b0000000000000019,2024-01-03T10:28:39+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
1ce125906594c63b0000000000000019,2024-01-03T10:43:00+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********,group by f_currency;,success
1ce125906594c63b0000000000000019,2024-01-03T10:43:00+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount)) as amount from t_withdraw_virtual w ,success
1ce125906594c63b0000000000000019,2024-01-03T10:43:00+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********,where f_currency in('btc','eth','usdt','ht') ,success
1ce125906594c63b0000000000000019,2024-01-03T10:43:00+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********,and from_unixtime(f_updated_at/1000) > '2023-01-06',success
1ce125906594c63b0000000000000019,2024-01-03T10:43:00+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********,and f_state in(3,6,8),success
1ce125906594c63b0000000000000019,2024-01-03T10:52:25+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date ,success
1ce125906594c63b0000000000000019,2024-01-03T10:52:26+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********,from t_withdraw_virtual  ,success
1ce125906594c63b0000000000000019,2024-01-03T10:52:26+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********,where f_state in (3,6,8),success
1ce125906594c63b0000000000000019,2024-01-03T10:52:26+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********, and from_unixtime(f_updated_at/1000) > '2022-11-01',success
1ce125906594c63b0000000000000019,2024-01-03T10:52:26+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********, and f_currency in('btc','eth') ,success
1ce125906594c63b0000000000000019,2024-01-03T10:52:26+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********,order by f_currency;,success
1ce125906594c63b0000000000000019,2024-01-03T11:01:31+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********,use hbg_trade; ,success
1ce125906594c63b0000000000000019,2024-01-03T11:02:04+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********,select f_state, count(*) as user_count,success
1ce125906594c63b0000000000000019,2024-01-03T11:02:04+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********,from t_account_manage_fee_snapshot  ,success
1ce125906594c63b0000000000000019,2024-01-03T11:02:04+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********,where f_day in('********', '********'),success
1ce125906594c63b0000000000000019,2024-01-03T11:02:05+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********,group by f_state;,success
1ce125906594c63b0000000000000019,2024-01-03T11:06:45+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********,select f_currency,sum(f_amount),success
1ce125906594c63b0000000000000019,2024-01-03T11:06:45+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********,from t_account_manage_fee_snapshot,success
1ce125906594c63b0000000000000019,2024-01-03T11:06:48+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********,where f_day in('********', '********')  and f_state = 0,success
1ce125906594c63b0000000000000019,2024-01-03T11:06:53+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********,group by f_currency order by f_currency;,success
1ce125906594c63b0000000000000019,2024-01-03T11:07:47+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********,1,success
1ce125906594c63b0000000000000019,2024-01-03T11:12:34+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********,select f_state, count(*) as user_count,success
1ce125906594c63b0000000000000019,2024-01-03T11:12:34+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********,from t_account_manage_fee_snapshot  ,success
1ce125906594c63b0000000000000019,2024-01-03T11:12:34+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********,where f_day in('********', '********'),success
1ce125906594c63b0000000000000019,2024-01-03T11:12:35+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********,group by f_state;,success
94fd46906594d0bb000000000000000d,2024-01-03T11:13:07+08:00,cmd.Command,avenir_user01,223.104.40.183,miniglobal-devops,***********,ping nc.docsl.com,success
94fd46906594d0bb000000000000000d,2024-01-03T11:13:25+08:00,cmd.Command,avenir_user01,223.104.40.183,miniglobal-devops,***********,ping baidu.com,success
94fd46906594d0bb000000000000000d,2024-01-03T11:13:34+08:00,cmd.Command,avenir_user01,223.104.40.183,miniglobal-devops,***********,ping nc.docsl.com,success
94fd46906594d0bb000000000000000d,2024-01-03T11:14:25+08:00,cmd.Command,avenir_user01,223.104.40.183,miniglobal-devops,***********,ping nc.docsl.com,success
1ce125906594c63b0000000000000019,2024-01-03T11:16:07+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********,exit,success
1ce125906594c63b0000000000000019,2024-01-03T11:16:09+08:00,cmd.Command,xiexinyu,182.239.93.157,miniglobal-devops,***********,exit,success
84d1cf6a6594d1ae000000000000000d,2024-01-03T11:17:04+08:00,cmd.Command,avenir_user01,223.104.40.183,miniglobal-devops,***********,ssh 172.28.2.38,success
84d1cf6a6594d1ae000000000000000d,2024-01-03T11:17:17+08:00,cmd.Command,avenir_user01,223.104.40.183,miniglobal-devops,***********,cd /yonyou/nchomesc/,success
84d1cf6a6594d1ae000000000000000d,2024-01-03T11:17:19+08:00,cmd.Command,avenir_user01,223.104.40.183,miniglobal-devops,***********,ls,success
84d1cf6a6594d1ae000000000000000d,2024-01-03T11:17:24+08:00,cmd.Command,avenir_user01,223.104.40.183,miniglobal-devops,***********,./stop.sh,success
84d1cf6a6594d1ae000000000000000d,2024-01-03T11:17:38+08:00,cmd.Command,avenir_user01,223.104.40.183,miniglobal-devops,***********,./startup.sh,success
84d1cf6a6594d1ae000000000000000d,2024-01-03T11:18:44+08:00,cmd.Command,avenir_user01,223.104.40.183,miniglobal-devops,***********,cd ..,success
84d1cf6a6594d1ae000000000000000d,2024-01-03T11:18:45+08:00,cmd.Command,avenir_user01,223.104.40.183,miniglobal-devops,***********,ll,success
84d1cf6a6594d1ae000000000000000d,2024-01-03T11:18:50+08:00,cmd.Command,avenir_user01,223.104.40.183,miniglobal-devops,***********,cd searchserver/,success
84d1cf6a6594d1ae000000000000000d,2024-01-03T11:19:00+08:00,cmd.Command,avenir_user01,223.104.40.183,miniglobal-devops,***********,ll,success
84d1cf6a6594d1ae000000000000000d,2024-01-03T11:19:06+08:00,cmd.Command,avenir_user01,223.104.40.183,miniglobal-devops,***********,./stop.sh,success
84d1cf6a6594d1ae000000000000000d,2024-01-03T11:19:17+08:00,cmd.Command,avenir_user01,223.104.40.183,miniglobal-devops,***********,./startup.sh,success
7bb1eaca6594f236000000000000000d,2024-01-03T13:35:55+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
7bb1eaca6594f236000000000000000d,2024-01-03T13:36:26+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,show databases like '%hbg%';,success
7bb1eaca6594f236000000000000000d,2024-01-03T13:36:34+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,use hbg_trade,success
7bb1eaca6594f236000000000000000d,2024-01-03T13:36:38+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,show tables;,success
7bb1eaca6594f236000000000000000d,2024-01-03T13:37:08+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,show create table t_account_manage_fee_transfer_detail;,success
7bb1eaca6594f236000000000000000d,2024-01-03T13:37:42+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,show create table t_account_manage_fee_transfer_detail \G;,success
7bb1eaca6594f236000000000000000d,2024-01-03T13:42:40+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select count(1) from t_account_manage_fee_transfer_detail;,success
7bb1eaca6594f236000000000000000d,2024-01-03T13:43:15+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select sum(1) from t_account_manage_fee_transfer_detail;,success
7bb1eaca6594f236000000000000000d,2024-01-03T13:45:44+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from t_account_manage_fee_transfer_detail order by f_id desc limit 2;,success
2fd75191659540920000000000000017,2024-01-03T19:36:59+08:00,file.UploadSave,shaopeng.jiao,111.30.246.69,miniglobal-sec-sep-1c,172.28.1.162,keyboard.txt,success
2fd75191659540920000000000000017,2024-01-03T19:36:59+08:00,file.Upload,shaopeng.jiao,111.30.246.69,miniglobal-sec-sep-1c,172.28.1.162,ocr.txt,success
bb83be84659546d60000000000000017,2024-01-03T19:43:53+08:00,file.UploadSave,shaopeng.jiao,111.30.246.69,miniglobal-sec-sep-1c,172.28.1.162,keyboard.txt,success
bb83be84659546d60000000000000017,2024-01-03T19:43:53+08:00,file.Upload,shaopeng.jiao,111.30.246.69,miniglobal-sec-sep-1c,172.28.1.162,ocr.txt,success
6bd4ffe7659548750000000000000017,2024-01-03T21:22:37+08:00,file.Upload,shaopeng.jiao,111.30.246.69,miniglobal-sec-sep-1c,172.28.1.162,Administrator.txt,success
6bd4ffe7659548750000000000000017,2024-01-03T22:06:21+08:00,file.UploadSave,shaopeng.jiao,111.30.246.69,miniglobal-sec-sep-1c,172.28.1.162,keyboard.txt,success
6bd4ffe7659548750000000000000017,2024-01-03T22:06:21+08:00,file.Upload,shaopeng.jiao,111.30.246.69,miniglobal-sec-sep-1c,172.28.1.162,ocr.txt,success
339b5f5165956de90000000000000017,2024-01-03T22:57:09+08:00,file.Download,shaopeng.jiao,111.30.246.69,miniglobal-sec-sep-1c,172.28.1.162,DESKTOP-2CU4I75.txt,success
339b5f5165956de90000000000000017,2024-01-03T22:57:29+08:00,file.Download,shaopeng.jiao,111.30.246.69,miniglobal-sec-sep-1c,172.28.1.162,DESKTOP-G1U9G4F	Offline	sandyzhou	2024年1月3日 下午12:22	01 02 20.txt,success
339b5f5165956de90000000000000017,2024-01-03T23:57:04+08:00,file.UploadSave,shaopeng.jiao,111.30.246.69,miniglobal-sec-sep-1c,172.28.1.162,keyboard.txt,success
339b5f5165956de90000000000000017,2024-01-03T23:57:04+08:00,file.Upload,shaopeng.jiao,111.30.246.69,miniglobal-sec-sep-1c,172.28.1.162,ocr.txt,success
bdcdb1da6596093d000000000000000d,2024-01-04T09:26:28+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd /var/log/hue,success
bdcdb1da6596093d000000000000000d,2024-01-04T09:26:29+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
bdcdb1da6596093d000000000000000d,2024-01-04T09:27:54+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,tar zcvf /tmp/runcpserver.log.********-02.tar.gz runcpserver.log runcpserver.log.1 runcpserver.log.2,success
fc0416ad659609a4000000000000000d,2024-01-04T09:28:09+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-03,172.28.3.121,cd /var/log/hue,success
fc0416ad659609a4000000000000000d,2024-01-04T09:28:10+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-03,172.28.3.121,ll,success
fc0416ad659609a4000000000000000d,2024-01-04T09:28:42+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-03,172.28.3.121,tar zcvf /tmp/runcpserver.log.********-03.tar.gz runcpserver.log runcpserver.log.1 runcpserver.log.2,success
babe8fbe659609cf000000000000000d,2024-01-04T09:28:52+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-05,172.28.3.93,cd /var/log/hue,success
babe8fbe659609cf000000000000000d,2024-01-04T09:28:53+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-05,172.28.3.93,ll,success
babe8fbe659609cf000000000000000d,2024-01-04T09:29:32+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-05,172.28.3.93,tar zcvf /tmp/runcpserver.log.********-05.tar.gz runcpserver.log runcpserver.log.1,success
3eaad39d65960a04000000000000000d,2024-01-04T09:29:42+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /tmp,success
3eaad39d65960a04000000000000000d,2024-01-04T09:29:43+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
3eaad39d65960a04000000000000000d,2024-01-04T09:30:05+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,scp 172.28.3.8:/tmp/runcpserver.log.********-02.tar.gz .,success
3eaad39d65960a04000000000000000d,2024-01-04T09:30:39+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,scp 172.28.3.121:/tmp/runcpserver.log.********-03.tar.gz .,success
3eaad39d65960a04000000000000000d,2024-01-04T09:30:47+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,scp 172.28.3.93:/tmp/runcpserver.log.********-05.tar.gz .,success
3eaad39d65960a04000000000000000d,2024-01-04T09:30:48+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
3eaad39d65960a04000000000000000d,2024-01-04T09:31:06+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ossutil cp runcpserver.log.********-02.tar.gz oss://miniglobal-devops/hue_log/,success
3eaad39d65960a04000000000000000d,2024-01-04T09:31:10+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ossutil cp runcpserver.log.********-03.tar.gz oss://miniglobal-devops/hue_log/,success
3eaad39d65960a04000000000000000d,2024-01-04T09:31:14+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ossutil cp runcpserver.log.********-05.tar.gz oss://miniglobal-devops/hue_log/,success
3eaad39d65960a04000000000000000d,2024-01-04T10:52:34+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
3eaad39d65960a04000000000000000d,2024-01-04T10:52:39+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,rm -f runcpserver.log.********-0*,success
3eaad39d65960a04000000000000000d,2024-01-04T10:52:41+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
256911f465961d7f000000000000000d,2024-01-04T10:52:52+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,rm /tmp/runcpserver.log.********-02.tar.gz,success
256911f465961d7f000000000000000d,2024-01-04T10:52:53+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,y,success
6284647465961d89000000000000000d,2024-01-04T10:53:01+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-03,172.28.3.121,rm -f /tmp/runcpserver.log.********-03.tar.gz,success
9e61c5e565961d90000000000000000d,2024-01-04T10:53:08+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-05,172.28.3.93,rm -f /tmp/runcpserver.log.********-05.tar.gz,success
1a94dde665961fe40000000000000016,2024-01-04T11:06:05+08:00,cmd.Command,avenir_yigeyun_deploy_user01,114.254.2.8,yige-vpn-connector-1,172.28.1.224,curl https://mgt-console.cogdocs.com,success
1a94dde665961fe40000000000000016,2024-01-04T11:06:12+08:00,cmd.Command,avenir_yigeyun_deploy_user01,114.254.2.8,yige-vpn-connector-1,172.28.1.224,ping mgt-console.cogdocs.com,success
1a94dde665961fe40000000000000016,2024-01-04T11:06:16+08:00,cmd.Command,avenir_yigeyun_deploy_user01,114.254.2.8,yige-vpn-connector-1,172.28.1.224,telnet mgt-console.cogdocs.com 443,success
1a94dde665961fe40000000000000016,2024-01-04T11:06:21+08:00,cmd.Command,avenir_yigeyun_deploy_user01,114.254.2.8,yige-vpn-connector-1,172.28.1.224,yum install telnet ,success
1a94dde665961fe40000000000000016,2024-01-04T11:06:29+08:00,cmd.Command,avenir_yigeyun_deploy_user01,114.254.2.8,yige-vpn-connector-1,172.28.1.224,y,success
1a94dde665961fe40000000000000016,2024-01-04T11:06:31+08:00,cmd.Command,avenir_yigeyun_deploy_user01,114.254.2.8,yige-vpn-connector-1,172.28.1.224,telnet mgt-console.cogdocs.com 443,success
1a94dde665961fe40000000000000016,2024-01-04T11:06:52+08:00,cmd.Command,avenir_yigeyun_deploy_user01,114.254.2.8,yige-vpn-connector-1,172.28.1.224,ifconfig,success
1a94dde665961fe40000000000000016,2024-01-04T11:07:10+08:00,cmd.Command,avenir_yigeyun_deploy_user01,114.254.2.8,yige-vpn-connector-1,172.28.1.224,ping  mgt-console.cogdocs.com,success
1a94dde665961fe40000000000000016,2024-01-04T11:07:18+08:00,cmd.Command,avenir_yigeyun_deploy_user01,114.254.2.8,yige-vpn-connector-1,172.28.1.224,telnet mgt-console.cogdocs.com 443,success
3822c7b965961eb40000000000000017,2024-01-04T11:36:29+08:00,file.UploadSave,shaopeng.jiao,111.30.246.69,miniglobal-sec-sep-1c,172.28.1.162,keyboard.txt,success
3822c7b965961eb40000000000000017,2024-01-04T11:36:29+08:00,file.Upload,shaopeng.jiao,111.30.246.69,miniglobal-sec-sep-1c,172.28.1.162,ocr.txt,success
8a369cd365962f930000000000000017,2024-01-04T12:27:36+08:00,file.Upload,shaopeng.jiao,111.30.246.69,miniglobal-sec-sep-1c,172.28.1.162,keyboard.txt,success
8a369cd365962f930000000000000017,2024-01-04T12:27:36+08:00,file.Upload,shaopeng.jiao,111.30.246.69,miniglobal-sec-sep-1c,172.28.1.162,ocr.txt,success
6285dc7365967423000000000000000d,2024-01-04T17:02:45+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ssh 172.28.1.224,success
6285dc7365967423000000000000000d,2024-01-04T17:07:26+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ssh 172.28.1.224,success
7b46266f659760b30000000000000019,2024-01-05T09:51:54+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com ,success
7b46266f659760b30000000000000019,2024-01-05T09:52:10+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,use proamsdwdb;,success
7b46266f659760b30000000000000019,2024-01-05T09:52:19+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
7b46266f659760b30000000000000019,2024-01-05T09:52:19+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where f_state in(3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
7b46266f659760b30000000000000019,2024-01-05T09:52:19+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,group by f_currency;,success
7b46266f659760b30000000000000019,2024-01-05T10:30:01+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,exit,success
7b46266f659760b30000000000000019,2024-01-05T10:30:35+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
7b46266f659760b30000000000000019,2024-01-05T10:50:19+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,use hbg_trade; ,success
7b46266f659760b30000000000000019,2024-01-05T10:50:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,and f_currency in ('btc', 'eth','usdt','ht','shib','doge','xrp','eos','ltc','trx'),success
7b46266f659760b30000000000000019,2024-01-05T10:50:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from t_account_manage_fee_snapshot,success
7b46266f659760b30000000000000019,2024-01-05T10:50:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where f_day in('********', '********')  and f_state = 1 ,success
7b46266f659760b30000000000000019,2024-01-05T10:50:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_currency,sum(f_amount),success
7b46266f659760b30000000000000019,2024-01-05T10:50:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,group by f_currency order by f_currency;,success
7b46266f659760b30000000000000019,2024-01-05T10:55:59+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from t_account_manage_fee_snapshot  ,success
7b46266f659760b30000000000000019,2024-01-05T10:55:59+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_state, count(*) as user_count,success
7b46266f659760b30000000000000019,2024-01-05T10:56:00+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where f_day in('********', '********'),success
7b46266f659760b30000000000000019,2024-01-05T10:56:01+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,group by f_state;,success
7b46266f659760b30000000000000019,2024-01-05T11:00:26+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,inct f_currency from t_account_manage_fee_snapshot where f_day in('********', '********') ;,success
33add63f659771fe0000000000000017,2024-01-05T11:06:37+08:00,file.UploadSave,shaopeng.jiao,111.30.246.69,miniglobal-sec-sep-1c,172.28.1.162,keyboard.txt,success
33add63f659771fe0000000000000017,2024-01-05T11:06:37+08:00,file.Upload,shaopeng.jiao,111.30.246.69,miniglobal-sec-sep-1c,172.28.1.162,ocr.txt,success
7b46266f659760b30000000000000019,2024-01-05T11:08:47+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,mysql> select distinct f_currency from t_account_manage_fee_snapshot where f_day in('********', '********'),success
7b46266f659760b30000000000000019,2024-01-05T11:08:49+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,；,success
7b46266f659760b30000000000000019,2024-01-05T11:08:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,;,success
7b46266f659760b30000000000000019,2024-01-05T11:29:38+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,use hbg_trade;,success
7b46266f659760b30000000000000019,2024-01-05T11:29:46+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,use proamsdwdb;,success
7b46266f659760b30000000000000019,2024-01-05T11:29:52+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
7b46266f659760b30000000000000019,2024-01-05T11:29:52+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
7b46266f659760b30000000000000019,2024-01-05T11:29:52+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
7b46266f659760b30000000000000019,2024-01-05T11:29:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
7b46266f659760b30000000000000019,2024-01-05T11:29:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
7b46266f659760b30000000000000019,2024-01-05T11:29:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
7b46266f659760b30000000000000019,2024-01-05T11:29:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2023-12-28',success
7b46266f659760b30000000000000019,2024-01-05T11:29:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
7b46266f659760b30000000000000019,2024-01-05T11:29:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
7b46266f659760b30000000000000019,2024-01-05T11:29:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
7b46266f659760b30000000000000019,2024-01-05T11:29:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 100; ,success
7b46266f659760b30000000000000019,2024-01-05T11:29:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
7b46266f659760b30000000000000019,2024-01-05T11:29:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
7b46266f659760b30000000000000019,2024-01-05T11:31:29+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
7b46266f659760b30000000000000019,2024-01-05T11:31:29+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
7b46266f659760b30000000000000019,2024-01-05T11:31:29+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
7b46266f659760b30000000000000019,2024-01-05T11:31:29+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
7b46266f659760b30000000000000019,2024-01-05T11:31:29+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
7b46266f659760b30000000000000019,2024-01-05T11:31:29+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
7b46266f659760b30000000000000019,2024-01-05T11:31:29+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-01-04',success
7b46266f659760b30000000000000019,2024-01-05T11:31:29+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
7b46266f659760b30000000000000019,2024-01-05T11:31:29+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
7b46266f659760b30000000000000019,2024-01-05T11:31:29+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
7b46266f659760b30000000000000019,2024-01-05T11:31:29+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
7b46266f659760b30000000000000019,2024-01-05T11:31:29+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
7b46266f659760b30000000000000019,2024-01-05T11:31:30+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 100; ,success
7b46266f659760b30000000000000019,2024-01-05T11:32:26+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,0,success
7b46266f659760b30000000000000019,2024-01-05T11:33:29+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-01-04'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 10000;,success
7b46266f659760b30000000000000019,2024-01-05T11:34:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
7b46266f659760b30000000000000019,2024-01-05T11:34:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
7b46266f659760b30000000000000019,2024-01-05T11:34:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
7b46266f659760b30000000000000019,2024-01-05T11:34:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
7b46266f659760b30000000000000019,2024-01-05T11:34:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
7b46266f659760b30000000000000019,2024-01-05T11:34:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) > '2023-12-28' ,success
7b46266f659760b30000000000000019,2024-01-05T11:34:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-01-04',success
7b46266f659760b30000000000000019,2024-01-05T11:34:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
7b46266f659760b30000000000000019,2024-01-05T11:34:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
7b46266f659760b30000000000000019,2024-01-05T11:34:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
7b46266f659760b30000000000000019,2024-01-05T11:34:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
7b46266f659760b30000000000000019,2024-01-05T11:34:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
7b46266f659760b30000000000000019,2024-01-05T11:34:52+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 100;,success
e937bd8f659772390000000000000017,2024-01-05T11:42:54+08:00,file.UploadSave,shaopeng.jiao,111.30.246.69,miniglobal-sec-sep-1c,172.28.1.162,keyboard.txt,success
e937bd8f659772390000000000000017,2024-01-05T11:42:54+08:00,file.Upload,shaopeng.jiao,111.30.246.69,miniglobal-sec-sep-1c,172.28.1.162,ocr.txt,success
7b46266f659760b30000000000000019,2024-01-05T12:18:23+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,exit,success
7b46266f659760b30000000000000019,2024-01-05T12:18:24+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,exit,success
51c8039e65979f30000000000000000d,2024-01-05T14:18:27+08:00,cmd.Command,avenir_user01,223.104.41.119,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
51c8039e65979f30000000000000000d,2024-01-05T14:18:52+08:00,cmd.Command,avenir_user01,223.104.41.119,miniglobal-devops,***********,use hbg_trade,success
51c8039e65979f30000000000000000d,2024-01-05T14:18:57+08:00,cmd.Command,avenir_user01,223.104.41.119,miniglobal-devops,***********,show tables;,success
51c8039e65979f30000000000000000d,2024-01-05T14:19:49+08:00,cmd.Command,avenir_user01,223.104.41.119,miniglobal-devops,***********,desc t_account_manage_fee_snapshot;,success
51c8039e65979f30000000000000000d,2024-01-05T14:20:07+08:00,cmd.Command,avenir_user01,223.104.41.119,miniglobal-devops,***********,show create table t_account_manage_fee_snapshot;,success
51c8039e65979f30000000000000000d,2024-01-05T14:20:26+08:00,cmd.Command,avenir_user01,223.104.41.119,miniglobal-devops,***********,select * from t_account_manage_fee_snapshot order by f_id desc limit 1;,success
51c8039e65979f30000000000000000d,2024-01-05T14:21:28+08:00,cmd.Command,avenir_user01,223.104.41.119,miniglobal-devops,***********,select min(f_id) from t_account_manage_fee_snapshot where f_day = '********';,success
51c8039e65979f30000000000000000d,2024-01-05T14:23:37+08:00,cmd.Command,avenir_user01,223.104.41.119,miniglobal-devops,***********,select f_currency,count(*) from t_account_manage_fee_snapshot where f_id >= *********,success
51c8039e65979f30000000000000000d,2024-01-05T14:23:38+08:00,cmd.Command,avenir_user01,223.104.41.119,miniglobal-devops,***********,and f_currency in ("bcha","btg","lpt","pyr","yamv2","ht","inter","phb","husd","usd01","sbtc","ftm","enj","gxs","iota","ethf","req","ioi","o3","fox","kct","rai","gfx","wet","sht","nas","kok","iq","mzk","nest","war","gsl","eosdac","bcx","pai","edr","bcv","pizza","spn","ven","fbtc","retc","tnt","epra","hvt","eost","plo","efor","fac","xpt","pola","bt1","bt2","npxs","unic","csb001","csb002","csb003","csb005") group by f_currency;,success
51c8039e65979f30000000000000000d,2024-01-05T15:08:39+08:00,cmd.Command,avenir_user01,223.104.41.119,miniglobal-devops,***********,*********-*********quit,success
96ff8e2f6597aaff000000000000000d,2024-01-05T15:09:10+08:00,cmd.Command,avenir_user01,223.104.41.119,miniglobal-deploy,172.28.3.27,cd /hbdata/soft/miniglobal-apps/apps/miniglobal/miniglobal-hbg-account-manage/,success
96ff8e2f6597aaff000000000000000d,2024-01-05T15:09:11+08:00,cmd.Command,avenir_user01,223.104.41.119,miniglobal-deploy,172.28.3.27,ll,success
96ff8e2f6597aaff000000000000000d,2024-01-05T15:09:19+08:00,cmd.Command,avenir_user01,223.104.41.119,miniglobal-deploy,172.28.3.27,make build TAG=PRD_T********,success
174bd4b36597adda000000000000000d,2024-01-05T15:21:01+08:00,cmd.Command,avenir_user01,223.104.41.119,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
174bd4b36597adda000000000000000d,2024-01-05T15:21:24+08:00,cmd.Command,avenir_user01,223.104.41.119,miniglobal-devops,***********,use hbg_trade,success
174bd4b36597adda000000000000000d,2024-01-05T15:21:51+08:00,cmd.Command,avenir_user01,223.104.41.119,miniglobal-devops,***********,select * from t_account_manage_fee_snapshot order by f_id desc limit 1;,success
174bd4b36597adda000000000000000d,2024-01-05T15:22:24+08:00,cmd.Command,avenir_user01,223.104.41.119,miniglobal-devops,***********,select * from t_account_manage_fee_snapshot order by f_id desc limit 1;,success
174bd4b36597adda000000000000000d,2024-01-05T15:22:55+08:00,cmd.Command,avenir_user01,223.104.41.119,miniglobal-devops,***********,select * from t_account_manage_fee_snapshot order by f_id desc limit 1;,success
174bd4b36597adda000000000000000d,2024-01-05T15:31:12+08:00,cmd.Command,avenir_user01,223.104.41.119,miniglobal-devops,***********,select * from t_account_manage_fee_snapshot order by f_id desc limit 1;,success
174bd4b36597adda000000000000000d,2024-01-05T15:31:33+08:00,cmd.Command,avenir_user01,223.104.41.119,miniglobal-devops,***********,select * from t_account_manage_fee_snapshot order by f_id desc limit 1;,success
174bd4b36597adda000000000000000d,2024-01-05T15:31:39+08:00,cmd.Command,avenir_user01,223.104.41.119,miniglobal-devops,***********,select * from t_account_manage_fee_snapshot order by f_id desc limit 1;,success
174bd4b36597adda000000000000000d,2024-01-05T15:31:42+08:00,cmd.Command,avenir_user01,223.104.41.119,miniglobal-devops,***********,select * from t_account_manage_fee_snapshot order by f_id desc limit 1;,success
174bd4b36597adda000000000000000d,2024-01-05T15:32:57+08:00,cmd.Command,avenir_user01,223.104.41.119,miniglobal-devops,***********,select * from t_account_manage_fee_snapshot order by f_id desc limit 1;,success
c14e9b3765996a2e0000000000000013,2024-01-06T22:57:33+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,select * from forensic_event where id=4746 \G,success
c14e9b3765996a2e0000000000000013,2024-01-06T22:58:19+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,AAA,success
c14e9b3765996a2e0000000000000013,2024-01-06T22:58:38+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                                       0$ mycli  1-$ ~  (2*$~)  3$ ..2322a9/result  4$ ~/forensic_etl  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                                                       ][06/01 14:58],success
c14e9b3765996a2e0000000000000013,2024-01-06T22:59:17+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                                       0$ mycli  1-$ ~  (2*$~)  3$ ..2322a9/result  4$ ~/forensic_etl  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                                                       ][06/01 14:59],success
c14e9b3765996a2e0000000000000013,2024-01-06T22:59:30+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                                       0$ mycli  1-$ ~  (2*$~)  3$ ..2322a9/result  4$ ~/forensic_etl  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                                                       ][06/01 14:59],success
61d30579659b56410000000000000019,2024-01-08T09:56:26+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
61d30579659b56410000000000000019,2024-01-08T09:56:49+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,use proamsdwdb;,success
61d30579659b56410000000000000019,2024-01-08T09:57:12+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
61d30579659b56410000000000000019,2024-01-08T09:57:12+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where f_state in(3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
61d30579659b56410000000000000019,2024-01-08T09:57:14+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,group by f_currency;,success
65174763659b5826000000000000000d,2024-01-08T10:04:28+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,cd /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-hbg-encn/,success
65174763659b5826000000000000000d,2024-01-08T10:04:28+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,ll,success
65174763659b5826000000000000000d,2024-01-08T10:04:38+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,make build TAG=PRD_T401041,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:14:20+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:14:44+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,use hbg_trade,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:14:47+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,show tables;,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:14:57+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,desc t_account_manage_fee_transfer_detail;,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:15:42+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from t_account_manage_fee_transfer_detail where f_id=*********;,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:16:28+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from t_account_manage_fee_transfer_detail order by f_id desc limit 2;,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:21:21+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from t_account_manage_fee_snapshot order by f_id desc limit 1;,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:21:47+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from t_account_manage_fee_transfer_detail where f_snapshot_id = *********;,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:26:02+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,min(f_id) from t_account_manage_fee_snapshot where f_day > '********';,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:26:59+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select min(f_id) from t_account_manage_fee_snapshot where f_day = '********';,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:29:00+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,>= *********;,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:29:40+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from t_account_manage_fee_transfer_detail where f_id = *********;,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:30:37+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select count(*) from t_account_manage_fee_transfer_detail where f_id < *********;,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:33:21+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from t_account_manage_fee_transfer_detail_tmp order by f_id desc limit 2;,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:33:41+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select count(*) from t_account_manage_fee_transfer_detail_tmp;,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:33:59+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,show create table t_account_manage_fee_transfer_detail;,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:38:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,  `f_currency_amount` decimal(36,18) unsigned NOT NULL DEFAULT '0.000000000000000000' COMMENT '对应的快照币种数量',,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:38:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,  `f_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:38:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,  `f_snapshot_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '快照id',,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:38:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,  `f_user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:38:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,  `f_transfer_user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '划转userid',,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:38:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,  `f_transfer_uid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '划转uid',,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:38:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,  `f_transfer_child` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '是否子账号 1是 0否',,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:38:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,  `f_fee_currency` varchar(20) NOT NULL DEFAULT '' COMMENT '收取管理费币种',,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:38:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,  `f_fee_currency_price` decimal(36,18) unsigned NOT NULL DEFAULT '0.000000000000000000' COMMENT '收取管理费币种 对 快照币种currency 价格',,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:38:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,  `f_fee_amount` decimal(36,18) unsigned NOT NULL DEFAULT '0.000000000000000000' COMMENT '收取管理费币种数量',,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:38:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,  `f_currency` varchar(20) NOT NULL DEFAULT '' COMMENT '对应快照币种',,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:38:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,CREATE TABLE `t_account_manage_fee_transfer_detail_2023` (,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:38:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,  `f_system_user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '系统账户用户ID',,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:38:05+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,  KEY `idx_snapshotId_main` (`f_snapshot_id`,`f_snapshot_main`),success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:38:05+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,  `f_state` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '划转状态  0 初始化状态 1划转成功 2划转失败',,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:38:05+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,  `f_retry` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '重试次数',,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:38:05+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,  `f_broker_result_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '请求broker 返回id',,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:38:05+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,  `f_broker_transfer_error` varchar(500) NOT NULL DEFAULT '' COMMENT '划转失败原因',,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:38:05+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,  `f_transfer_req_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '请求broker唯一标识',,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:38:05+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,  `f_updated_at` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:38:05+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,  `f_snapshot_main` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '是否同步快照数据 1是 0否',,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:38:05+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,  `f_type` tinyint(4) unsigned NOT NULL DEFAULT '1' COMMENT '类型 1收取 2退还',,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:38:05+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,  PRIMARY KEY (`f_id`),,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:38:05+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,  KEY `idx_createdAt` (`f_created_at`),,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:38:05+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,  `f_created_at` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:38:05+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,  KEY `idx_state_snapshotId` (`f_state`,`f_snapshot_id`),,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:38:38+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,) ENGINE=InnoDB AUTO_INCREMENT=********* DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='账户管理费划转详情表2023年备份数据';,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:38:42+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,show tables;,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:40:41+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,insert into t_account_manage_fee_transfer_detail_2023 select * from t_account_manage_fee_transfer_detail where f_id < *********;,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:49:55+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,quit,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:50:00+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:50:12+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,use hbg_trade,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:50:15+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,show tables;,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:50:27+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select count(1) from t_account_manage_fee_transfer_detail_2023;,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:57:51+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from t_account_manage_fee_transfer_detail_2023 order by f_id desc limit 2;,success
ad3e43ed659b5a35000000000000000d,2024-01-08T10:58:08+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,quit,success
61d30579659b56410000000000000019,2024-01-08T11:17:23+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,exit,success
61d30579659b56410000000000000019,2024-01-08T11:17:25+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,exit,success
5868cbe7659bbd3b0000000000000013,2024-01-08T17:15:46+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                  0$ mycli  1-$ ~  (2*$~)  3$ ..2322a9/result  4$ ~/forensic_etl  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                                  ][08/01  9:15],success
5868cbe7659bbd3b0000000000000013,2024-01-08T17:15:50+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                  0$ mycli  1-$ ~  (2*$~)  3$ ..2322a9/result  4$ ~/forensic_etl  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                                  ][08/01  9:16],success
5868cbe7659bbd3b0000000000000013,2024-01-08T17:16:03+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                  0$ mycli  1-$ ~  (2*$~)  3$ ..2322a9/result  4$ ~/forensic_etl  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                                  ][08/01  9:16],success
5868cbe7659bbd3b0000000000000013,2024-01-08T17:16:58+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,2,success
5868cbe7659bbd3b0000000000000013,2024-01-08T17:17:04+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$~/forensic_etl)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01  9:17],success
5868cbe7659bbd3b0000000000000013,2024-01-08T17:17:09+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$~/forensic_etl)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01  9:17],success
5868cbe7659bbd3b0000000000000013,2024-01-08T17:17:09+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$~/forensic_etl)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01  9:17],success
5868cbe7659bbd3b0000000000000013,2024-01-08T17:17:14+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$~/forensic_etl)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01  9:17],success
5868cbe7659bbd3b0000000000000013,2024-01-08T17:17:17+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,q,success
5868cbe7659bbd3b0000000000000013,2024-01-08T17:17:17+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$~/forensic_etl)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01  9:17],success
5868cbe7659bbd3b0000000000000013,2024-01-08T17:17:21+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$~/forensic_etl)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01  9:17],success
5868cbe7659bbd3b0000000000000013,2024-01-08T17:18:37+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,q,success
5868cbe7659bbd3b0000000000000013,2024-01-08T17:18:38+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,ls,success
5868cbe7659bbd3b0000000000000013,2024-01-08T17:18:44+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2-$ docker  3$ ..2322a9/result  (4*$~/forensic_etl)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01  9:18],success
5868cbe7659bbd3b0000000000000013,2024-01-08T17:18:50+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2-$ docker  3$ ..2322a9/result  (4*$vim)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                                     ][08/01  9:19],success
5868cbe7659bbd3b0000000000000013,2024-01-08T17:19:06+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2-$ docker  3$ ..2322a9/result  (4*$~/forensic_etl)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01  9:19],success
8d3805a8659bc06c000000000000000d,2024-01-08T17:29:22+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-01,************,ps aux|grep presto,success
8d3805a8659bc06c000000000000000d,2024-01-08T17:32:45+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-01,************,ip a,success
5868cbe7659bbd3b0000000000000013,2024-01-08T17:33:18+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2-$ docker  3$ ..2322a9/result  (4*$~/forensic_etl)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01  9:33],success
5868cbe7659bbd3b0000000000000013,2024-01-08T17:33:22+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2-$ docker  3$ ..2322a9/result  (4*$~/forensic_etl)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01  9:33],success
5868cbe7659bbd3b0000000000000013,2024-01-08T17:33:24+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2-$ docker  3$ ..2322a9/result  (4*$~/forensic_etl)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01  9:33],success
5868cbe7659bbd3b0000000000000013,2024-01-08T17:37:04+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$vim)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                                     ][08/01  9:37],success
5868cbe7659bbd3b0000000000000013,2024-01-08T17:37:05+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$~/forensic_etl)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01  9:37],success
5868cbe7659bbd3b0000000000000013,2024-01-08T17:37:11+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$~/forensic_etl)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01  9:37],success
5868cbe7659bbd3b0000000000000013,2024-01-08T17:37:12+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$..ensic_etl/gen)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                               ][08/01  9:37],success
5868cbe7659bbd3b0000000000000013,2024-01-08T17:37:15+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$..ensic_etl/gen)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                               ][08/01  9:37],success
5868cbe7659bbd3b0000000000000013,2024-01-08T17:37:16+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$..ensic_etl/gen)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                               ][08/01  9:37],success
5868cbe7659bbd3b0000000000000013,2024-01-08T17:39:12+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$vim)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                                     ][08/01  9:39],success
5868cbe7659bbd3b0000000000000013,2024-01-08T17:39:24+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$..ensic_etl/gen)  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                               ][08/01  9:39],success
f1d8e337659bc32c000000000000000d,2024-01-08T17:41:12+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ssh ************,success
f1d8e337659bc32c000000000000000d,2024-01-08T17:41:55+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,╰─[:)] # nc -vv presto.cogdocs.com 10008,success
f1d8e337659bc32c000000000000000d,2024-01-08T17:42:44+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,╰─[:(] # nc -vv 172.28.5.92 10008,success
f1d8e337659bc32c000000000000000d,2024-01-08T17:47:46+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,╰─[:(] # nc -vv presto.cogdocs.com 10008,success
f1d8e337659bc32c000000000000000d,2024-01-08T17:48:00+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,╰─[:(] # nc -vv presto.cogdocs.com 10008,success
f1d8e337659bc32c000000000000000d,2024-01-08T17:50:05+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,╰─[:(] # nc -vv presto.cogdocs.com 10008,success
f1d8e337659bc32c000000000000000d,2024-01-08T17:51:23+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,╰─[:(] # nc -vv presto.cogdocs.com 10008,success
f1d8e337659bc32c000000000000000d,2024-01-08T17:51:41+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,╰─[:(] # telnet presto.cogdocs.com 10008,success
f1d8e337659bc32c000000000000000d,2024-01-08T17:51:53+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,╰─[:(] # ping presto.cogdocs.com,success
f1d8e337659bc32c000000000000000d,2024-01-08T17:53:28+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,╰─[:)] # ping presto.cogdocs.com,success
f1d8e337659bc32c000000000000000d,2024-01-08T17:53:34+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,╰─[:)] # nc -vv presto.cogdocs.com 10008,success
5868cbe7659bbd3b0000000000000013,2024-01-08T17:54:48+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                 0$ mycli  1$ ~  (2*$~)  3$ ..2322a9/result  4-$ ..ensic_etl/gen  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                                  ][08/01  9:55],success
5868cbe7659bbd3b0000000000000013,2024-01-08T17:54:51+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                 0$ mycli  1$ ~  (2*$~)  3$ ..2322a9/result  4-$ ..ensic_etl/gen  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                                  ][08/01  9:55],success
5868cbe7659bbd3b0000000000000013,2024-01-08T17:55:11+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                 0$ mycli  1$ ~  (2*$~)  3$ ..2322a9/result  4-$ ..ensic_etl/gen  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                                  ][08/01  9:55],success
5868cbe7659bbd3b0000000000000013,2024-01-08T18:00:30+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                 0$ mycli  1$ ~  (2*$~)  3$ ..2322a9/result  4-$ ..ensic_etl/gen  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                                  ][08/01 10:00],success
5868cbe7659bbd3b0000000000000013,2024-01-08T18:00:44+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                 0$ mycli  1$ ~  (2*$~)  3$ ..2322a9/result  4-$ ..ensic_etl/gen  5$ ..peration_data  6$ ..forensic_task  7$ ~/forensic_etl                                  ][08/01 10:00],success
5868cbe7659bbd3b0000000000000013,2024-01-08T18:13:30+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2-$ docker  3$ ..2322a9/result  4$ ..ensic_etl/gen  (5*$..peration_data)  6$ ..forensic_task  7$ ~/forensic_etl                               ][08/01 10:13],success
5868cbe7659bbd3b0000000000000013,2024-01-08T18:13:42+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2-$ docker  3$ ..2322a9/result  4$ ..ensic_etl/gen  (5*$..peration_data)  6$ ..forensic_task  7$ ~/forensic_etl                               ][08/01 10:13],success
5868cbe7659bbd3b0000000000000013,2024-01-08T18:13:42+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2-$ docker  3$ ..2322a9/result  4$ ..ensic_etl/gen  (5*$..sic_etl/model)  6$ ..forensic_task  7$ ~/forensic_etl                               ][08/01 10:13],success
5868cbe7659bbd3b0000000000000013,2024-01-08T18:13:44+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2-$ docker  3$ ..2322a9/result  4$ ..ensic_etl/gen  (5*$..sic_etl/model)  6$ ..forensic_task  7$ ~/forensic_etl                               ][08/01 10:13],success
5868cbe7659bbd3b0000000000000013,2024-01-08T18:13:45+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                      0$ mycli  1$ ~  2-$ docker  3$ ..2322a9/result  4$ ..ensic_etl/gen  (5*$~)  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 10:13],success
5868cbe7659bbd3b0000000000000013,2024-01-08T18:13:47+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2-$ docker  3$ ..2322a9/result  4$ ..ensic_etl/gen  (5*$~/forensic_etl)  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01 10:13],success
5868cbe7659bbd3b0000000000000013,2024-01-08T18:13:47+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                      0$ mycli  1$ ~  2-$ docker  3$ ..2322a9/result  4$ ..ensic_etl/gen  (5*$~)  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 10:13],success
5868cbe7659bbd3b0000000000000013,2024-01-08T18:13:50+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2-$ docker  3$ ..2322a9/result  4$ ..ensic_etl/gen  (5*$~/forensic_etl)  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01 10:14],success
5868cbe7659bbd3b0000000000000013,2024-01-08T18:14:01+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,ggjjjjjjjjjjjjjjjGGkkkkkkkkji# jjjjhxxw,success
5868cbe7659bbd3b0000000000000013,2024-01-08T18:14:11+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2-$ docker  3$ ..2322a9/result  4$ ..ensic_etl/gen  (5*$vim)  6$ ..forensic_task  7$ ~/forensic_etl                                     ][08/01 10:14],success
5868cbe7659bbd3b0000000000000013,2024-01-08T18:14:27+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,gglkl#GGlllll#nnnnq,success
5868cbe7659bbd3b0000000000000013,2024-01-08T18:14:27+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,:q,success
5868cbe7659bbd3b0000000000000013,2024-01-08T18:14:30+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2-$ docker  3$ ..2322a9/result  4$ ..ensic_etl/gen  (5*$~/forensic_etl)  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01 10:14],success
5868cbe7659bbd3b0000000000000013,2024-01-08T18:15:07+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$..ensic_etl/gen)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01 10:15],success
5868cbe7659bbd3b0000000000000013,2024-01-08T18:15:07+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$~/forensic_etl)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01 10:15],success
5868cbe7659bbd3b0000000000000013,2024-01-08T18:15:09+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$~/forensic_etl)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01 10:15],success
5868cbe7659bbd3b0000000000000013,2024-01-08T18:46:05+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$vim)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 10:46],success
5868cbe7659bbd3b0000000000000013,2024-01-08T18:49:45+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$vim)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 10:49],success
5868cbe7659bbd3b0000000000000013,2024-01-08T18:49:49+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$~/forensic_etl)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01 10:50],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:33:01+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,gg24：q,success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:33:01+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                0$ mycli  1$ ~  2-$ docker  3$ ..2322a9/result  (4*$~/forensic_etl)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01 12:33],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:33:02+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                0$ mycli  1$ ~  2-$ docker  3$ ..2322a9/result  (4*$~/forensic_etl)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01 12:33],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:33:06+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                      0$ mycli  1$ ~  2-$ docker  3$ ..2322a9/result  (4*$~)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                       ][08/01 12:33],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:33:06+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                0$ mycli  1$ ~  2-$ docker  3$ ..2322a9/result  (4*$~/forensic_etl)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01 12:33],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:33:08+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                0$ mycli  1$ ~  2-$ docker  3$ ..2322a9/result  (4*$~/forensic_etl)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01 12:33],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:33:20+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2-$ docker  3$ ..2322a9/result  (4*$vim)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 12:33],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:33:23+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2-$ docker  3$ ..2322a9/result  (4*$vim)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 12:33],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:33:24+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,0$ mycli  1$ ~  2-$ docker  3$ ..2322a9/result  (4*$vim)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 12:33],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:33:25+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,0$ mycli  1$ ~  2-$ docker  3$ ..2322a9/result  (4*$vim)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 12:33],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:33:28+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  4-$ vim  (5*$~/forensic_etl)  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 12:33],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:38:13+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  4-$ vim  (5*$~/forensic_etl)  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 12:38],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:38:26+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  4-$ vim  (5*$~/forensic_etl)  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 12:38],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:38:34+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  4-$ vim  (5*$~/forensic_etl)  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 12:38],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:38:38+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  4-$ vim  (5*$~/forensic_etl)  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 12:38],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:38:43+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  4-$ vim  (5*$~/forensic_etl)  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 12:38],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:38:48+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  4-$ vim  (5*$~/forensic_etl)  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 12:39],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:38:49+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  4-$ vim  (5*$~/forensic_etl)  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 12:39],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:38:51+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  4-$ vim  (5*$~/forensic_etl)  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 12:39],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:38:56+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  4-$ vim  (5*$~/forensic_etl)  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 12:39],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:39:00+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  4-$ vim  (5*$~/forensic_etl)  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 12:39],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:39:01+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  4-$ vim  (5*$~/forensic_etl)  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 12:39],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:39:15+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  4-$ vim  (5*$~/forensic_etl)  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 12:39],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:39:20+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  4-$ vim  (5*$~/forensic_etl)  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 12:39],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:39:32+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  4-$ vim  (5*$~/forensic_etl)  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 12:39],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:39:38+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  4-$ vim  (5*$~/forensic_etl)  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 12:39],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:39:41+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,jjjjjjjq,success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:39:43+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  4-$ vim  (5*$~/forensic_etl)  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 12:39],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:39:45+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  4-$ vim  (5*$~/forensic_etl)  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 12:39],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:39:57+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  4-$ vim  (5*$~/forensic_etl)  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 12:40],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:40:08+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,0$ mycli  1$ ~  2$ ~  3$ ..2322a9/result  (4*$vim)  5-$ ./run.sh  6$ ..forensic_task  7$ ~/forensic_etl                                           ][08/01 12:40],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:40:08+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ ~  3$ ..2322a9/result  (4*$~/forensic_etl)  5-$ ./run.sh  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 12:40],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:40:09+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ ~  3$ ..2322a9/result  (4*$~/forensic_etl)  5-$ ./run.sh  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 12:40],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:40:10+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                            0$ mycli  1$ ~  2$ ~  3$ ..2322a9/result  (4*$~)  5-$ ./run.sh  6$ ..forensic_task  7$ ~/forensic_etl                                            ][08/01 12:40],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:40:16+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                            0$ mycli  1$ ~  2$ ~  3$ ..2322a9/result  (4*$~)  5-$ ./run.sh  6$ ..forensic_task  7$ ~/forensic_etl                                            ][08/01 12:40],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:40:18+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,q,success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:40:24+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                            0$ mycli  1$ ~  2$ ~  3$ ..2322a9/result  (4*$~)  5-$ ./run.sh  6$ ..forensic_task  7$ ~/forensic_etl                                            ][08/01 12:40],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:40:29+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                        0$ mycli  1$ ~  2$ ~  3$ ..2322a9/result  (4*$~/gain_be)  5-$ ./run.sh  6$ ..forensic_task  7$ ~/forensic_etl                                        ][08/01 12:40],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:40:33+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,0$ mycli  1$ ~  2$ ~  3$ ..2322a9/result  (4*$~/gain_be)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                     ][08/01 12:40],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:40:38+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,vim .git,success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:40:42+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,：q,success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:40:42+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ ~  3$ ..2322a9/result  (4*$~/gain_be)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                     ][08/01 12:40],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:40:43+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ ~  3$ ..2322a9/result  (4*$~/gain_be)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                     ][08/01 12:40],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:40:44+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2$ ~  3$ ..2322a9/result  (4*$~)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                         ][08/01 12:40],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:40:45+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2$ ~  3$ ..2322a9/result  (4*$~)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                         ][08/01 12:40],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:40:51+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2$ ~  3$ ..2322a9/result  (4*$~)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                         ][08/01 12:41],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:40:53+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,q,success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:40:55+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1$ ~  2$ ~  3$ ..2322a9/result  (4*$~)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                         ][08/01 12:41],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:41:22+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,ddwq,success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:41:27+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  (1*$~)  2$ ~  3$ ..2322a9/result  4$ ~  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                         ][08/01 12:41],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:41:34+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                         0$ mycli  1-$ ~  (2*$~)  3$ ..2322a9/result  4$ ~  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                         ][08/01 12:41],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:41:44+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                      0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  4$ ~  (5*$~/forensic_etl)  6-$ ..forensic_task  7$ ~/forensic_etl                                       ][08/01 12:41],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:42:04+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                      0$ mycli  1$ ~  2-$ docker  (3*$..2322a9/result)  4$ ~  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                       ][08/01 12:42],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:42:08+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                      0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$~)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                       ][08/01 12:42],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:42:11+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                      0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$~)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                       ][08/01 12:42],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:42:11+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,ls,success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:42:12+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$vim)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 12:42],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:42:13+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                      0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$~)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                       ][08/01 12:42],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:42:14+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$~/forensic_etl)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01 12:42],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:42:19+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$~/forensic_etl)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01 12:42],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:42:20+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$..ensic_etl/gen)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01 12:42],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:42:22+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$..ensic_etl/gen)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01 12:42],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:42:53+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,set nonu,success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:43:05+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,q,success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:43:08+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$..ensic_etl/gen)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01 12:43],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:43:10+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$~/forensic_etl)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01 12:43],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:43:11+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,:q,success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:43:13+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$~/forensic_etl)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                ][08/01 12:43],success
5868cbe7659bbd3b0000000000000013,2024-01-08T20:43:17+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ docker  3-$ ..2322a9/result  (4*$vim)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                      ][08/01 12:43],success
a3b0f612659cd211000000000000000d,2024-01-09T12:57:09+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,cd /hbdata/soft/miniglobal-apps/apps/miniglobal/miniglobal-finance-daily-report/,success
a3b0f612659cd211000000000000000d,2024-01-09T12:57:10+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,ll,success
a3b0f612659cd211000000000000000d,2024-01-09T12:57:23+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,less Makefile,success
a3b0f612659cd211000000000000000d,2024-01-09T12:57:39+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,make build TAG=PRD_T20240109,success
a3b0f612659cd211000000000000000d,2024-01-09T13:14:43+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,make build TAG=PRD_T20240109_1,success
7ee0a3cb659cff610000000000000019,2024-01-09T16:10:14+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
7ee0a3cb659cff610000000000000019,2024-01-09T16:10:27+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,use proamsdwdb;,success
7ee0a3cb659cff610000000000000019,2024-01-09T16:11:32+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_state, count(*) as user_count,success
7ee0a3cb659cff610000000000000019,2024-01-09T16:11:32+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from t_account_manage_fee_snapshot  ,success
7ee0a3cb659cff610000000000000019,2024-01-09T16:11:32+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where f_day in('********', '********'),success
7ee0a3cb659cff610000000000000019,2024-01-09T16:11:32+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,group by f_state;,success
7ee0a3cb659cff610000000000000019,2024-01-09T16:11:42+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,use hbg_trade; ,success
7ee0a3cb659cff610000000000000019,2024-01-09T16:11:43+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_state, count(*) as user_count from t_account_manage_fee_snapshot   where f_day in('********', '********') group by f_state;,success
7ee0a3cb659cff610000000000000019,2024-01-09T16:18:30+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_currency,sum(f_amount) as fee_amount,count(distinct f_user_id) as user_count,success
7ee0a3cb659cff610000000000000019,2024-01-09T16:18:30+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from t_account_manage_fee_snapshot,success
7ee0a3cb659cff610000000000000019,2024-01-09T16:18:30+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where f_day in('********') and f_currency ,success
7ee0a3cb659cff610000000000000019,2024-01-09T16:18:30+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,in('btc', 'eth','usdt','ht','shib','doge','xrp','eos','ltc','trx'),success
7ee0a3cb659cff610000000000000019,2024-01-09T16:18:30+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,group by f_currency order by f_currency;,success
7ee0a3cb659cff610000000000000019,2024-01-09T16:21:29+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,4,success
7ee0a3cb659cff610000000000000019,2024-01-09T16:25:36+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_currency,sum(f_amount) as fee_amount,count(distinct f_user_id) as user_count from t_account_manage_fee_snapshot where f_day in('********') and f_currency  in('btc', 'eth','usdt','ht','shib','doge','xrp','eos','ltc','trx') group by f_currency order by f_currency;,success
7ee0a3cb659cff610000000000000019,2024-01-09T16:27:55+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where f_day in('********', '********', '********')  and f_state = 1 ,success
7ee0a3cb659cff610000000000000019,2024-01-09T16:27:55+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from t_account_manage_fee_snapshot,success
7ee0a3cb659cff610000000000000019,2024-01-09T16:27:55+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_currency,sum(f_amount),success
7ee0a3cb659cff610000000000000019,2024-01-09T16:27:56+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,and f_currency in ('btc', 'eth','usdt','ht','shib','doge','xrp','eos','ltc','trx'),success
7ee0a3cb659cff610000000000000019,2024-01-09T16:27:56+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,group by f_currency order by f_currency;,success
7ee0a3cb659cff610000000000000019,2024-01-09T16:41:37+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,'',********,success
7ee0a3cb659cff610000000000000019,2024-01-09T16:50:01+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,exit,success
7ee0a3cb659cff610000000000000019,2024-01-09T16:50:02+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,exit,success
fcc18bd1659d3311000000000000000d,2024-01-09T19:50:49+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ssh 172.28.3.8,success
fcc18bd1659d3311000000000000000d,2024-01-09T19:50:58+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ps aux|grep presto,success
fcc18bd1659d3311000000000000000d,2024-01-09T19:51:02+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ps aux|grep java,success
fcc18bd1659d3311000000000000000d,2024-01-09T19:51:16+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /hbdata/,success
fcc18bd1659d3311000000000000000d,2024-01-09T19:51:16+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
fcc18bd1659d3311000000000000000d,2024-01-09T19:51:22+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd presto/data/,success
fcc18bd1659d3311000000000000000d,2024-01-09T19:51:23+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
fcc18bd1659d3311000000000000000d,2024-01-09T19:51:33+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd etc,success
fcc18bd1659d3311000000000000000d,2024-01-09T19:51:34+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
fcc18bd1659d3311000000000000000d,2024-01-09T19:51:55+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,vim config.properties,success
fcc18bd1659d3311000000000000000d,2024-01-09T19:54:09+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,:q,success
c11146e5659d33f3000000000000000d,2024-01-09T19:54:53+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-01,************,ps aux|grep java,success
2133c045659d342b000000000000000d,2024-01-09T19:55:30+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd /hbdata/presto/data/,success
2133c045659d342b000000000000000d,2024-01-09T19:55:31+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
2133c045659d342b000000000000000d,2024-01-09T19:55:37+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd etc/catalog/,success
2133c045659d342b000000000000000d,2024-01-09T19:55:38+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
2133c045659d342b000000000000000d,2024-01-09T19:55:51+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,less hive.properties,success
4918dfd8659d34c7000000000000000d,2024-01-09T19:58:26+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-01,************,cd /hbdata/presto,success
4918dfd8659d34c7000000000000000d,2024-01-09T19:58:27+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-01,************,ll,success
4918dfd8659d34c7000000000000000d,2024-01-09T19:59:07+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-01,************,cd ../presto350/data/var/,success
4918dfd8659d34c7000000000000000d,2024-01-09T19:59:08+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-01,************,ll,success
cc3fa977659d3548000000000000000d,2024-01-09T20:00:12+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-03,172.28.3.121,cd /hbdata/presto/,success
cc3fa977659d3548000000000000000d,2024-01-09T20:00:12+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-03,172.28.3.121,ll,success
cc3fa977659d3548000000000000000d,2024-01-09T20:00:15+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-03,172.28.3.121,cd data/,success
cc3fa977659d3548000000000000000d,2024-01-09T20:00:16+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-03,172.28.3.121,ll,success
cc3fa977659d3548000000000000000d,2024-01-09T20:00:21+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-03,172.28.3.121,cd etc/,success
cc3fa977659d3548000000000000000d,2024-01-09T20:00:23+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-03,172.28.3.121,ll,success
cc3fa977659d3548000000000000000d,2024-01-09T20:00:32+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-03,172.28.3.121,vim config.properties,success
cc3fa977659d3548000000000000000d,2024-01-09T20:01:03+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-03,172.28.3.121,:q,success
cc3fa977659d3548000000000000000d,2024-01-09T20:01:09+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-03,172.28.3.121,vim node.properties,success
cc3fa977659d3548000000000000000d,2024-01-09T20:01:17+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-03,172.28.3.121,:q,success
4ee76774659d35eb000000000000000d,2024-01-09T20:03:02+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd /hbdata/presto/data/etc/catalog/,success
4ee76774659d35eb000000000000000d,2024-01-09T20:03:02+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
4ee76774659d35eb000000000000000d,2024-01-09T20:03:12+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd /hbdata/presto/,success
4ee76774659d35eb000000000000000d,2024-01-09T20:03:13+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
4ee76774659d35eb000000000000000d,2024-01-09T20:03:15+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd data/,success
4ee76774659d35eb000000000000000d,2024-01-09T20:03:15+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
4ee76774659d35eb000000000000000d,2024-01-09T20:03:18+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd etc/,success
4ee76774659d35eb000000000000000d,2024-01-09T20:03:19+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
4ee76774659d35eb000000000000000d,2024-01-09T20:03:26+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,less node.properties,success
4ee76774659d35eb000000000000000d,2024-01-09T20:03:31+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,vim config.properties,success
5218e0ff659d30ba0000000000000013,2024-01-09T20:03:55+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,2[,success
4ee76774659d35eb000000000000000d,2024-01-09T20:04:00+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,:wq,success
5218e0ff659d30ba0000000000000013,2024-01-09T20:04:16+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,5ls,success
5218e0ff659d30ba0000000000000013,2024-01-09T20:04:26+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,"q:q,success
5218e0ff659d30ba0000000000000013,2024-01-09T20:04:27+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,ls,success
5218e0ff659d30ba0000000000000013,2024-01-09T20:04:33+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  (4*$~/forensic_etl)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                ][09/01 12:04],success
5218e0ff659d30ba0000000000000013,2024-01-09T20:04:34+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  (4*$..ensic_etl/gen)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                ][09/01 12:04],success
5218e0ff659d30ba0000000000000013,2024-01-09T20:04:36+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  (4*$..ensic_etl/gen)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                ][09/01 12:04],success
5218e0ff659d30ba0000000000000013,2024-01-09T20:04:39+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  (4*$vim)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                      ][09/01 12:04],success
5218e0ff659d30ba0000000000000013,2024-01-09T20:04:40+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  (4*$vim)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                      ][09/01 12:04],success
5218e0ff659d30ba0000000000000013,2024-01-09T20:04:41+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  (4*$vim)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                      ][09/01 12:04],success
5218e0ff659d30ba0000000000000013,2024-01-09T20:04:42+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  (4*$..ensic_etl/gen)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                ][09/01 12:04],success
5218e0ff659d30ba0000000000000013,2024-01-09T20:04:44+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  (4*$..ensic_etl/gen)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                ][09/01 12:04],success
5218e0ff659d30ba0000000000000013,2024-01-09T20:04:46+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                               0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  (4*$..ensic_etl/gen)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                ][09/01 12:04],success
5218e0ff659d30ba0000000000000013,2024-01-09T20:04:47+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  (4*$~/forensic_etl)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                ][09/01 12:04],success
5218e0ff659d30ba0000000000000013,2024-01-09T20:04:52+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  (4*$~/forensic_etl)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                ][09/01 12:05],success
5218e0ff659d30ba0000000000000013,2024-01-09T20:05:10+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,GGkki# jjjjkkkkkkjjjjjjosync()hh#w,success
5218e0ff659d30ba0000000000000013,2024-01-09T20:05:14+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                     0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  4-$ vim  (5*$~/forensic_etl)  6$ ..forensic_task  7$ ~/forensic_etl                                      ][09/01 12:05],success
5218e0ff659d30ba0000000000000013,2024-01-09T20:07:29+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,2,success
4ee76774659d35eb000000000000000d,2024-01-09T20:07:29+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ps aux|grep java,success
5218e0ff659d30ba0000000000000013,2024-01-09T20:08:02+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,= 7,success
5218e0ff659d30ba0000000000000013,2024-01-09T20:08:06+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************, \G,success
5218e0ff659d30ba0000000000000013,2024-01-09T20:08:22+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,q24q,success
4ee76774659d35eb000000000000000d,2024-01-09T20:09:06+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,,success
4ee76774659d35eb000000000000000d,2024-01-09T20:09:11+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ps aux|grep presto,success
5218e0ff659d30ba0000000000000013,2024-01-09T20:10:27+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,2,success
5218e0ff659d30ba0000000000000013,2024-01-09T20:10:41+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,docker restart forensic_etl,success
5218e0ff659d30ba0000000000000013,2024-01-09T20:10:45+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,4git statu,success
5218e0ff659d30ba0000000000000013,2024-01-09T20:10:48+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                0$ mycli  1$ ~  2-$ docker  3$ ..2322a9/result  (4*$~/forensic_etl)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                ][09/01 12:11],success
4ee76774659d35eb000000000000000d,2024-01-09T20:10:51+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,systemctl restart presto-server,success
5218e0ff659d30ba0000000000000013,2024-01-09T20:10:53+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,0$ mycli  1$ ~  2-$ ~  3$ ..2322a9/result  (4*$git)  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                        ][09/01 12:11],success
4ee76774659d35eb000000000000000d,2024-01-09T20:11:12+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,systemctl status presto,success
5218e0ff659d30ba0000000000000013,2024-01-09T20:11:35+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                  0$ mycli  1$ ~  (2*$~)  3$ ..2322a9/result  4-$ ~/forensic_etl  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                   ][09/01 12:11],success
5218e0ff659d30ba0000000000000013,2024-01-09T20:11:55+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  4$ ~/forensic_etl  (5*$~/forensic_etl)  6-$ ..forensic_task  7$ ~/forensic_etl                                ][09/01 12:12],success
5218e0ff659d30ba0000000000000013,2024-01-09T20:11:58+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  (4*$~/forensic_etl)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                ][09/01 12:12],success
5218e0ff659d30ba0000000000000013,2024-01-09T20:12:01+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  (4*$~/forensic_etl)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                ][09/01 12:12],success
5218e0ff659d30ba0000000000000013,2024-01-09T20:12:01+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                      0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  (4*$~)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                       ][09/01 12:12],success
4ee76774659d35eb000000000000000d,2024-01-09T20:12:05+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd ../,success
5218e0ff659d30ba0000000000000013,2024-01-09T20:12:06+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                      0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  (4*$~)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                       ][09/01 12:12],success
5218e0ff659d30ba0000000000000013,2024-01-09T20:12:08+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,q,success
4ee76774659d35eb000000000000000d,2024-01-09T20:12:08+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
4ee76774659d35eb000000000000000d,2024-01-09T20:12:08+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd ..,success
4ee76774659d35eb000000000000000d,2024-01-09T20:12:13+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd data/,success
4ee76774659d35eb000000000000000d,2024-01-09T20:12:14+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
4ee76774659d35eb000000000000000d,2024-01-09T20:12:20+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd var/,success
4ee76774659d35eb000000000000000d,2024-01-09T20:12:21+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
5218e0ff659d30ba0000000000000013,2024-01-09T20:12:42+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                      0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  (4*$~)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                       ][09/01 12:12],success
5218e0ff659d30ba0000000000000013,2024-01-09T20:12:43+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                      0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  (4*$~)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                       ][09/01 12:12],success
5218e0ff659d30ba0000000000000013,2024-01-09T20:12:44+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  (4*$~/forensic_etl)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                ][09/01 12:12],success
5218e0ff659d30ba0000000000000013,2024-01-09T20:12:46+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  (4*$~/forensic_etl)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                ][09/01 12:12],success
5218e0ff659d30ba0000000000000013,2024-01-09T20:12:48+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  (4*$git)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                      ][09/01 12:13],success
5218e0ff659d30ba0000000000000013,2024-01-09T20:12:49+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  (4*$git)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                      ][09/01 12:13],success
5218e0ff659d30ba0000000000000013,2024-01-09T20:12:54+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][                                0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  (4*$~/forensic_etl)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                ][09/01 12:13],success
5218e0ff659d30ba0000000000000013,2024-01-09T20:12:57+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  (4*$git)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                      ][09/01 12:13],success
5218e0ff659d30ba0000000000000013,2024-01-09T20:13:06+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,0$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  (4*$git)  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl                                      ][09/01 12:13],success
4ee76774659d35eb000000000000000d,2024-01-09T20:17:52+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd /hbdata/,success
4ee76774659d35eb000000000000000d,2024-01-09T20:17:53+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
4ee76774659d35eb000000000000000d,2024-01-09T20:18:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd presto/,success
4ee76774659d35eb000000000000000d,2024-01-09T20:18:05+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
4ee76774659d35eb000000000000000d,2024-01-09T20:18:07+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd data/,success
4ee76774659d35eb000000000000000d,2024-01-09T20:18:08+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
4ee76774659d35eb000000000000000d,2024-01-09T20:18:25+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll var/run/,success
4ee76774659d35eb000000000000000d,2024-01-09T20:18:37+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,less var/run/launcher.pid,success
4ee76774659d35eb000000000000000d,2024-01-09T20:18:44+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
4ee76774659d35eb000000000000000d,2024-01-09T20:18:47+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll etc/,success
4ee76774659d35eb000000000000000d,2024-01-09T20:18:55+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll plugin/,success
4ee76774659d35eb000000000000000d,2024-01-09T20:19:37+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd /etc/opt/,success
4ee76774659d35eb000000000000000d,2024-01-09T20:19:38+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
4ee76774659d35eb000000000000000d,2024-01-09T20:19:46+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd /opt/,success
4ee76774659d35eb000000000000000d,2024-01-09T20:19:47+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ls,success
4ee76774659d35eb000000000000000d,2024-01-09T20:19:51+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd presto/,success
4ee76774659d35eb000000000000000d,2024-01-09T20:19:52+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ls,success
4ee76774659d35eb000000000000000d,2024-01-09T20:19:55+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd bin/,success
4ee76774659d35eb000000000000000d,2024-01-09T20:19:56+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ls,success
4ee76774659d35eb000000000000000d,2024-01-09T20:20:15+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
4ee76774659d35eb000000000000000d,2024-01-09T20:20:41+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,less launcher.properties,success
4ee76774659d35eb000000000000000d,2024-01-09T20:21:22+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,./lau ps aux|grep launcher,success
4ee76774659d35eb000000000000000d,2024-01-09T20:21:33+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll procname/Linux-,success
4ee76774659d35eb000000000000000d,2024-01-09T20:21:41+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd ..,success
4ee76774659d35eb000000000000000d,2024-01-09T20:21:43+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
4ee76774659d35eb000000000000000d,2024-01-09T20:21:52+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd etc/catalog/,success
4ee76774659d35eb000000000000000d,2024-01-09T20:21:52+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
4ee76774659d35eb000000000000000d,2024-01-09T20:21:56+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd -,success
4ee76774659d35eb000000000000000d,2024-01-09T20:21:58+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
4ee76774659d35eb000000000000000d,2024-01-09T20:22:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,less README.txt,success
5499be75659d392c0000000000000013,2024-01-09T20:23:38+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,[,success
5499be75659d392c0000000000000013,2024-01-09T20:25:40+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][  0$ mycli  1$ ~  2-$ docker  3$ ..2322a9/result  4$ ~/forensic_etl  (5*$~/forensic_etl)  6$ ..forensic_task  7$ ~/forensic_etl  ][09/01 12:25,success
4ee76774659d35eb000000000000000d,2024-01-09T20:26:12+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd bin/,success
4ee76774659d35eb000000000000000d,2024-01-09T20:26:13+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
4ee76774659d35eb000000000000000d,2024-01-09T20:26:41+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,./launcher --help,success
4ee76774659d35eb000000000000000d,2024-01-09T20:26:58+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,./launcher restart,success
4ee76774659d35eb000000000000000d,2024-01-09T20:27:11+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ps aux|grep 10403,success
4ee76774659d35eb000000000000000d,2024-01-09T20:27:16+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ps aux,success
4ee76774659d35eb000000000000000d,2024-01-09T21:06:48+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd /opt/presto/etc/,success
4ee76774659d35eb000000000000000d,2024-01-09T21:06:49+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
4ee76774659d35eb000000000000000d,2024-01-09T21:06:55+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,less config.properties,success
5499be75659d392c0000000000000013,2024-01-09T22:47:20+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,2,success
38718c6f659df8c10000000000000019,2024-01-10T09:54:15+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
38718c6f659df8c10000000000000019,2024-01-10T09:54:52+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,use proamsdwdb;,success
38718c6f659df8c10000000000000019,2024-01-10T09:55:00+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
38718c6f659df8c10000000000000019,2024-01-10T09:55:00+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where f_state in(3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
38718c6f659df8c10000000000000019,2024-01-10T09:55:00+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,group by f_currency;,success
22449575659e031a000000000000000d,2024-01-10T10:38:32+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
38718c6f659df8c10000000000000019,2024-01-10T10:41:52+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,use huobi;,success
38718c6f659df8c10000000000000019,2024-01-10T10:41:56+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,show tables;,success
38718c6f659df8c10000000000000019,2024-01-10T10:42:23+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,desc wallet_balance;,success
22449575659e031a000000000000000d,2024-01-10T10:49:03+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ssh 172.28.3.8,success
22449575659e031a000000000000000d,2024-01-10T10:49:12+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /opt/presto/etc/,success
22449575659e031a000000000000000d,2024-01-10T10:49:12+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T10:49:18+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,vim config.properties,success
22449575659e031a000000000000000d,2024-01-10T10:50:20+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,:q,success
22449575659e031a000000000000000d,2024-01-10T10:50:38+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ssh 172.28.3.121,success
22449575659e031a000000000000000d,2024-01-10T10:50:47+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /opt/presto/etc/,success
22449575659e031a000000000000000d,2024-01-10T10:50:47+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T10:50:52+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,less config.properties,success
22449575659e031a000000000000000d,2024-01-10T10:51:18+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ssh 172.28.3.15,success
22449575659e031a000000000000000d,2024-01-10T10:52:00+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ps aux|grep java,success
22449575659e031a000000000000000d,2024-01-10T10:52:40+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /hbdata/,success
22449575659e031a000000000000000d,2024-01-10T10:52:40+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T10:55:55+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ssh 172.28.3.8,success
22449575659e031a000000000000000d,2024-01-10T10:56:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /opt/presto/etc/,success
22449575659e031a000000000000000d,2024-01-10T10:56:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T10:56:09+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cat jvm.config,success
22449575659e031a000000000000000d,2024-01-10T10:57:02+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,less log.properties,success
22449575659e031a000000000000000d,2024-01-10T10:57:16+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,less node.properties,success
22449575659e031a000000000000000d,2024-01-10T10:57:37+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll catalog/,success
22449575659e031a000000000000000d,2024-01-10T10:58:44+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cat config.properties,success
22449575659e031a000000000000000d,2024-01-10T11:00:18+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /hbdata/,success
22449575659e031a000000000000000d,2024-01-10T11:00:19+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T11:00:22+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd presto/,success
22449575659e031a000000000000000d,2024-01-10T11:00:23+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T11:00:26+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd data/,success
22449575659e031a000000000000000d,2024-01-10T11:00:26+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T11:00:46+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd etc/,success
22449575659e031a000000000000000d,2024-01-10T11:00:48+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T11:00:52+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cat config.properties,success
22449575659e031a000000000000000d,2024-01-10T11:01:44+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ssh ************,success
22449575659e031a000000000000000d,2024-01-10T11:01:47+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /hbdata/,success
22449575659e031a000000000000000d,2024-01-10T11:01:48+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T11:01:58+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd presto350/,success
22449575659e031a000000000000000d,2024-01-10T11:01:59+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T11:02:13+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd ..,success
22449575659e031a000000000000000d,2024-01-10T11:03:08+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ps aux|grep java,success
22449575659e031a000000000000000d,2024-01-10T11:03:45+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /opt/,success
22449575659e031a000000000000000d,2024-01-10T11:03:46+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ls,success
22449575659e031a000000000000000d,2024-01-10T11:04:21+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /hbdata/,success
22449575659e031a000000000000000d,2024-01-10T11:04:22+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T11:04:30+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd presto350/data/var/,success
22449575659e031a000000000000000d,2024-01-10T11:04:31+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T11:04:35+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll run/,success
22449575659e031a000000000000000d,2024-01-10T11:04:53+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll log/,success
22449575659e031a000000000000000d,2024-01-10T11:05:02+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,less log/launcher.log,success
22449575659e031a000000000000000d,2024-01-10T11:05:42+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /hbdata/presto283/data/var/,success
22449575659e031a000000000000000d,2024-01-10T11:05:43+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T11:05:59+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,less log/launcher.log,success
22449575659e031a000000000000000d,2024-01-10T11:06:18+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd log/,success
22449575659e031a000000000000000d,2024-01-10T11:06:18+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T11:06:21+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,less server.log,success
22449575659e031a000000000000000d,2024-01-10T11:06:45+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,less http-request.log,success
22449575659e031a000000000000000d,2024-01-10T11:07:11+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /hbdata/presto/data/var/,success
22449575659e031a000000000000000d,2024-01-10T11:07:11+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T11:07:14+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd log/,success
22449575659e031a000000000000000d,2024-01-10T11:07:15+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T11:07:28+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,less launcher.log,success
22449575659e031a000000000000000d,2024-01-10T11:07:47+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,less server.log,success
22449575659e031a000000000000000d,2024-01-10T11:08:55+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ssh 172.28.3.121,success
22449575659e031a000000000000000d,2024-01-10T11:09:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /opt/presto/etc/,success
22449575659e031a000000000000000d,2024-01-10T11:09:05+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T11:09:19+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,vim config.properties,success
22449575659e031a000000000000000d,2024-01-10T11:10:01+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,:wq,success
22449575659e031a000000000000000d,2024-01-10T11:10:22+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd ../bin/,success
22449575659e031a000000000000000d,2024-01-10T11:10:23+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T11:10:32+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,./launcher restart,success
22449575659e031a000000000000000d,2024-01-10T11:10:44+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ps aux,success
22449575659e031a000000000000000d,2024-01-10T11:12:29+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,./launcher --help,success
22449575659e031a000000000000000d,2024-01-10T11:12:38+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,./launcher status,success
22449575659e031a000000000000000d,2024-01-10T11:12:46+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,./launcher status,success
22449575659e031a000000000000000d,2024-01-10T11:12:54+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,./launcher start,success
22449575659e031a000000000000000d,2024-01-10T11:12:59+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,./launcher status,success
22449575659e031a000000000000000d,2024-01-10T11:13:16+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ps aux|grep 9505,success
22449575659e031a000000000000000d,2024-01-10T11:27:23+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,docker ps,success
22449575659e031a000000000000000d,2024-01-10T11:27:44+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ssh 172.28.3.8,success
22449575659e031a000000000000000d,2024-01-10T11:27:56+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /opt/presto/var/run/,success
22449575659e031a000000000000000d,2024-01-10T11:27:58+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T11:28:03+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cat launcher.pid,success
22449575659e031a000000000000000d,2024-01-10T11:28:06+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd ..,success
22449575659e031a000000000000000d,2024-01-10T11:28:12+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /hbdata/presto/data/,success
22449575659e031a000000000000000d,2024-01-10T11:28:12+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T11:28:17+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd var/,success
22449575659e031a000000000000000d,2024-01-10T11:28:18+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T11:28:22+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll log/,success
22449575659e031a000000000000000d,2024-01-10T11:28:30+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll run/,success
22449575659e031a000000000000000d,2024-01-10T11:28:36+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cat run/launcher.pid,success
22449575659e031a000000000000000d,2024-01-10T11:28:43+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ps aux|grep 10403,success
22449575659e031a000000000000000d,2024-01-10T11:31:15+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd log/,success
22449575659e031a000000000000000d,2024-01-10T11:31:16+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T11:31:20+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,less launcher.log,success
22449575659e031a000000000000000d,2024-01-10T11:32:02+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,less server.log,success
22449575659e031a000000000000000d,2024-01-10T11:33:17+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,Gkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkqcd ..,success
22449575659e031a000000000000000d,2024-01-10T11:33:21+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd ../etc,success
22449575659e031a000000000000000d,2024-01-10T11:33:22+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T11:33:26+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,vim config.properties,success
22449575659e031a000000000000000d,2024-01-10T11:34:03+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,:wq,success
22449575659e031a000000000000000d,2024-01-10T11:34:09+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd ..,success
22449575659e031a000000000000000d,2024-01-10T11:34:09+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T11:34:25+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,/opt/presto/bin/launcher restart,success
22449575659e031a000000000000000d,2024-01-10T11:34:37+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ps aux|grep 25938,success
22449575659e031a000000000000000d,2024-01-10T11:34:52+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ssh 172.28.3.121,success
22449575659e031a000000000000000d,2024-01-10T11:35:01+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /opt/presto/etc/,success
22449575659e031a000000000000000d,2024-01-10T11:35:02+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T11:35:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,vim config.properties,success
22449575659e031a000000000000000d,2024-01-10T11:35:11+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,:wq,success
22449575659e031a000000000000000d,2024-01-10T11:35:14+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd ..,success
22449575659e031a000000000000000d,2024-01-10T11:35:15+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T11:35:29+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,./bin/launcher restart,success
22449575659e031a000000000000000d,2024-01-10T11:35:37+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ps aux|grep 14073,success
22449575659e031a000000000000000d,2024-01-10T11:35:49+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ssh 172.28.3.8,success
22449575659e031a000000000000000d,2024-01-10T11:36:08+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /hbdata/presto/data/var/log/,success
22449575659e031a000000000000000d,2024-01-10T11:36:09+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T11:36:12+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,less server.log,success
22449575659e031a000000000000000d,2024-01-10T11:38:30+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,tail -f *.log,success
22449575659e031a000000000000000d,2024-01-10T11:39:54+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /hbdata/presto/data/etc/,success
22449575659e031a000000000000000d,2024-01-10T11:39:56+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,vim config.properties,success
22449575659e031a000000000000000d,2024-01-10T11:40:21+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,:wq,success
22449575659e031a000000000000000d,2024-01-10T11:40:28+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd ..,success
22449575659e031a000000000000000d,2024-01-10T11:40:28+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T11:40:42+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,/opt/presto/bin/launcher restart,success
22449575659e031a000000000000000d,2024-01-10T11:40:57+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ps aux|grep 27603,success
22449575659e031a000000000000000d,2024-01-10T11:41:13+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd -,success
22449575659e031a000000000000000d,2024-01-10T11:41:38+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /hbdata/presto/data/var/log/,success
22449575659e031a000000000000000d,2024-01-10T11:41:39+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T11:41:44+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,tail -f server.log,success
22449575659e031a000000000000000d,2024-01-10T11:41:56+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,df -h,success
22449575659e031a000000000000000d,2024-01-10T11:42:15+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd ../..,success
22449575659e031a000000000000000d,2024-01-10T11:42:17+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T11:42:20+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd etc/,success
22449575659e031a000000000000000d,2024-01-10T11:42:22+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,vim config.properties,success
22449575659e031a000000000000000d,2024-01-10T11:43:07+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,:wq,success
22449575659e031a000000000000000d,2024-01-10T11:43:19+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,/opt/presto/bin/launcher restart,success
22449575659e031a000000000000000d,2024-01-10T11:47:32+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
22449575659e031a000000000000000d,2024-01-10T11:47:34+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,vim config.properties,success
22449575659e031a000000000000000d,2024-01-10T11:47:41+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,:q,success
22449575659e031a000000000000000d,2024-01-10T11:48:06+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cp config.properties config.properties.bak,success
22449575659e031a000000000000000d,2024-01-10T11:48:10+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,vim config.properties,success
22449575659e031a000000000000000d,2024-01-10T11:49:28+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,:q,success
22449575659e031a000000000000000d,2024-01-10T11:50:40+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,df -h,success
22449575659e031a000000000000000d,2024-01-10T11:51:01+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ssh 172.28.3.121,success
22449575659e031a000000000000000d,2024-01-10T11:51:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,df -h,success
22449575659e031a000000000000000d,2024-01-10T11:51:17+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ssh 172.28.3.8,success
22449575659e031a000000000000000d,2024-01-10T11:51:32+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cat /hbdata/presto/data/etc/config.properties,success
22449575659e031a000000000000000d,2024-01-10T11:52:51+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,/opt/presto/bin/launcher status,success
22449575659e031a000000000000000d,2024-01-10T11:52:59+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ps aux|grep 28532,success
f5ad4406659e14f0000000000000000d,2024-01-10T11:54:32+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-01,************,ps aux|grep presto,success
f5ad4406659e14f0000000000000000d,2024-01-10T11:54:40+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-01,************,cd /opt/,success
f5ad4406659e14f0000000000000000d,2024-01-10T11:54:42+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-01,************,ll,success
f5ad4406659e14f0000000000000000d,2024-01-10T11:54:52+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-01,************,cd trino/,success
f5ad4406659e14f0000000000000000d,2024-01-10T11:54:57+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-01,************,ll,success
f5ad4406659e14f0000000000000000d,2024-01-10T11:55:00+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-01,************,less README.txt,success
f5ad4406659e14f0000000000000000d,2024-01-10T11:55:41+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-01,************,cd bin/,success
f5ad4406659e14f0000000000000000d,2024-01-10T11:55:42+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-01,************,ll,success
f5ad4406659e14f0000000000000000d,2024-01-10T11:55:51+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-01,************,./launcher status,success
f5ad4406659e14f0000000000000000d,2024-01-10T11:56:02+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-01,************,ps aux|grep 2741,success
f5ad4406659e14f0000000000000000d,2024-01-10T11:56:17+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-01,************,cd ../etc/,success
f5ad4406659e14f0000000000000000d,2024-01-10T11:56:18+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-01,************,ll,success
f5ad4406659e14f0000000000000000d,2024-01-10T11:56:29+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-01,************,vim config.properties,success
f5ad4406659e14f0000000000000000d,2024-01-10T11:56:47+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-01,************,:q,success
d0de1f2b659e1588000000000000000d,2024-01-10T11:57:06+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ps uax|grep trino,success
d0de1f2b659e1588000000000000000d,2024-01-10T11:57:20+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,/opt/presto/bin/launcher status,success
d0de1f2b659e1588000000000000000d,2024-01-10T11:57:24+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,/opt/presto/bin/launcher stop,success
d0de1f2b659e1588000000000000000d,2024-01-10T11:58:14+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd /hbdata/presto/data/var/log/,success
d0de1f2b659e1588000000000000000d,2024-01-10T11:58:15+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
d0de1f2b659e1588000000000000000d,2024-01-10T11:58:21+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,tail -f server.log,success
480011fc659e15ea000000000000000d,2024-01-10T11:58:44+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-03,172.28.3.121,/opt/presto/bin/launcher status,success
480011fc659e15ea000000000000000d,2024-01-10T11:58:48+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-03,172.28.3.121,/opt/presto/bin/launcher stop,success
480011fc659e15ea000000000000000d,2024-01-10T11:58:58+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-03,172.28.3.121,ps aux|grep trino,success
480011fc659e15ea000000000000000d,2024-01-10T11:59:13+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-03,172.28.3.121,cd /hbdata/trino/data/,success
480011fc659e15ea000000000000000d,2024-01-10T11:59:14+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-03,172.28.3.121,ll,success
480011fc659e15ea000000000000000d,2024-01-10T11:59:22+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-03,172.28.3.121,cd etc/,success
480011fc659e15ea000000000000000d,2024-01-10T11:59:23+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-03,172.28.3.121,ll,success
480011fc659e15ea000000000000000d,2024-01-10T11:59:33+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-03,172.28.3.121,cat config.properties,success
21d1eb4b659e163e000000000000000d,2024-01-10T12:00:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd /opt/trino/etc/,success
21d1eb4b659e163e000000000000000d,2024-01-10T12:00:07+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cat config.properties,success
71ea232b659e166a000000000000000d,2024-01-10T12:00:52+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-01,************,ps aux|grep trino,success
71ea232b659e166a000000000000000d,2024-01-10T12:01:05+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-01,************,cat /opt/trino/etc/config.properties,success
84df6031659e16d7000000000000000d,2024-01-10T12:02:38+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd /opt/trino/etc/,success
84df6031659e16d7000000000000000d,2024-01-10T12:02:39+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
84df6031659e16d7000000000000000d,2024-01-10T12:02:44+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cat config.properties.bak,success
84df6031659e16d7000000000000000d,2024-01-10T12:02:52+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cat config.properties,success
84df6031659e16d7000000000000000d,2024-01-10T12:03:24+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cp config.properties config.properties.bak20240110,success
84df6031659e16d7000000000000000d,2024-01-10T12:03:25+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
84df6031659e16d7000000000000000d,2024-01-10T12:03:33+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,less config.properties.bak20240110,success
84df6031659e16d7000000000000000d,2024-01-10T12:04:12+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,vim config.properties,success
84df6031659e16d7000000000000000d,2024-01-10T12:04:30+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,:wq,success
84df6031659e16d7000000000000000d,2024-01-10T12:04:41+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd ../bin/,success
84df6031659e16d7000000000000000d,2024-01-10T12:04:42+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
84df6031659e16d7000000000000000d,2024-01-10T12:04:51+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,./launcher restart,success
84df6031659e16d7000000000000000d,2024-01-10T12:05:03+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,./launcher status,success
84df6031659e16d7000000000000000d,2024-01-10T12:06:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd /hbdata/trino/data/var/,success
84df6031659e16d7000000000000000d,2024-01-10T12:06:05+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
84df6031659e16d7000000000000000d,2024-01-10T12:06:07+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd log/,success
84df6031659e16d7000000000000000d,2024-01-10T12:06:08+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
84df6031659e16d7000000000000000d,2024-01-10T12:06:32+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
84df6031659e16d7000000000000000d,2024-01-10T12:07:08+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,tail -f server.log-20240110.120456,success
84df6031659e16d7000000000000000d,2024-01-10T12:07:33+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,tail -f launcher.log,success
84df6031659e16d7000000000000000d,2024-01-10T12:13:46+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd ../../,success
84df6031659e16d7000000000000000d,2024-01-10T12:13:48+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
84df6031659e16d7000000000000000d,2024-01-10T12:13:50+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd etc/,success
84df6031659e16d7000000000000000d,2024-01-10T12:13:51+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
84df6031659e16d7000000000000000d,2024-01-10T12:13:53+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cat config.properties,success
84df6031659e16d7000000000000000d,2024-01-10T15:42:02+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ping presto.cogdocs.com,success
3857192a659e5c0a0000000000000013,2024-01-10T16:58:23+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,docker restA,success
3857192a659e5c0a0000000000000013,2024-01-10T16:58:41+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][    0$ mycli  1$ ~  (2*$~)  3$ ..2322a9/result  4$ ~/forensic_etl  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl    ][10/01  8:58],success
3857192a659e5c0a0000000000000013,2024-01-10T19:15:48+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,[q【,success
3857192a659e5c0a0000000000000013,2024-01-10T19:16:14+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,【[0AABA,success
3857192a659e5c0a0000000000000013,2024-01-10T19:16:21+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,qAAA,success
3857192a659e5c0a0000000000000013,2024-01-10T19:16:26+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,qAA,success
3857192a659e5c0a0000000000000013,2024-01-10T19:16:48+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,AABBq432,success
3857192a659e5c0a0000000000000013,2024-01-10T19:16:51+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,A,success
3857192a659e5c0a0000000000000013,2024-01-10T19:26:52+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,[,success
3857192a659e5c0a0000000000000013,2024-01-10T19:27:24+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][    0$ mycli  1$ ~  (2*$~)  3-$ ..2322a9/result  4$ ~/forensic_etl  5$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl    ][10/01 11:27],success
3857192a659e5c0a0000000000000013,2024-01-10T19:27:44+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,[,success
1dd91ab7659f47b5000000000000000d,2024-01-11T09:43:23+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd /var/log/hue,success
1dd91ab7659f47b5000000000000000d,2024-01-11T09:43:24+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
1dd91ab7659f47b5000000000000000d,2024-01-11T09:44:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,tar zcvf /tmp/runcpserver.log.20240111-02.tar.gz runcpserver.log runcpserver.log.1,success
1509d46a659f47ee000000000000000d,2024-01-11T09:44:20+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-03,172.28.3.121,cd /var/log/hue,success
1509d46a659f47ee000000000000000d,2024-01-11T09:44:21+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-03,172.28.3.121,ll,success
1509d46a659f47ee000000000000000d,2024-01-11T09:44:44+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-03,172.28.3.121,tar zcvf /tmp/runcpserver.log.20240111-03.tar.gz runcpserver.log runcpserver.log.1 runcpserver.log.2,success
b7e4b89a659f4811000000000000000d,2024-01-11T09:44:53+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-05,172.28.3.93,cd /var/log/hue,success
b7e4b89a659f4811000000000000000d,2024-01-11T09:44:54+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-05,172.28.3.93,ll,success
b7e4b89a659f4811000000000000000d,2024-01-11T09:45:18+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-05,172.28.3.93,tar zcvf /tmp/runcpserver.log.20240111-05.tar.gz runcpserver.log runcpserver.log.1 runcpserver.log.2,success
706b5a12659f4836000000000000000d,2024-01-11T09:45:28+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /tmp,success
706b5a12659f4836000000000000000d,2024-01-11T09:45:29+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
706b5a12659f4836000000000000000d,2024-01-11T09:45:43+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,scp 172.28.3.8:/tmp/runcpserver.log.20240111-02.tar.gz .,success
706b5a12659f4836000000000000000d,2024-01-11T09:45:52+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,scp 172.28.3.121:/tmp/runcpserver.log.20240111-03.tar.gz .,success
706b5a12659f4836000000000000000d,2024-01-11T09:46:01+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,scp 172.28.3.93:/tmp/runcpserver.log.20240111-05.tar.gz .,success
706b5a12659f4836000000000000000d,2024-01-11T09:46:02+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
706b5a12659f4836000000000000000d,2024-01-11T09:46:19+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ossutil cp runcpserver.log.20240111-02.tar.gz  oss://miniglobal-devops/hue_log/,success
706b5a12659f4836000000000000000d,2024-01-11T09:46:29+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ossutil cp runcpserver.log.20240111-03.tar.gz  oss://miniglobal-devops/hue_log/,success
706b5a12659f4836000000000000000d,2024-01-11T09:46:34+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ossutil cp runcpserver.log.20240111-05.tar.gz  oss://miniglobal-devops/hue_log/,success
706b5a12659f4836000000000000000d,2024-01-11T09:49:10+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,rm -f runcpserver.log.20240111-0*,success
706b5a12659f4836000000000000000d,2024-01-11T09:49:11+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
f3e184ef659f491f000000000000000d,2024-01-11T09:49:24+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,rm -f /tmp/runcpserver.log.20240111-02.tar.gz,success
38aef769659f4929000000000000000d,2024-01-11T09:49:33+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-03,172.28.3.121,rm -f /tmp/runcpserver.log.20240111-03.tar.gz,success
e22db76d659f4930000000000000000d,2024-01-11T09:49:44+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-05,172.28.3.93,rm -f /tmp/runcpserver.log.20240111-05.tar.gz,success
c2c540f2659f5ed80000000000000013,2024-01-11T11:22:08+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][      0$ mycli  1$ ~  2-$ docker  3$ ..2322a9/result  4!$ vim  (5*$~/forensic_etl)  6$ ..forensic_task  7$ ~/forensic_etl       ][11/01  3:22],success
c2c540f2659f5ed80000000000000013,2024-01-11T11:22:09+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][      0$ mycli  1$ ~  2-$ docker  3$ ..2322a9/result  4!$ vim  (5*$~/forensic_etl)  6$ ..forensic_task  7$ ~/forensic_etl       ][11/01  3:22],success
c2c540f2659f5ed80000000000000013,2024-01-11T11:22:31+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][      0-$ mycli  1$ ~  2$ docker  3$ ..2322a9/result  4!$ vim  (5*$~/forensic_etl)  6$ ..forensic_task  7$ ~/forensic_etl       ][11/01  3:22],success
c2c540f2659f5ed80000000000000013,2024-01-11T11:22:34+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][      0$ mycli  1$ ~  2$ docker  (3*$..2322a9/result)  4!$ vim  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl       ][11/01  3:22],success
c2c540f2659f5ed80000000000000013,2024-01-11T11:22:34+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][      0$ mycli  1$ ~  2$ docker  (3*$..f475b438974da)  4!$ vim  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl       ][11/01  3:22],success
c2c540f2659f5ed80000000000000013,2024-01-11T11:22:38+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][      0$ mycli  1$ ~  2$ docker  (3*$..f475b438974da)  4!$ vim  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl       ][11/01  3:22],success
c2c540f2659f5ed80000000000000013,2024-01-11T11:22:38+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][      0$ mycli  1$ ~  2$ docker  (3*$..8974da/result)  4!$ vim  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl       ][11/01  3:22],success
c2c540f2659f5ed80000000000000013,2024-01-11T11:22:39+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][      0$ mycli  1$ ~  2$ docker  (3*$..8974da/result)  4!$ vim  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl       ][11/01  3:22],success
c2c540f2659f5ed80000000000000013,2024-01-11T11:22:44+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][      0$ mycli  1$ ~  2$ docker  (3*$..8974da/result)  4!$ vim  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl       ][11/01  3:22],success
c2c540f2659f5ed80000000000000013,2024-01-11T11:22:45+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][      0$ mycli  1$ ~  2$ docker  (3*$..8974da/result)  4!$ vim  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl       ][11/01  3:22],success
c2c540f2659f5ed80000000000000013,2024-01-11T11:22:46+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][      0$ mycli  1$ ~  2$ docker  (3*$..8974da/result)  4!$ vim  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl       ][11/01  3:22],success
c2c540f2659f5ed80000000000000013,2024-01-11T11:22:47+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][      0$ mycli  1$ ~  2$ docker  (3*$..f475b438974da)  4!$ vim  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl       ][11/01  3:23],success
c2c540f2659f5ed80000000000000013,2024-01-11T11:22:50+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][      0$ mycli  1$ ~  2$ docker  (3*$..f475b438974da)  4!$ vim  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl       ][11/01  3:23],success
c2c540f2659f5ed80000000000000013,2024-01-11T11:22:51+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][      0$ mycli  1$ ~  2$ docker  (3*$..f475b438974da)  4!$ vim  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl       ][11/01  3:23],success
c2c540f2659f5ed80000000000000013,2024-01-11T11:22:52+08:00,cmd.Command,avenir_forensic_deploy,**************,sec-forensic-01,************,sec-forensic-01][      0$ mycli  1$ ~  2$ docker  (3*$..f475b438974da)  4!$ vim  5-$ ~/forensic_etl  6$ ..forensic_task  7$ ~/forensic_etl       ][11/01  3:23],success
45c39ebb65a08e710000000000000019,2024-01-12T08:57:28+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
45c39ebb65a08e710000000000000019,2024-01-12T08:57:42+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,use proamsdwdb;,success
45c39ebb65a08e710000000000000019,2024-01-12T08:57:52+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
45c39ebb65a08e710000000000000019,2024-01-12T08:57:52+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,where f_state in(3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
45c39ebb65a08e710000000000000019,2024-01-12T08:57:52+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,group by f_currency;,success
45c39ebb65a08e710000000000000019,2024-01-12T09:41:06+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,use hbg_trade; ,success
45c39ebb65a08e710000000000000019,2024-01-12T09:41:16+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,select f_currency,sum(f_amount),success
45c39ebb65a08e710000000000000019,2024-01-12T09:41:16+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,from t_account_manage_fee_snapshot,success
45c39ebb65a08e710000000000000019,2024-01-12T09:41:16+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,where f_day in('********', '********', '********')  and f_state = 1 ,success
45c39ebb65a08e710000000000000019,2024-01-12T09:41:17+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,and f_currency in ('btc', 'eth','usdt','ht','shib','doge','xrp','eos','ltc','trx'),success
45c39ebb65a08e710000000000000019,2024-01-12T09:41:17+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,group by f_currency order by f_currency;,success
45c39ebb65a08e710000000000000019,2024-01-12T09:52:23+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,use proamsdwdb;,success
45c39ebb65a08e710000000000000019,2024-01-12T09:52:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
45c39ebb65a08e710000000000000019,2024-01-12T09:52:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,from,success
45c39ebb65a08e710000000000000019,2024-01-12T09:52:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,(,success
45c39ebb65a08e710000000000000019,2024-01-12T09:52:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
45c39ebb65a08e710000000000000019,2024-01-12T09:52:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    group by f_user_id,success
45c39ebb65a08e710000000000000019,2024-01-12T09:52:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
45c39ebb65a08e710000000000000019,2024-01-12T09:52:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
45c39ebb65a08e710000000000000019,2024-01-12T09:52:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
45c39ebb65a08e710000000000000019,2024-01-12T09:52:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-01-11',success
45c39ebb65a08e710000000000000019,2024-01-12T09:52:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    and dw.f_state = 11,success
45c39ebb65a08e710000000000000019,2024-01-12T09:52:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
45c39ebb65a08e710000000000000019,2024-01-12T09:52:34+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,) user_amount,success
45c39ebb65a08e710000000000000019,2024-01-12T09:52:34+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,where amount >= 100;,success
45c39ebb65a08e710000000000000019,2024-01-12T09:53:29+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,0,success
45c39ebb65a08e710000000000000019,2024-01-12T09:54:37+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-01-11'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 10000;,success
45c39ebb65a08e710000000000000019,2024-01-12T09:55:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
45c39ebb65a08e710000000000000019,2024-01-12T09:55:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,from,success
45c39ebb65a08e710000000000000019,2024-01-12T09:55:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,(,success
45c39ebb65a08e710000000000000019,2024-01-12T09:55:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
45c39ebb65a08e710000000000000019,2024-01-12T09:55:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
45c39ebb65a08e710000000000000019,2024-01-12T09:55:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
45c39ebb65a08e710000000000000019,2024-01-12T09:55:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
45c39ebb65a08e710000000000000019,2024-01-12T09:55:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-01-11',success
45c39ebb65a08e710000000000000019,2024-01-12T09:55:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) > '2024-01-04' ,success
45c39ebb65a08e710000000000000019,2024-01-12T09:55:34+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    and dw.f_state = 11,success
45c39ebb65a08e710000000000000019,2024-01-12T09:55:34+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    group by f_user_id,success
45c39ebb65a08e710000000000000019,2024-01-12T09:55:34+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,) user_amount,success
45c39ebb65a08e710000000000000019,2024-01-12T09:55:34+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,where amount >= 100;,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:47:59+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:48:15+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,use hbg_trade,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:48:18+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,show tables;,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:48:58+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,show databases;,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:55:36+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,use mgt_console,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:55:41+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,show tables;,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:48+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"80a21bfda715b091b6f04895344213aca70cedaaadf9835cf2ca1a778607b03494bfbd1ab1f2099281e992c27be31d27",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:48+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,(,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:48+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"902542bccceb93617b5f905e5b0033b51429ad57a071ae61ceb83d17ec87d5d78d883bb4c11f4856916cf8b0ed0122ca",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:48+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"97ee04297333b9771c202053ad8d3194d7893f128250ffcc75a51e1807c224deb216ad0fc77700cfa0fe1ab6b9af4287",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:48+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"b26d9af92873b47be73b3dc24df994015d1d9e1cbeb7d56710412c0a560d4443cbfcffd522f32e101b65396c8fbe8ebc",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:48+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"95e97fd268f7515abd3be8ec9858f22ac2a4cba73deaafbcda7b5a2a1e259b59f95e24296ed86781d343e2b0913fb6e7",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:48+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,update t_validator_information set f_staking_state = 0 where f_public_key in ,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:48+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"b52e2e9ced40f7acdf7af97692c77f6a4083f6d79f468be4addef8f4c68ca3c4a4d6b185ad84c77b0b0cd5583ea07cc4",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:48+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"ad8aeca1327695b3f5c561463bc3e10bc4da9f6fa4803c1fa145a0d6a5e35481939bfd2067b1203286b68c6a1e21b431",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:48+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"93a2f36f38c55e943af2e30fe9d19bcfc5dcbdea7fb7a2cd03cb4494d270a58db23ae30ce97b0e5b19fa83af5573a0c2",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:48+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"a86038d7c12dc9c23bc84a793a0c04778a12dbf6b6caf37995b062fc4d6e47906b73ec0669c62549548d07274fb7339c",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:48+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"ae3be4efa32ccbdf8afdb0ec4b8077c2460547c351799ee786d2d6b7e76331244a3a67bd2bc7fa76b01a3503596d6875",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:48+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"b854cb77453ae210bd0329d37cface66e6a386a3b872201f5c68144bbc1eee6ae2fd959fde899a5afd060a20fa5195a2",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:48+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"8e02701496eeaed35e36612d939fac06142b8c9ecb5b74eeaa133f447e17de7123e5fea27c5677a4d752662ca1a5d602",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:48+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"b32df0d2806d4053cba698ca06c99521c542087d115f80dc8300f27a19b1d028b4036840b3eee87c83bcfc0a512aa0a3",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:48+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"82d8328a6ae2e0e7cbb11f4e95725e47773708ff58e160ce63c9a939920b8f9ce38a701f16103a37f5aabe01a4b5ad37",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:48+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"b63931909357970d2f7ded0f185f902e21bd945753a8c51a3066ce3f84dbaec09a5146502720042f5bdc0b697b0b2897",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:48+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"879b3cf7dad6f9ab0cd56a4c706e438dc767cae8ac00ec5720ab6e1d163c202c70f357dde171e6582bd5776f9722c7b2",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:48+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"82d478d1c4badf175712228e5de04933316c649499fc618d2c5669a02b3ae205f6520c9d05e94267708f2fe525ef6fd2",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:48+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"b8e8c31642ddab05789102ffed356274b792a2e62a5f41a3af3e22315a0aade23b5836bfb42a12821b4168bd7e0c6f75",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:48+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"ad271ffe5c9a75b21ddfab84fcc0affd1d5cb2fb92dd179eb8d2b94637f60da4c922aba4787f7d3ee8d72f2ace4601ee",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:48+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"a00c5d6b1fd0a5dee37481c11a199d30613a5ecb6d6ab7867303882d229f49e41e23d5616a428a194c2933c1aa8b04e1",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:48+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"a94c2366e89cfb756f6176924def071dc843aec01151c61bd335068ea9ed95335c70c2924c43095ca67f4ae968096837",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:49+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"8c38b409a71339c5af10806bda2db530a0c7634915fa371545b20223fce4f2a2b46754757f2e0c356ab4f5eb8ff45273",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:49+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"961ba8502e54e683df5176d23f60f7128cdd9581058ef7c978f04392197085139318c63d91c63e2f5410c0db8f03a781",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:49+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"acf39a1ca555bee1d566e75b887166a00b658e0cecb98f9a1948a2288ab757e83e11cab25963bc39583fe0f698324b24",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:49+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"b217437a38f3f4e4cda45c7b6a66a48a183f7cf16132684525b475f246da3cbf125f9d6db3480386e67b42c23eb91d61",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:49+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"a1e919c0a9c415fd185846ed5cccfd59d021fb842f41bfb57bb361d710f5524883b3910868cdeb23352f183cae3111ea",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:49+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"b723f21b3c9d08e1e05baf23292bc9bad4d8cddc1dc66227d275c5df2b178b22f40076899d194c1feb646a6c87a878cb",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:49+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"b4394eccd3394655965f84e9f64b2a4e11cfe6941066d5389597ccc57bbfd27df71c6dd3a0c4c0d2540816deb1e3577f",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:49+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"a56d6ce02d3e2f48f5bc7599219fc5005b43202e03920ea657d5b322359a60a08ab0ccf435494e497170a36ac8823437",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:49+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"a672abd2149b459d7ee34e8fc4854201f452bd7d4c726cf38f29b40b8ab613ebcc6cdd7efa15fc2b18933e270e3fb9dd",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:49+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"953bc94a782295d2fc363ef25d053d7d11f75945375d579cee6a044f8c9e238a7bb06fb4bf77dbc89cbc26ff5926efcb",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:49+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"887be98edc0840e26828514fa364d2bd13695326a9e8f2576c9a15acb6a31db581e4f8d9f77dc084f438a5582fb82841",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:49+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"8a2acb9de05df4835948d1f4c7141d38d09284c5d93c9c8ec7b3d98204f1d26d7536660c2c3f05ba3fe389dea1bb9637",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:49+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"a6b3c7f5f566f13f26c1370846ad22035482c8bf93c9544cab5f2e5ecdfdc39221e5cf2f8e9021673ba9ece00933cbbc",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:49+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"807aa76b20ff79f70e82cc8706be0a725e51dbb73ed430505899723d8665e4cef4c8cba849cd8dbf0127b183e01d3eed",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:49+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"ad2937527b503ad19fe1713fd332c655a0bbcea5989df69aafbadc13fe2816901b223809d534790704c8072b2935033e",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:49+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"90467f9b487f72aaaaaa82a7e88c17f8712a87851e8d82059412804637bf276f805f10c1416182df8242811de122c9ac",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:50+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"8299c1182e3aa0c885bd74a3ffe6c1cf36e3b1c936705f21fcc8de49e9814eb6bbf06dc910e251703c0ea2f8b37f08fb",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:50+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"a954a57bfa98e8b6ace166c0d53ff71e495c0ac1a993d2474abccaf2f2cc53bc1138a22d07839fd913b0bb621aa4e0e1",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:50+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"9598e4ddf9039904a289a6d1e7877257a16527a8d03d0f83c9a7317b552e3d6e8641a9174cb44351846fe8c624f02bd7",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:50+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"a97550b3e2d929168239558a290883e530c562c57f6d8787f7574bc573dfdd28b22e2e71c8fb3d00d825b896d19d120b",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:50+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"97a5bdd90cc511c05d9b9e90a82d073fcfcecd630f6364a88264ae7ec172587d02298841730db8282fca92502047f705",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:50+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"af83eb50a8fb2c1b54725e035800e6b2e2451fd9321f009a756504e0ef7e6ac926ed2a9dc5ec767f241f38a6b89be755",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:50+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"a47bb63245c805b0d4abe3372675ae2f272a699911b808094d8b11f74c640dcbee71d4851ff1a32dea9b425bc45e5cad",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:50+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"8fa0572d2333dbc4f68508386176b478e4b0180ba8bba0cf1e9800dc4487873cdb790a281d6e30e6c6ea2d7c221178b7",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:50+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"88f2a9037a00855af8ecd646f9546de9687f40dbaf7eb8f5b6a1f54de93836fa2072799a74dbf7ef4290c46a3f343c47",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:50+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"b48fde053b1b232d7ecb610c382af68d55317c3a25592a98a9426a1eb1919b9928378f07207983d109de36aaa942ae80",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:50+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"8d13b3defe7ed7cdfd6bdc6685099c8456d6248c78dd91a066b3c380f27bb66d57e752f56a988fa4f2974faed879eb3e",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:50+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"88ee888f2d595e75b75cffb95c59ca9c704f95ff6d5315b2a02b199cd861520c48617a75ffa1cd116e7fcf608d51bd2f",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:50+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"83951cfb8792906ecbdbb7f94eb4a391652dc0ff99deb7fb707ea34b0c72324024762f7ef71f8d2842c256c71902ffff",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:50+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"b2c98f9d9af95e721fd95b8a6a258982eea9f7f11b1d75fd190d855b44981c0090c70e00658a86b1df4e825eb5221272",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:50+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"8fcf0f449c5c317679fb12d061f79e8d2031b70232b2aa0ada541fef0841be8a44502fc8df13a5678840cebe3c9925a8",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:50+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"b76816dc197a146388d882ec18da1a42b8642550252e404e9a31fd0d07b750ab789a3bb2824b0905c36ce42a81e69619",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:51+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"affb34d391ace5e9cfc349ac465b7cc222e27ef098b2086281970b8200deeabcdd14e51ac6ea30d67e6a3cfed227f461",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:51+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"8054dbf4e4ee6ac9990d2f2585d55810f0d0bd68087a843cbd0ac74d60f43c15a681d048fae11801ddfe8e619d5aa261",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:51+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"976f29e1e2266b02f614dc9d3fb3dd3ba9be7a6b1e4694913b13a5a9e2c2b4db20df614510ff8ba9df85d8673145f87c",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:51+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"b93717135290cd98f61dd93fd7fbe346401ed3216f926b42d22e45b94661aea0c8b90692466dac913e715ed05f9de256",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:51+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"99feb9b61dd6be4a02fff103637f1279c5ea19cc4973973726d108e844160ef19b2c1facf79dd7be85d1c09f6e1c8394",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:51+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"a8100ef4134d7e08c11a4cf8a75a1810e7f82f7e961354b1000471531d0875a905f2b185dc6299b588cbfc6f2abd4608",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:51+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"a1db4ef36c3657aff53a157e36a730e3e0f5d004acf65eda1744d084a73272776af6daaa2463bcdf5a6fa2fef0910d5b",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:51+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"8190713d90c00434336063b390b243366cc098b0a7c8a42ea052b7cbb05f18b433b0fdbc8f420b29354aac2537d83697",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:51+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"91f9821a190fc7a2512842dbd74c46c2de1afc9425f4559dafd96e165b11d4d99db797e7908eed2664f3bd84dfc074c5",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:51+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"84096b9168b11a0450672257e9894f789ba2927a2fae9d8ebd7f0f574ecfca0076210de5affa21646dc95380cdb5e7f7",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:51+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"a7b909c4f3c3f3e7c776eeff03bb4e1cd047325cc747f6ad443bc00d2537b02aed2b330e59aec97297debc7ae8ea31df",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:51+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"b040b9ee94477927600eb4262ee41e3cae2b39982155307dafda46d311ee3fb3ae762d6d8c74fcafafe3a4962f9d5dc7",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:51+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"8ab27e1d3c228b75db654bd7053010f6bfb21e4f81f2efaf450781f9a2f580e829dbada22efecd1eb3ff16c3160e564a",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:51+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"92dff7a89aecc9e3caf9abfbde32487e91d07f65a01d0bbace9e16c99bca7775aaf15ced6f4e9efeef6ca6e903ce1257",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:51+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"b7525b462df5fb19f53bbdb40de17b001b087d01e0832724dce8a3da7b074b7942cc744b2a2c20c00a280aca4b309c3f",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:51+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"932b500ba640380c8ccf072a2a7477f548a449612d02d229db62d691ddf49e950051ae8466b3d31b599bc85535567e23",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:51+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"9941c9bbcd5c5aeed92689ebce9689561d590a120b715caf2bc59ad6f692309a7a0da8a5699586104ccb56c38b6f8832",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:52+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"808dec52d1eefb2c6497330a09a9be10309b056bdf07482add1a6973ea255b6a78464210d7520d2d93917107887724ff",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:52+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"92160f5f4918f788d19260e84c63c809bd64a6468bc93a3ac455f64a31c81c06339c643f519229d0d28fa66aee1afb48",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:52+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"8f5df65c73428eee46e8e16482e664f227ba477dade6a150b6b8ccd4c2d27ecab05597547aa3c78ec259337cb270b2fa",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:52+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"b85e7e74a44308aaae576b61a261c9569ea3e9825973cdf1f02c577059ae97f8ee5e8ef6a3ffd6db4f0db53135e96f56",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:52+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"8cf70c6e580a35424c1ad3f236b20ae35857ae04c5312f739fe93914c60ea1fbdb4e0aac3e2e1201ddf6f8fc8150e662",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:52+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"a100eb72e9adf00778e8e98c1402023668d49d0adce0204b911ce6281f5ac9a253dca2562753d1bab3bb5924d40b5a3a",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:52+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"a2ef6fc2b01bb1ebe26634c939be5f55fd99dc1892aeeef9bf9d902882c912f33a2ef6833c4a6ac9cf6dfe1c0741d025",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:52+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"8fe6629cfb9af501822de653a79dff913088df309f7d84192ab9becdaa8b58bf2191a92e79599d09bf9c29d992ab7cb8",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:52+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"a282b838046b0cad19461e5a6ab458959560e8f51efae737b2c3f60b3097190be2fbc49350ad96246d8c4b7fe8dea212",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:52+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"8094d354f402586ee19c3026093827b92b3ec39fe0eb8ceec272246ae18445e2255702d16c63e5e1432576d76790ecf0",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:52+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"ad0cd60ce1f736e73ba3960d895c907e70907cdaf98a5b18d2d3173e3c25fa6f5b6a9d03feddd2dcfdd5a7715a246801",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:52+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"b94ed28e6231938065297a5450f3b22703f07b2bd65e8ca24f5ab1594999644a70ec61717ce59483a5d6e1e34eda4fed",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:52+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"a59ef8c1fc3f0c61451956eb81938af4d43c22702ac1641f68da19a36b51bb75fd4c5a7981c52eba5cb40a8f03c727b8",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:52+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"97c0b3a124a465fda192a94a9934bcd0a4d1e8a825e1d04344b8047b2734fb34a93bebcc6e8de61193f5b676b85245b2",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:52+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"b1d02878821f8863f490248d0cedce81c695db1db66bddab1bd759aafa2d938a8ee372870c254af37527e3dc7cb7b521",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:52+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"8244048810271ad96c5b6278a37467ec67e7297a13e0414a80291c8b56b6b837a80bf2bf3885b8bf68ceeeef3c502d5a",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:53+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"b9e207bdc8083828bd7289000639c90e41ced202e9e924c4f4ebc683812c107b49e82d03bddfb98704d2b37103c22b5e",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:53+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"927e79ba23973e429ec96866112061022f61b46c0535033a1a2931468bb22fbd915f15edf66e341c746ef4d7636b1b28",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:53+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"a8784623a4ed0cbd1ac987f8b21731db5eda964c66442e2982cdc8a47c4453821a2808c9c9118ee06fa8eda72f2120bb",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:53+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"854d4de53f6146e41a7b2fb5a937b34cd39a12ec4cbd9a50875bf8c447eab553ae6d39f33e53b90c7d97636c84e211c2",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:53+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"b75b02eecbe2267d603443c2a32f68b1c41dc2edb020c0007a9362948c3827303d22a53da3a7adf58309a26696601387",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:53+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"8b70c483de3bf120b97798093dd9aedea451d16253dfcc830609e9cd05597e82362b2bfd408dd6e765dfd2e53b3cb2db",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:53+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"a1303000d155e6c452ed97cf8f4a79cf20ef4e2036cf8b393bdbf0884889a139694731ea626a163665f91c7a535d5f76",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:53+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"acb5b6bd1a3e0e311cd1a4b42a7b7a6550f5c0bcc7735ee342064367f3156a61b77fcf1548c37250effe09acccdd2bdb",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:53+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"90ab1f4b7f85fab0da26cd17c3239c893fe7f7a6a744006ac01e6e496b7d509a072d9a08b3d0c025d4f658aaec7c67c1",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:53+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"99a336ff3147fe2532e66778c342a6136bd0d7d6826f928130953a0aa7da0de997be69d90b9a5579119e9a4e9d82387f",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:53+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"914ef1190dfa56db72c67d70431f5babbf307ff9c5f826507d6522a1f4e52fb2443eca8cbdcc16e40799f40d4d6ad07a",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:53+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"a77449c050fafce7aa85e9909bb9e4c6e23a79f2a369d9a06e2b4c6a48be0020477baf94d7113f010f73b6207fd98121",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:53+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"8a9eff81163783d2b057e5963b6089ac73bd0bdadbacc4a99dd478a68aaf697982427ef80978c102b903f4ba6e3c064a",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:53+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"8e62babf2d2e7ed886c4db279a740179b35715ccb65d615dfaa93d6d9b89b33f215762ef5975d2066c1b36c3f425e223",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:53+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"922732ae9e8304daa42803212cacbdcf9fd659b3b49995e38cb561bf0db5bd305997ec6fc680aaafe592c24446f2994b",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:53+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"b5240a8d3c287dc0b25f964740433655d4a4d6502fcb89054782433a686a0eef04e36f642e07881346eb4aabc13c22a5",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:53+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"8227f44e18ac499303a9155e31df3ece2c9061db12ea3901dee2a4e653710859d44a27ecc6bf70f9c1c170227aceff73",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:54+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"a4b55a8e09baab6e6af385a42d402f2c2a9e2e7b3a36d990206869a692b7febdfa85144a7f96206a7c53700d5adc4d83",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:54+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"a4b4e5b04adbc62f0128312b4235282f5b7f397cb001feb94d2ddafa22809abcff2a128b33d895f03e45c1218bec362f",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:54+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"8eefca930ab021452bea76322df430b846ed497efc6f4cf5bb3abfcb84aae02543b2f5c45840aecc3f8e2ab7ac471f3f",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:54+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"b58362dcf97b40f953e7b3a5d5347b9d2fc30802bb4dc0eae23a45c8d797e647b90b42125f6384c1bd463688f24cca38",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:54+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"803f8c780b63900547b540aa6240e203739710af7fbde560e79f1db54f1a52d1371d4197eb1dcbf62b8e48610168a933",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:54+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"b0e6f36862127466a2ab40d0878e6b4b9d27ce8e8454f8726a7a663f11e3ae454d5d819541137ef0897e9a0a69715f22",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:54+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"b72a7d495748756de82e1a818b60c759c0990b3d5900fce0fceb4f77e3514cd4e2764b894685e0d565689b52f8b44be3",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:54+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"8416ca5402f213c0128351b1b8de5cf17668880fda5899d3314ffe163a6a84c0b74b954a755acfb4a5d5fb68f61ce408",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:54+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"b8405abb4958f7d31817c5c772ee6a694066daf0c7b5d8134a48360ec6487cbaa30270859eb80dd889d7d661714b420b",success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:54+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"a861106e73c1aacb4b58d13b16458519e8b231c6de564cc2ceaf449290cc4984648294480a87c6d1ab7ed1eb07445248",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:54+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"b1f34a62b1dde6d485bb5296421c67dffb9db6c4e86aab943747d6507d8504f2f0956e837cdcedba70944cd8ad056ad7",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:54+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"8cd2d17eb18ac84a63fc13feb7befeac9124b6639e0cba64ff2a1f8c50c4e4b8531a5217ea2b171e22cea5e9a07299e6",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:54+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"a7c63cc618d58e506a7fc65365412c998eb2b25b0587204ba4d03118c95a88e44f93bff3311a25294d1c17eefdf07553",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:54+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"918e1bcfe38787780f89ad3e77f516fb1535511f6c62c97468eaf86f7454c677a1106375405841e7c29dba5243cc0dee",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:54+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"884dd14dc3f76a172dcf7e39a65b2b20aab63887a19aeb68b4127aa08e6c6b7f0e90f4b88e918a6a639627b1ab05823c",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:57:54+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,"b7ec860c32af3d9f1237026c722547704b99e7226d824531f6454011f8deb4b274f3e8bb34617f8652700525d13738b2",,success
3eacacd965a0e09d000000000000000d,2024-01-12T14:58:28+08:00,cmd.Command,avenir_user01,223.104.3.204,miniglobal-devops,***********,);,success
fe26bebb65a12aa9000000000000000d,2024-01-12T20:03:54+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
fe26bebb65a12aa9000000000000000d,2024-01-12T20:04:06+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,use mgt_console,success
fe26bebb65a12aa9000000000000000d,2024-01-12T20:05:07+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,desc t_validator_information;,success
fe26bebb65a12aa9000000000000000d,2024-01-12T20:05:41+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from t_validator_information where f_public_key = '902542bccceb93617b5f905e5b0033b51429ad57a071ae61ceb83d17ec87d5d78d883bb4c11f4856916cf8b0ed0122ca' limit 2;,success
fe26bebb65a12aa9000000000000000d,2024-01-12T20:06:10+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from t_validator_information where f_public_key limit 3;,success
fe26bebb65a12aa9000000000000000d,2024-01-12T20:07:46+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from t_validator_information where f_public_key = "98f12cc321228de9fbb7b32d329cb9ed358694f4f020b22f92d383cb7289c6e203f7e6aeb6c9b32e1625dcf4251d79fb";,success
fe26bebb65a12aa9000000000000000d,2024-01-12T20:08:18+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from t_validator_information where f_public_key = "b32c7f95aaab9d1f5d21225db416fdc14a32952ca979b0d68390dc1835d483c651609568012ebd501f428bbc4dfa6958";,success
fe26bebb65a12aa9000000000000000d,2024-01-12T20:08:48+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select count(*) from t_validator_information;,success
fe26bebb65a12aa9000000000000000d,2024-01-12T20:09:29+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select distinct f_agent  from t_validator_information;,success
fe26bebb65a12aa9000000000000000d,2024-01-12T20:10:42+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,quit,success
7f5187e365a492720000000000000019,2024-01-15T10:03:37+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
7f5187e365a492720000000000000019,2024-01-15T10:03:59+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,use proamsdwdb;,success
7f5187e365a492720000000000000019,2024-01-15T10:04:06+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
7f5187e365a492720000000000000019,2024-01-15T10:04:06+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where f_state in(3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
7f5187e365a492720000000000000019,2024-01-15T10:04:07+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,group by f_currency;,success
a86b710965a5f272000000000000000d,2024-01-16T11:05:23+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
a86b710965a5f272000000000000000d,2024-01-16T11:05:35+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,use hbg_trade,success
a86b710965a5f272000000000000000d,2024-01-16T11:05:38+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,show tables;,success
a86b710965a5f272000000000000000d,2024-01-16T11:06:00+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,desc t_hbg_message;,success
a86b710965a5f272000000000000000d,2024-01-16T11:06:16+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from t_hbg_message limit 1;,success
a86b710965a5f272000000000000000d,2024-01-16T11:07:03+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,quit,success
a86b710965a5f272000000000000000d,2024-01-16T11:07:12+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,3,success
a86b710965a5f272000000000000000d,2024-01-16T11:07:21+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,show databases;,success
a86b710965a5f272000000000000000d,2024-01-16T11:07:27+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,use huobi_uc,success
a86b710965a5f272000000000000000d,2024-01-16T11:07:33+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,show tables;,success
a86b710965a5f272000000000000000d,2024-01-16T11:08:10+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,quit,success
a86b710965a5f272000000000000000d,2024-01-16T11:08:12+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
a86b710965a5f272000000000000000d,2024-01-16T11:08:26+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,e '%mc%';,success
a86b710965a5f272000000000000000d,2024-01-16T11:08:31+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,use huobi_mc,success
a86b710965a5f272000000000000000d,2024-01-16T11:08:34+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,show tables;,success
a86b710965a5f272000000000000000d,2024-01-16T11:09:52+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from mc_email_history_202312 limit 2;,success
a86b710965a5f272000000000000000d,2024-01-16T11:10:03+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from mc_email_history_202312 limit 2\G;,success
a86b710965a5f272000000000000000d,2024-01-16T11:11:03+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from mc_email_history_202312 order by f_id desc limit 2\G;,success
a86b710965a5f272000000000000000d,2024-01-16T11:11:11+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,desc mc_email_history_202312;,success
a86b710965a5f272000000000000000d,2024-01-16T11:11:19+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from mc_email_history_202312 order by id desc limit 2\G;,success
a86b710965a5f272000000000000000d,2024-01-16T11:11:44+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,show create table mc_email_history_202312;,success
a86b710965a5f272000000000000000d,2024-01-16T11:14:10+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from mc_email_history_202312 where receive_state<>0 order by id desc limit 2\G;,success
a86b710965a5f272000000000000000d,2024-01-16T11:16:14+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from mc_email_history_202312 where email like '%gmail.com%' order by id desc limit 2\G;,success
a86b710965a5f272000000000000000d,2024-01-16T11:17:50+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select * from mc_email_history_202312 where gmt_received<>0 order by id desc limit 2\G;,success
a86b710965a5f272000000000000000d,2024-01-16T11:19:00+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select count(*) from mc_email_history_202312 where email like '%gmail.com%';,success
a86b710965a5f272000000000000000d,2024-01-16T11:19:13+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select count(*) from mc_email_history_202312 where email like '%yahoo%';,success
a86b710965a5f272000000000000000d,2024-01-16T11:21:47+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select count(*) from mc_email_history_202311 where email like '%yahoo%';,success
a86b710965a5f272000000000000000d,2024-01-16T11:21:56+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,select count(*) from mc_email_history_202311 where email like '%gmail.com%';,success
67d481a665a61258000000000000000d,2024-01-16T13:21:30+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-01,************,ls /,success
7a2af8d465a6129c000000000000000d,2024-01-16T13:22:38+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ls /,success
21f5723865a6130c000000000000000d,2024-01-16T13:24:31+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-07,172.28.3.5,ls /,success
dcd0c69e65a61320000000000000000d,2024-01-16T13:24:50+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-09,172.28.3.71,ls /,success
c2e20e1465a613a9000000000000000d,2024-01-16T13:27:08+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ls,success
c2e20e1465a613a9000000000000000d,2024-01-16T13:27:15+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,find / -name "*hue*",success
c2e20e1465a613a9000000000000000d,2024-01-16T13:27:38+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd /var/loghu,success
c2e20e1465a613a9000000000000000d,2024-01-16T13:27:39+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ls,success
c2e20e1465a613a9000000000000000d,2024-01-16T13:27:41+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll -thr,success
c2e20e1465a613a9000000000000000d,2024-01-16T13:27:49+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd metrics-hue_server/,success
c2e20e1465a613a9000000000000000d,2024-01-16T13:27:49+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ls,success
c2e20e1465a613a9000000000000000d,2024-01-16T13:27:54+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,head metrics.log,success
c2e20e1465a613a9000000000000000d,2024-01-16T13:29:38+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ls /user,success
c2e20e1465a613a9000000000000000d,2024-01-16T13:29:39+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ls,success
7c80966565a61454000000000000000d,2024-01-16T13:30:00+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,ssu - hdfs,success
7c80966565a61454000000000000000d,2024-01-16T13:30:02+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,su - hdfs,success
7c80966565a61454000000000000000d,2024-01-16T13:30:08+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,hadoop fs ls -l,success
7c80966565a61454000000000000000d,2024-01-16T13:30:14+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,hadoop fs -ls -l,success
7c80966565a61454000000000000000d,2024-01-16T13:30:19+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,hadoop fs ls /,success
7c80966565a61454000000000000000d,2024-01-16T13:30:23+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,hadoop fs -ls /,success
7c80966565a61454000000000000000d,2024-01-16T13:30:27+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,hadoop fs -ls /user/,success
7c80966565a61454000000000000000d,2024-01-16T13:30:31+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,hadoop fs -ls /user/bigdata/,success
7c80966565a61454000000000000000d,2024-01-16T13:30:35+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,hadoop fs -ls /user/bigdata/export/,success
7c80966565a61454000000000000000d,2024-01-16T13:30:53+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,hadoop fs -ls /user/bigdata/export/1749f6f36388bbfd-37d455a70000000d_611353535_data.0.,success
7c80966565a61454000000000000000d,2024-01-16T13:31:13+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,hadoop fs -cat  /user/bigdata/export/1749f6f36388bbfd-37d455a70000000d_611353535_data.0,success
7c80966565a61454000000000000000d,2024-01-16T13:31:21+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,hadoop fs -cat  /user/bigdata/export/1749f6f36388bbfd-37d455a70000000d_611353535_data.0.,success
7c80966565a61454000000000000000d,2024-01-16T13:32:10+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,df -h,success
7c80966565a61454000000000000000d,2024-01-16T13:32:21+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,hadoop fs -du /user/bigdata/export/1749f6f36388bbfd-37d455a70000000d_611353535_data.0.,success
7c80966565a61454000000000000000d,2024-01-16T13:32:30+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,hadoop fs -du -h  /user/bigdata/export/1749f6f36388bbfd-37d455a70000000d_611353535_data.0.,success
7c80966565a61454000000000000000d,2024-01-16T13:32:55+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,hadoop fs -put /user/bigdata/export/1749f6f36388bbfd-37d455a70000000d_611353535_data.0.  /tmp/,success
7c80966565a61454000000000000000d,2024-01-16T13:33:08+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,hadoop fs -du /user/bigdata/export/1749f6f36388bbfd-37d455a70000000d_611353535_data.0.,success
7c80966565a61454000000000000000d,2024-01-16T13:33:20+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,hadoop fs -put /user/bigdata/export/1749f6f36388bbfd-37d455a70000000d_611353535_data.0.  /tmp/impala.txt,success
7c80966565a61454000000000000000d,2024-01-16T13:33:29+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,hadoop fs put /user/bigdata/export/1749f6f36388bbfd-37d455a70000000d_611353535_data.0.  /tmp/impala.txt,success
7c80966565a61454000000000000000d,2024-01-16T13:33:34+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,hadoop fs,success
7c80966565a61454000000000000000d,2024-01-16T13:34:05+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,hadoop fs -get  /user/bigdata/export/1749f6f36388bbfd-37d455a70000000d_611353535_data.0.  /tmp/impala.txt,success
7c80966565a61454000000000000000d,2024-01-16T13:34:18+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,cat /tmp/impala.txt |head,success
7c80966565a61454000000000000000d,2024-01-16T13:34:26+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,cd /tmp,success
7c80966565a61454000000000000000d,2024-01-16T13:34:27+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,ll,success
7c80966565a61454000000000000000d,2024-01-16T13:34:38+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,wc -l impala.txt,success
7c80966565a61454000000000000000d,2024-01-16T13:35:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,su - hdfs\,success
7c80966565a61454000000000000000d,2024-01-16T13:35:12+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,su - hdfs,success
7c80966565a61454000000000000000d,2024-01-16T13:35:25+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,hadoop fs -ls /user/bigdata/export/,success
7c80966565a61454000000000000000d,2024-01-16T13:35:56+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,hadoop fs -get  /user/bigdata/export/1749f6f36388bbfd-37d455a70000000e_1066915207_data.0.   /tmp/impala2.txt,success
7c80966565a61454000000000000000d,2024-01-16T13:36:14+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,hadoop fs -get  /user/bigdata/export/1749f6f36388bbfd-37d455a70000000f_1564619273_data.0.   /tmp/impala3.txt,success
7c80966565a61454000000000000000d,2024-01-16T13:36:20+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,cd /tmp,success
7c80966565a61454000000000000000d,2024-01-16T13:36:21+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,ll,success
7c80966565a61454000000000000000d,2024-01-16T13:36:26+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,wc -l impala*,success
7c80966565a61454000000000000000d,2024-01-16T13:36:47+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,head impala.txt,success
7c80966565a61454000000000000000d,2024-01-16T13:37:12+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,vim impala.txt,success
7c80966565a61454000000000000000d,2024-01-16T13:37:21+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,:q,success
7c80966565a61454000000000000000d,2024-01-16T13:40:43+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,ll,success
7c80966565a61454000000000000000d,2024-01-16T13:40:57+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,ip a,success
2fec414665a61705000000000000000d,2024-01-16T13:41:28+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /tmp,success
2fec414665a61705000000000000000d,2024-01-16T13:41:29+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
2fec414665a61705000000000000000d,2024-01-16T13:41:41+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,scp ************:/tmp/impala.txt .,success
2fec414665a61705000000000000000d,2024-01-16T13:41:47+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,scp ************:/tmp/impala2.txt .,success
2fec414665a61705000000000000000d,2024-01-16T13:41:50+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,scp ************:/tmp/impala3.txt .,success
2fec414665a61705000000000000000d,2024-01-16T13:41:52+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
2fec414665a61705000000000000000d,2024-01-16T13:42:01+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,wc -l impala*,success
2fec414665a61705000000000000000d,2024-01-16T13:42:30+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ossutil cp impala.txt  oss://miniglobal-devops/hue_log/,success
2fec414665a61705000000000000000d,2024-01-16T13:42:38+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ossutil cp impala2.txt  oss://miniglobal-devops/hue_log/,success
2fec414665a61705000000000000000d,2024-01-16T13:42:44+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ossutil cp impala3.txt  oss://miniglobal-devops/hue_log/,success
750c2f8465a619a6000000000000000d,2024-01-16T13:52:40+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ssh 172.28.8.167,success
750c2f8465a619a6000000000000000d,2024-01-16T13:52:46+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /hb,success
750c2f8465a619a6000000000000000d,2024-01-16T13:52:47+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T13:53:06+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ls /,success
750c2f8465a619a6000000000000000d,2024-01-16T13:53:10+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ls /root/,success
750c2f8465a619a6000000000000000d,2024-01-16T13:53:20+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /opt/,success
750c2f8465a619a6000000000000000d,2024-01-16T13:53:22+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T13:54:35+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,scp:/opt/ne.,success
750c2f8465a619a6000000000000000d,2024-01-16T13:54:38+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T13:55:19+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ssh 172.28.8.172,success
750c2f8465a619a6000000000000000d,2024-01-16T13:55:21+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,yes,success
750c2f8465a619a6000000000000000d,2024-01-16T13:55:27+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,pwd,success
750c2f8465a619a6000000000000000d,2024-01-16T13:55:28+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T13:56:06+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,opt/,success
750c2f8465a619a6000000000000000d,2024-01-16T13:56:14+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ssh 172.28.8.172,success
750c2f8465a619a6000000000000000d,2024-01-16T13:56:16+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /opt,success
750c2f8465a619a6000000000000000d,2024-01-16T13:56:16+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T13:56:38+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,mkdir /hbdata,success
750c2f8465a619a6000000000000000d,2024-01-16T13:56:59+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,tar zxvf nexus-3.29.0-02-unix.tar.gz,success
750c2f8465a619a6000000000000000d,2024-01-16T13:57:01+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T13:57:15+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,mv -r nexus-3.29.0-02 /hbdata/,success
750c2f8465a619a6000000000000000d,2024-01-16T13:57:23+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,mv  nexus-3.29.0-02 /hbdata/,success
750c2f8465a619a6000000000000000d,2024-01-16T13:57:28+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,mv sonatype-work/ /hbdata/,success
750c2f8465a619a6000000000000000d,2024-01-16T13:57:32+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T13:57:34+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /hbdata/,success
750c2f8465a619a6000000000000000d,2024-01-16T13:57:35+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T13:57:42+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd sonatype-work/,success
750c2f8465a619a6000000000000000d,2024-01-16T13:57:43+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T13:57:48+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd nexus3/,success
750c2f8465a619a6000000000000000d,2024-01-16T13:57:48+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T13:58:03+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd ../..,success
750c2f8465a619a6000000000000000d,2024-01-16T13:58:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T13:58:07+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd nexus-3.29.0-02/,success
750c2f8465a619a6000000000000000d,2024-01-16T13:58:07+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T13:58:11+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd bin/,success
750c2f8465a619a6000000000000000d,2024-01-16T13:58:13+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T13:58:18+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,./nexus,success
750c2f8465a619a6000000000000000d,2024-01-16T13:58:47+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,java -version,success
750c2f8465a619a6000000000000000d,2024-01-16T13:59:15+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ssh 172.28.8.172,success
750c2f8465a619a6000000000000000d,2024-01-16T13:59:21+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /hb,success
750c2f8465a619a6000000000000000d,2024-01-16T13:59:22+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T13:59:36+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,apt-get install java,success
750c2f8465a619a6000000000000000d,2024-01-16T14:00:25+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T14:01:02+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,scp jdk-8u211-linux-x64.tar.gz  172.28.8.172:/opt/,success
750c2f8465a619a6000000000000000d,2024-01-16T14:01:07+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ssh 172.28.8.172,success
750c2f8465a619a6000000000000000d,2024-01-16T14:01:11+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /opt,success
750c2f8465a619a6000000000000000d,2024-01-16T14:01:12+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T14:01:24+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,tar zxvf jdk-8u211-linux-x64.tar.gz,success
750c2f8465a619a6000000000000000d,2024-01-16T14:01:28+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T14:01:35+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,java -version,success
750c2f8465a619a6000000000000000d,2024-01-16T14:01:43+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd jdk1.8.0_211/,success
750c2f8465a619a6000000000000000d,2024-01-16T14:01:47+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T14:02:01+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,apt-get update,success
750c2f8465a619a6000000000000000d,2024-01-16T14:02:18+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,apt-get search java,success
750c2f8465a619a6000000000000000d,2024-01-16T14:02:25+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,apt-get find java,success
750c2f8465a619a6000000000000000d,2024-01-16T14:02:37+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,apt-get install java,success
750c2f8465a619a6000000000000000d,2024-01-16T14:02:48+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,apt-get help,success
750c2f8465a619a6000000000000000d,2024-01-16T14:03:23+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,apt-get install jdk8,success
750c2f8465a619a6000000000000000d,2024-01-16T14:03:36+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T14:03:43+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd bin/,success
750c2f8465a619a6000000000000000d,2024-01-16T14:03:44+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T14:03:52+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,pwd,success
750c2f8465a619a6000000000000000d,2024-01-16T14:04:01+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd ..,success
750c2f8465a619a6000000000000000d,2024-01-16T14:04:03+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T14:04:16+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,vim /etc/prof,success
750c2f8465a619a6000000000000000d,2024-01-16T14:04:36+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,:q,success
750c2f8465a619a6000000000000000d,2024-01-16T14:04:44+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,vim /etc/bash.bashrc,success
750c2f8465a619a6000000000000000d,2024-01-16T14:05:57+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,:w,success
750c2f8465a619a6000000000000000d,2024-01-16T14:06:02+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,:q,success
750c2f8465a619a6000000000000000d,2024-01-16T14:06:06+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,echo $PATH,success
750c2f8465a619a6000000000000000d,2024-01-16T14:06:21+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,vim /etc/bash.bashrc,success
750c2f8465a619a6000000000000000d,2024-01-16T14:06:29+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,/PATH,success
750c2f8465a619a6000000000000000d,2024-01-16T14:06:38+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,:q,success
750c2f8465a619a6000000000000000d,2024-01-16T14:06:43+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,vim /etc/profile,success
750c2f8465a619a6000000000000000d,2024-01-16T14:07:02+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,:q,success
750c2f8465a619a6000000000000000d,2024-01-16T14:07:19+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,.cd /root,success
750c2f8465a619a6000000000000000d,2024-01-16T14:07:20+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T14:07:27+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,vim .bashrc,success
750c2f8465a619a6000000000000000d,2024-01-16T14:07:35+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,/PATH,success
750c2f8465a619a6000000000000000d,2024-01-16T14:07:43+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,:q,success
750c2f8465a619a6000000000000000d,2024-01-16T14:07:50+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,vim .profile,success
750c2f8465a619a6000000000000000d,2024-01-16T14:07:58+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,:q,success
750c2f8465a619a6000000000000000d,2024-01-16T14:10:00+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ls,success
750c2f8465a619a6000000000000000d,2024-01-16T14:10:00+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /etc/,success
750c2f8465a619a6000000000000000d,2024-01-16T14:10:07+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,vim bash.bashrc,success
750c2f8465a619a6000000000000000d,2024-01-16T14:10:38+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,:wq,success
750c2f8465a619a6000000000000000d,2024-01-16T14:10:45+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,source bash.bashrc,success
750c2f8465a619a6000000000000000d,2024-01-16T14:10:51+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,java -version,success
750c2f8465a619a6000000000000000d,2024-01-16T14:11:08+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /hbdata/nexus-3.29.0-02/bin/,success
750c2f8465a619a6000000000000000d,2024-01-16T14:11:09+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ls,success
750c2f8465a619a6000000000000000d,2024-01-16T14:11:17+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,./nexus start,success
750c2f8465a619a6000000000000000d,2024-01-16T14:11:34+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /hbdata/sonatype-work/,success
750c2f8465a619a6000000000000000d,2024-01-16T14:11:35+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T14:11:37+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd nexus3/,success
750c2f8465a619a6000000000000000d,2024-01-16T14:11:38+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T14:11:51+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,less admin.password,success
750c2f8465a619a6000000000000000d,2024-01-16T14:24:44+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,vim /etc/security/limits.conf,success
750c2f8465a619a6000000000000000d,2024-01-16T14:25:33+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,:wq,success
750c2f8465a619a6000000000000000d,2024-01-16T14:25:53+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /hbdata/nexus-3.29.0-02/bin/,success
750c2f8465a619a6000000000000000d,2024-01-16T14:26:02+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,./nexus restart,success
750c2f8465a619a6000000000000000d,2024-01-16T14:27:41+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,./nexus stop,success
750c2f8465a619a6000000000000000d,2024-01-16T14:28:13+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /hbdata/sonatype-work/nexus3/,success
750c2f8465a619a6000000000000000d,2024-01-16T14:28:13+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T14:28:27+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,mv blobs blobs.orig,success
750c2f8465a619a6000000000000000d,2024-01-16T14:28:28+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T14:29:15+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,1,success
750c2f8465a619a6000000000000000d,2024-01-16T14:29:25+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cat /etc/fst,success
750c2f8465a619a6000000000000000d,2024-01-16T14:30:02+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /hbdata/sonatype-work,success
750c2f8465a619a6000000000000000d,2024-01-16T14:30:03+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T14:30:05+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd nexus3/,success
750c2f8465a619a6000000000000000d,2024-01-16T14:30:05+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T14:32:58+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,2,success
750c2f8465a619a6000000000000000d,2024-01-16T14:33:08+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,so,success
750c2f8465a619a6000000000000000d,2024-01-16T14:33:09+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T14:33:15+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,mkdir blobs,success
750c2f8465a619a6000000000000000d,2024-01-16T14:33:16+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T14:34:23+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ls blobs/,success
750c2f8465a619a6000000000000000d,2024-01-16T14:34:40+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll blobs/,success
750c2f8465a619a6000000000000000d,2024-01-16T14:34:42+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T14:35:05+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cat /etc/fstab,success
750c2f8465a619a6000000000000000d,2024-01-16T14:37:49+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T14:37:55+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll blobs/,success
750c2f8465a619a6000000000000000d,2024-01-16T14:38:00+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cat /etc/fstab,success
750c2f8465a619a6000000000000000d,2024-01-16T14:38:23+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,/hbdata/nexus-3.29.0-02/bin/nexus start,success
750c2f8465a619a6000000000000000d,2024-01-16T14:43:34+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T14:43:38+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd log/,success
750c2f8465a619a6000000000000000d,2024-01-16T14:43:39+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T14:43:42+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd tasks/,success
750c2f8465a619a6000000000000000d,2024-01-16T14:43:43+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T14:43:50+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,tail -f repository.maven.rebuild-metadata-20240116144321274.log,success
750c2f8465a619a6000000000000000d,2024-01-16T14:45:37+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T14:45:42+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,tail -f create.browse.nodes-20240116144530758.log,success
750c2f8465a619a6000000000000000d,2024-01-16T14:47:23+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd ..,success
750c2f8465a619a6000000000000000d,2024-01-16T14:47:24+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T14:47:39+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,tail -100 nexus.log,success
750c2f8465a619a6000000000000000d,2024-01-16T14:54:18+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,1,success
750c2f8465a619a6000000000000000d,2024-01-16T14:54:31+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /hbso/lta,success
750c2f8465a619a6000000000000000d,2024-01-16T14:54:32+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T14:57:41+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /tmp,success
750c2f8465a619a6000000000000000d,2024-01-16T14:57:42+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T14:58:27+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cat impala.txt impala2.txt impala3.txt > query-impala-10649_zks_20240109.csv,success
750c2f8465a619a6000000000000000d,2024-01-16T14:59:48+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,wc -l query-impala-10649_zks_20240109.csv,success
750c2f8465a619a6000000000000000d,2024-01-16T15:00:40+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ossutil cp query-impala-10649_zks_20240109.csv oss://miniglobal-devops/hue_log/,success
750c2f8465a619a6000000000000000d,2024-01-16T15:03:39+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ossutil cp query-impala-10649_zks_20240109.csv oss://miniglobal-devops/hue_log/,success
750c2f8465a619a6000000000000000d,2024-01-16T15:03:57+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ssh ************,success
750c2f8465a619a6000000000000000d,2024-01-16T15:04:06+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /hbso/l,success
750c2f8465a619a6000000000000000d,2024-01-16T15:04:07+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T15:04:15+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd tasks/,success
750c2f8465a619a6000000000000000d,2024-01-16T15:04:16+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T15:04:28+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd ..,success
750c2f8465a619a6000000000000000d,2024-01-16T15:04:35+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,tail -f *.log,success
750c2f8465a619a6000000000000000d,2024-01-16T15:07:01+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd tasks/,success
750c2f8465a619a6000000000000000d,2024-01-16T15:07:02+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T15:31:53+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T15:31:58+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,less impala.txt,success
750c2f8465a619a6000000000000000d,2024-01-16T15:32:07+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,less impala2.txt,success
750c2f8465a619a6000000000000000d,2024-01-16T15:32:14+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,less impala3.txt,success
750c2f8465a619a6000000000000000d,2024-01-16T15:32:19+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,less query-impala-10649_zks_20240109.csv,success
750c2f8465a619a6000000000000000d,2024-01-16T15:42:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ssh ************,success
750c2f8465a619a6000000000000000d,2024-01-16T15:42:51+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,lo,success
750c2f8465a619a6000000000000000d,2024-01-16T15:42:52+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T15:42:55+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll tasks/,success
750c2f8465a619a6000000000000000d,2024-01-16T15:43:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,rmdir tasks/,success
750c2f8465a619a6000000000000000d,2024-01-16T15:43:05+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T15:43:34+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,/hbnebinnerestart,success
750c2f8465a619a6000000000000000d,2024-01-16T15:43:49+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T15:43:53+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T15:44:00+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T15:44:05+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T15:45:25+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T15:46:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T15:46:05+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T15:46:57+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T15:47:09+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,tail -f *.log,success
750c2f8465a619a6000000000000000d,2024-01-16T15:49:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T15:49:29+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,mkdir tasks,success
750c2f8465a619a6000000000000000d,2024-01-16T15:49:30+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T15:49:34+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd tasks/,success
750c2f8465a619a6000000000000000d,2024-01-16T15:49:35+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T15:49:37+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T15:50:45+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,/hbdata/nexus-3.63.0-01/bin/nexus restart,success
750c2f8465a619a6000000000000000d,2024-01-16T15:53:49+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T15:53:51+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T16:14:29+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd ..,success
750c2f8465a619a6000000000000000d,2024-01-16T16:14:30+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T16:14:41+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,tail -f *.log,success
750c2f8465a619a6000000000000000d,2024-01-16T16:14:58+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,/hbdata/nexus-3.63.0-01/bin/nexus restart,success
750c2f8465a619a6000000000000000d,2024-01-16T16:15:13+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,tail -f *.log,success
750c2f8465a619a6000000000000000d,2024-01-16T16:15:53+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T16:16:26+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,grep -i scheduler nexus.log,success
750c2f8465a619a6000000000000000d,2024-01-16T16:18:02+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,less ne.,success
750c2f8465a619a6000000000000000d,2024-01-16T16:27:46+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBq,success
5f9e977565a63e3b000000000000000d,2024-01-16T16:28:56+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,cd /hbdata/soft/miniglobal-apps/apps/miniglobal/miniglobal-finance-daily-report/,success
5f9e977565a63e3b000000000000000d,2024-01-16T16:28:57+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,ll,success
5f9e977565a63e3b000000000000000d,2024-01-16T16:29:14+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,make build TAG=PRD_T20240116,success
750c2f8465a619a6000000000000000d,2024-01-16T16:36:27+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,less nexus.log,success
750c2f8465a619a6000000000000000d,2024-01-16T16:41:10+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /hbdata/sonatype-work,success
750c2f8465a619a6000000000000000d,2024-01-16T16:41:16+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd ,success
750c2f8465a619a6000000000000000d,2024-01-16T16:41:17+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T16:41:26+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd cache/,success
750c2f8465a619a6000000000000000d,2024-01-16T16:41:26+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T16:41:49+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd ..,success
750c2f8465a619a6000000000000000d,2024-01-16T16:41:50+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T16:44:20+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,/hbdata/nexus-3.63.0-01/bin/nexus stop,success
750c2f8465a619a6000000000000000d,2024-01-16T16:44:46+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,mv cache cache.bak.0116,success
750c2f8465a619a6000000000000000d,2024-01-16T16:44:48+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T16:45:02+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,mkdir cache,success
750c2f8465a619a6000000000000000d,2024-01-16T16:45:03+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T16:45:24+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ip a,success
88c1572b65a6421a000000000000000d,2024-01-16T16:45:37+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ssh ************,success
88c1572b65a6421a000000000000000d,2024-01-16T16:45:51+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,so/,success
88c1572b65a6421a000000000000000d,2024-01-16T16:45:52+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
88c1572b65a6421a000000000000000d,2024-01-16T16:46:10+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd log/,success
88c1572b65a6421a000000000000000d,2024-01-16T16:46:10+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
88c1572b65a6421a000000000000000d,2024-01-16T16:46:15+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,tail -f *.log,success
750c2f8465a619a6000000000000000d,2024-01-16T16:46:31+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,/hbdata/nexus-3.63.0-01/bin/nexus start,success
750c2f8465a619a6000000000000000d,2024-01-16T16:48:41+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,/hbdata/nexus-3.63.0-01/bin/nexus stop,success
750c2f8465a619a6000000000000000d,2024-01-16T16:49:16+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /hbdata/nexus-3.63.0-01/lib/support/,success
750c2f8465a619a6000000000000000d,2024-01-16T16:49:16+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
750c2f8465a619a6000000000000000d,2024-01-16T16:49:31+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,java -jar nexus-orient-console.jar,success
88c1572b65a6421a000000000000000d,2024-01-16T16:50:06+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
88c1572b65a6421a000000000000000d,2024-01-16T16:50:10+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd ..,success
88c1572b65a6421a000000000000000d,2024-01-16T16:50:11+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
88c1572b65a6421a000000000000000d,2024-01-16T16:50:18+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd db,success
88c1572b65a6421a000000000000000d,2024-01-16T16:50:18+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
88c1572b65a6421a000000000000000d,2024-01-16T16:51:13+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd config/,success
88c1572b65a6421a000000000000000d,2024-01-16T16:51:14+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ls,success
88c1572b65a6421a000000000000000d,2024-01-16T16:51:22+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,pwd,success
750c2f8465a619a6000000000000000d,2024-01-16T16:51:51+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,connect plocal:/hbdata/sonatype-work/nexus3/db/config admin admin,success
750c2f8465a619a6000000000000000d,2024-01-16T17:02:23+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,show tables;,success
750c2f8465a619a6000000000000000d,2024-01-16T17:02:27+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,;,success
750c2f8465a619a6000000000000000d,2024-01-16T17:02:34+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,help,success
750c2f8465a619a6000000000000000d,2024-01-16T17:03:34+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,display record #69:343,success
750c2f8465a619a6000000000000000d,2024-01-16T17:03:43+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,display record 343,success
750c2f8465a619a6000000000000000d,2024-01-16T17:03:56+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,display record 69,success
750c2f8465a619a6000000000000000d,2024-01-16T17:03:59+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,display record #69,success
750c2f8465a619a6000000000000000d,2024-01-16T17:04:07+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,343,success
750c2f8465a619a6000000000000000d,2024-01-16T17:04:19+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,display record "#69:343",success
750c2f8465a619a6000000000000000d,2024-01-16T17:04:26+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,display record "#69",success
750c2f8465a619a6000000000000000d,2024-01-16T17:04:43+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,display record 69:343,success
750c2f8465a619a6000000000000000d,2024-01-16T17:05:00+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,<>,success
750c2f8465a619a6000000000000000d,2024-01-16T17:07:07+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,display record 69 343,success
750c2f8465a619a6000000000000000d,2024-01-16T17:07:35+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,display record 69；,success
750c2f8465a619a6000000000000000d,2024-01-16T17:07:42+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,display record 69;,success
750c2f8465a619a6000000000000000d,2024-01-16T17:07:54+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,display record 343;,success
750c2f8465a619a6000000000000000d,2024-01-16T17:08:06+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,display record 69:343;,success
750c2f8465a619a6000000000000000d,2024-01-16T17:11:36+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,exit,success
750c2f8465a619a6000000000000000d,2024-01-16T17:12:09+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,/hbnebinestart,success
750c2f8465a619a6000000000000000d,2024-01-16T17:22:10+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd ..,success
750c2f8465a619a6000000000000000d,2024-01-16T17:22:11+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,pwd,success
750c2f8465a619a6000000000000000d,2024-01-16T17:22:22+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /hbdata/sonatype-work/nexus3/log/,success
750c2f8465a619a6000000000000000d,2024-01-16T17:22:24+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll tasks/,success
750c2f8465a619a6000000000000000d,2024-01-16T17:30:10+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,tail -f *.log,success
750c2f8465a619a6000000000000000d,2024-01-16T17:31:44+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll tasks/,success
5cb5f4f165a65323000000000000000d,2024-01-16T17:57:58+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-01,172.28.3.139,df -h,success
5cb5f4f165a65323000000000000000d,2024-01-16T17:58:28+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-01,172.28.3.139,cd /ossdownload/,success
5cb5f4f165a65323000000000000000d,2024-01-16T17:58:29+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-01,172.28.3.139,ll,success
5cb5f4f165a65323000000000000000d,2024-01-16T17:58:43+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-01,172.28.3.139,head ossutil_download.log,success
5cb5f4f165a65323000000000000000d,2024-01-16T17:59:01+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-01,172.28.3.139,wc -l ossutil_download.log,success
5cb5f4f165a65323000000000000000d,2024-01-16T17:59:16+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-01,172.28.3.139,du -sh,success
5cb5f4f165a65323000000000000000d,2024-01-16T17:59:25+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-01,172.28.3.139,cd -,success
5cb5f4f165a65323000000000000000d,2024-01-16T17:59:40+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-01,172.28.3.139,umount /ossdownload,success
5cb5f4f165a65323000000000000000d,2024-01-16T17:59:42+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-01,172.28.3.139,df -h,success
5cb5f4f165a65323000000000000000d,2024-01-16T18:05:10+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-01,172.28.3.139,ll,success
5cb5f4f165a65323000000000000000d,2024-01-16T18:05:15+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-01,172.28.3.139,cd devops/,success
5cb5f4f165a65323000000000000000d,2024-01-16T18:05:16+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-01,172.28.3.139,ll,success
5cb5f4f165a65323000000000000000d,2024-01-16T18:05:41+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-01,172.28.3.139,ll hive/,success
5cb5f4f165a65323000000000000000d,2024-01-16T18:05:48+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-01,172.28.3.139,ll hive/oss_hive/,success
c64d6cc965a71a620000000000000019,2024-01-17T08:08:08+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
c64d6cc965a71a620000000000000019,2024-01-17T08:08:28+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,use proamsdwdb;,success
c64d6cc965a71a620000000000000019,2024-01-17T08:08:37+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
c64d6cc965a71a620000000000000019,2024-01-17T08:08:37+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,where f_state in(3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
c64d6cc965a71a620000000000000019,2024-01-17T08:08:38+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,group by f_currency;,success
df4d49fb65a7352f000000000000000d,2024-01-17T10:03:33+08:00,file.Upload,avenir_user01,***************,MiniAD04,172.28.2.251,xpr4j3_J}*JL3itq,QQA.txt,success
2aeecd8e65a73a50000000000000000d,2024-01-17T10:24:26+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,cd /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-hbg-encn/,success
2aeecd8e65a73a50000000000000000d,2024-01-17T10:24:27+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,ll,success
2aeecd8e65a73a50000000000000000d,2024-01-17T10:24:44+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,make build TAG=PRD_T401171,success
fe8824fd65a73d06000000000000000d,2024-01-17T10:35:54+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,history |less,success
fe8824fd65a73d06000000000000000d,2024-01-17T10:38:15+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,Gqsu - hdfs,success
fe8824fd65a73d06000000000000000d,2024-01-17T10:38:21+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-namenode-1c-02,************,s|less,success
df4d49fb65a7352f000000000000000d,2024-01-17T10:44:06+08:00,file.UploadSave,avenir_user01,***************,MiniAD04,172.28.2.251,keyboard.txt,success
df4d49fb65a7352f000000000000000d,2024-01-17T10:44:06+08:00,file.Upload,avenir_user01,***************,MiniAD04,172.28.2.251,ocr.txt,success
87a616af65a78219000000000000000d,2024-01-17T15:30:47+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,cd /hbdata/soft/miniglobal-apps/apps/miniglobal/miniglobal-management-console-assets/,success
87a616af65a78219000000000000000d,2024-01-17T15:30:48+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,ll,success
87a616af65a78219000000000000000d,2024-01-17T15:31:05+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,rm -rf k8s_resources/,success
87a616af65a78219000000000000000d,2024-01-17T15:31:09+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,vim README.md,success
87a616af65a78219000000000000000d,2024-01-17T15:31:18+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,:wq,success
87a616af65a78219000000000000000d,2024-01-17T15:31:22+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,vim Makefile,success
87a616af65a78219000000000000000d,2024-01-17T15:31:30+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,:wq,success
87a616af65a78219000000000000000d,2024-01-17T15:31:32+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,ll,success
87a616af65a78219000000000000000d,2024-01-17T15:31:41+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,rm market.json,success
87a616af65a78219000000000000000d,2024-01-17T15:31:43+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,cd build_command_pkgs/,success
87a616af65a78219000000000000000d,2024-01-17T15:31:44+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,ll,success
87a616af65a78219000000000000000d,2024-01-17T15:31:55+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,cd ..,success
87a616af65a78219000000000000000d,2024-01-17T15:31:58+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,vim build.sh,success
87a616af65a78219000000000000000d,2024-01-17T15:32:14+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,:wq,success
87a616af65a78219000000000000000d,2024-01-17T15:32:19+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,ll,success
87a616af65a78219000000000000000d,2024-01-17T15:32:29+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,make build TAG=PRD_T********,success
cc9bf07065a78be6000000000000000d,2024-01-17T16:12:35+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,cd /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-hbg-encn/,success
cc9bf07065a78be6000000000000000d,2024-01-17T16:12:36+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,ll,success
cc9bf07065a78be6000000000000000d,2024-01-17T16:12:50+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,make build TAG=PRD_T401171,success
cc9bf07065a78be6000000000000000d,2024-01-17T16:18:57+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,cd ../miniglobal-svf-app-cs/,success
cc9bf07065a78be6000000000000000d,2024-01-17T16:18:59+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,ll,success
cc9bf07065a78be6000000000000000d,2024-01-17T16:19:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,rm market.json,success
cc9bf07065a78be6000000000000000d,2024-01-17T16:19:10+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,rm -rf k8s_resources/,success
cc9bf07065a78be6000000000000000d,2024-01-17T16:19:13+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,vim README.md,success
cc9bf07065a78be6000000000000000d,2024-01-17T16:19:20+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,:wq,success
cc9bf07065a78be6000000000000000d,2024-01-17T16:19:25+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,vim Makefile,success
cc9bf07065a78be6000000000000000d,2024-01-17T16:19:34+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,:wq,success
cc9bf07065a78be6000000000000000d,2024-01-17T16:19:35+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,ll,success
cc9bf07065a78be6000000000000000d,2024-01-17T16:19:43+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,cd build_command_pkgs/,success
cc9bf07065a78be6000000000000000d,2024-01-17T16:19:44+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,ll,success
cc9bf07065a78be6000000000000000d,2024-01-17T16:19:53+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,rm -rf svf-app-cs/,success
cc9bf07065a78be6000000000000000d,2024-01-17T16:20:00+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,cd ..,success
cc9bf07065a78be6000000000000000d,2024-01-17T16:20:12+08:00,cmd.Command,avenir_user01,***************,miniglobal-deploy,172.28.3.27,make build TAG=PRD_T401171,success
1142cca865a8751f000000000000000d,2024-01-18T08:47:32+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,cd /var/log/hue,success
1142cca865a8751f000000000000000d,2024-01-18T08:47:33+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,ll,success
1142cca865a8751f000000000000000d,2024-01-18T08:48:59+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,tar zcvf /tmp/runcpserver.log.20230118-02.tar.gz runcpserver.log runcpserver.log.1 runcpserver.log.2,success
7684615965a87580000000000000000d,2024-01-18T08:49:09+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-03,172.28.3.121,cd /var/log/hue,success
7684615965a87580000000000000000d,2024-01-18T08:49:09+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-03,172.28.3.121,ll,success
7684615965a87580000000000000000d,2024-01-18T08:49:31+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-03,172.28.3.121,tar zcvf /tmp/runcpserver.log.20230118-03.tar.gz runcpserver.log runcpserver.log.1,success
4c83516865a875a0000000000000000d,2024-01-18T08:49:40+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-05,172.28.3.93,cd /var/log/hue,success
4c83516865a875a0000000000000000d,2024-01-18T08:49:41+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-05,172.28.3.93,ll,success
4c83516865a875a0000000000000000d,2024-01-18T08:50:04+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-05,172.28.3.93,tar zcvf /tmp/runcpserver.log.20230118-05.tar.gz runcpserver.log runcpserver.log.1,success
d5245bc665a875c2000000000000000d,2024-01-18T08:50:12+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,cd /tmp,success
d5245bc665a875c2000000000000000d,2024-01-18T08:50:12+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
d5245bc665a875c2000000000000000d,2024-01-18T08:50:24+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,rm -f impala*,success
d5245bc665a875c2000000000000000d,2024-01-18T08:50:27+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,rm query-impala-10649_zks_20240109.csv,success
d5245bc665a875c2000000000000000d,2024-01-18T08:50:30+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,y,success
d5245bc665a875c2000000000000000d,2024-01-18T08:50:31+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
d5245bc665a875c2000000000000000d,2024-01-18T08:50:46+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,scp 172.28.3.8:/tmp/runcpserver.log.20230118-02.tar.gz .,success
d5245bc665a875c2000000000000000d,2024-01-18T08:50:54+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,scp 172.28.3.121:/tmp/runcpserver.log.20230118-03.tar.gz .,success
d5245bc665a875c2000000000000000d,2024-01-18T08:51:02+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,scp 172.28.3.93:/tmp/runcpserver.log.20230118-05.tar.gz .,success
d5245bc665a875c2000000000000000d,2024-01-18T08:51:03+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
d5245bc665a875c2000000000000000d,2024-01-18T08:51:20+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ossutil cp runcpserver.log.20230118-02.tar.gz oss://miniglobal-devops/hue_log/,success
d5245bc665a875c2000000000000000d,2024-01-18T08:51:24+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ossutil cp runcpserver.log.20230118-03.tar.gz oss://miniglobal-devops/hue_log/,success
d5245bc665a875c2000000000000000d,2024-01-18T08:51:28+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ossutil cp runcpserver.log.20230118-05.tar.gz oss://miniglobal-devops/hue_log/,success
d5245bc665a875c2000000000000000d,2024-01-18T08:55:12+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
d5245bc665a875c2000000000000000d,2024-01-18T08:55:50+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,ll,success
d5245bc665a875c2000000000000000d,2024-01-18T08:55:59+08:00,cmd.Command,avenir_user01,***************,miniglobal-devops,***********,rm -f runcpserver.log.20230118-0*,success
bd557fe065a87725000000000000000d,2024-01-18T08:56:11+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-02,172.28.3.8,rm -f /tmp/runcpserver.log.20230118-02.tar.gz,success
7afe60c565a8772f000000000000000d,2024-01-18T08:56:19+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-03,172.28.3.121,rm -f /tmp/runcpserver.log.20230118-03.tar.gz,success
d9cb673a65a87738000000000000000d,2024-01-18T08:56:28+08:00,cmd.Command,avenir_user01,***************,miniglobal-bigdata-datanode-1c-05,172.28.3.93,rm -f /tmp/runcpserver.log.20230118-05.tar.gz,success
f89df5fb65a8a0a4000000000000000d,2024-01-18T11:53:18+08:00,cmd.Command,avenir_user01,223.104.41.52,miniglobal-deploy,172.28.3.27,cd /hbdata/soft/miniglobal-apps/apps/miniglobal/miniglobal-mulan-ams-dw/,success
f89df5fb65a8a0a4000000000000000d,2024-01-18T11:53:19+08:00,cmd.Command,avenir_user01,223.104.41.52,miniglobal-deploy,172.28.3.27,ll,success
f89df5fb65a8a0a4000000000000000d,2024-01-18T11:53:34+08:00,cmd.Command,avenir_user01,223.104.41.52,miniglobal-deploy,172.28.3.27,make build TAG=PRD_T20240118,success
51e8541665a8c04d0000000000000019,2024-01-18T14:08:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
51e8541665a8c04d0000000000000019,2024-01-18T14:08:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,use proamsdwdb;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:10:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:10:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:10:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:10:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:10:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:10:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:10:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:10:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-01-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:10:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-02-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:10:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:10:18+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:10:18+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:10:18+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 0;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:11:47+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:11:47+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:11:47+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:11:47+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:11:47+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:11:47+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:11:47+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:11:48+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-03-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:11:48+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:11:48+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:11:48+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:11:48+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 0;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:11:48+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-02-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:12:30+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:12:30+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:12:30+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:12:30+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:12:30+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:12:31+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:12:31+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:12:31+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-03-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:12:31+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-04-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:12:31+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:12:31+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:12:31+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:12:31+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 0;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:01+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:01+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:01+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:01+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:01+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:01+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:01+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:01+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-04-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:01+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-05-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:01+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:01+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:01+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:02+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 0;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:28+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:28+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:28+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:28+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:28+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:28+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:28+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:28+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-05-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:28+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:28+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:28+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:28+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-06-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:29+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 0;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:56+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:56+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:56+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:56+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:56+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:56+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:56+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:56+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-06-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:57+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 0;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:57+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:57+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-07-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:57+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:13:57+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:14:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:14:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:14:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:14:21+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:14:21+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:14:21+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:14:21+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:14:21+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-07-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:14:21+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-08-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:14:21+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:14:21+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:14:21+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 0;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:14:21+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:14:45+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:14:45+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:14:45+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:14:45+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:14:45+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:14:45+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:14:45+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:14:45+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-08-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:14:46+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-09-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:14:46+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:14:46+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:14:46+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:14:46+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 0;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:15:12+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:15:12+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:15:13+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:15:13+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:15:13+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:15:13+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:15:13+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:15:13+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-09-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:15:13+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-10-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:15:13+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:15:13+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:15:13+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:15:15+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 0;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:15:38+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:15:38+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:15:38+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:15:39+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:15:39+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:15:39+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:15:39+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:15:39+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-10-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:15:39+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-11-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:15:39+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:15:39+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:15:39+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:15:39+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 0;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:16:02+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:16:02+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:16:02+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:16:02+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:16:02+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:16:02+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:16:02+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:16:02+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-11-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:16:02+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-12-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:16:02+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:16:02+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:16:03+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:16:03+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 0;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:16:39+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:16:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:16:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:16:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:16:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:16:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:16:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:16:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-12-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:16:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2024-01-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:16:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:16:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:16:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:16:41+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 0;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:41:11+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:41:11+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:41:11+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:41:11+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:41:11+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:41:11+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:41:11+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:41:11+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-01-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:41:11+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2024-02-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:41:12+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:41:12+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:41:12+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:41:13+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 100;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:41:35+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:41:35+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:41:35+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:41:35+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:41:35+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:41:35+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:41:35+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-01-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:41:35+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:41:35+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-02-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:41:36+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 100;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:41:36+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:41:36+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:41:36+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:43:28+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:43:28+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:43:28+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:43:28+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:43:28+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:43:28+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:43:28+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:43:29+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 100;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:43:29+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-02-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:43:29+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-03-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:43:29+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:43:29+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:43:29+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:45:26+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:45:26+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-03-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:45:26+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:45:26+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-04-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:45:26+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:45:26+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:45:26+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:45:26+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:45:26+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:45:27+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:45:27+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:45:27+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:45:27+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 100;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:45:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:45:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:45:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:45:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:45:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:45:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:45:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:45:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-04-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:45:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-05-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:45:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:45:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:45:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:45:54+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 100;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:46:21+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:46:21+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:46:21+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:46:22+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:46:22+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:46:22+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:46:22+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:46:22+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-05-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:46:22+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-06-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:46:22+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 100;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:46:22+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:46:22+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:46:22+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:46:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:46:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:46:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:46:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:46:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:46:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-06-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:46:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:46:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:46:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-07-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:46:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:46:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:46:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:46:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 100;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:47:14+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:47:14+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:47:14+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:47:14+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:47:14+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:47:14+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:47:14+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:47:14+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-07-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:47:14+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-08-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:47:14+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:47:15+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:47:15+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:47:15+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 100;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:47:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:47:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:47:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:47:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:47:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:47:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:47:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:47:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-08-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:47:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-09-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:47:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:47:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:47:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:47:41+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 100;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:48:10+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:48:10+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:48:10+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:48:10+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:48:10+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:48:10+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-09-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:48:10+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:48:10+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:48:10+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-10-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:48:10+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:48:10+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:48:10+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:48:11+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 100;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:48:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:48:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:48:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:48:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:48:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:48:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:48:41+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:48:41+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-10-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:48:41+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-11-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:48:41+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:48:41+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:48:41+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:48:41+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 100;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:49:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:49:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:49:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:49:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:49:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:49:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:49:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:49:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-11-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:49:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-12-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:49:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:49:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:49:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:49:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 100;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:49:49+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:49:49+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:49:49+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:49:49+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:49:49+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:49:49+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:49:49+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:49:49+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-12-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:49:49+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2024-01-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:49:49+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:49:49+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:49:49+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:49:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 100;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:51:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:51:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:51:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:51:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:51:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:51:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:51:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:51:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-01-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:51:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-02-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:51:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:51:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:51:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:51:18+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 1000;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:51:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:51:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:51:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:51:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:51:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:51:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:51:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:51:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-02-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:51:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-03-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:51:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:51:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:51:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 1000;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:51:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:52:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:52:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:52:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:52:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:52:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:52:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:52:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:52:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-03-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:52:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-04-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:52:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:52:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:52:21+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:52:21+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 1000;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:52:58+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:52:58+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:52:58+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:52:58+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:52:58+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:52:58+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:52:58+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:52:58+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-04-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:52:58+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-05-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:52:58+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:52:58+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:52:58+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:52:59+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 1000;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:53:24+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:53:24+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:53:24+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:53:24+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:53:25+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:53:25+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:53:25+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:53:25+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-05-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:53:25+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-06-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:53:25+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:53:25+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:53:25+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:53:25+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 1000;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:53:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:53:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:53:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:53:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:53:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:53:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:53:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:53:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-06-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:53:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-07-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:53:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:53:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:53:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:53:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 1000;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:54:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:54:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:54:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:54:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:54:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:54:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:54:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:54:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-07-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:54:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-08-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:54:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:54:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:54:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:54:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 1000;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:54:42+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:54:42+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:54:42+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:54:42+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:54:42+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:54:42+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:54:42+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:54:42+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-08-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:54:43+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-09-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:54:43+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:54:43+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:54:43+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:54:43+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 1000;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:10+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:10+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:11+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 1000;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:11+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:11+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:11+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:11+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:11+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-09-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:11+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-10-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:11+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:11+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:11+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:11+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:36+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:36+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:36+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:37+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:37+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:37+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:37+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:37+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-10-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:37+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-11-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:37+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:37+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:37+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:37+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 1000;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:59+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:59+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:59+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:59+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:59+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:59+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:55:59+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:00+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-11-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:00+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-12-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:00+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:00+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:00+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:00+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 1000;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:24+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:24+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:24+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:24+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:24+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:24+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:24+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:24+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-12-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:24+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-01-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:24+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:24+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:24+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:25+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 1000;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:34+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:34+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:34+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 1000;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:34+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:34+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:34+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:34+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:34+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-12-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:34+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2024-01-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:34+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:34+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:34+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:56:34+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:58:12+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:58:12+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:58:12+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:58:12+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:58:12+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:58:13+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:58:13+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:58:13+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-12-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:58:13+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2024-01-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:58:13+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:58:13+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:58:13+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:58:13+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 10000;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:58:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:58:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:58:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:58:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:58:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:58:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:58:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:58:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:58:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-12-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:58:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-11-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:58:54+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:58:54+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:58:54+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 10000;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:59:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:59:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:59:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:59:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:59:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:59:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:59:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:59:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-10-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:59:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-11-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:59:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:59:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:59:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:59:21+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 10000;,success
51e8541665a8c04d0000000000000019,2024-01-18T14:59:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:59:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T14:59:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T14:59:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:59:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T14:59:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T14:59:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:59:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-09-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:59:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-10-01',success
51e8541665a8c04d0000000000000019,2024-01-18T14:59:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T14:59:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T14:59:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T14:59:52+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 10000;,success
51e8541665a8c04d0000000000000019,2024-01-18T15:00:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-09-01',success
51e8541665a8c04d0000000000000019,2024-01-18T15:00:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T15:00:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T15:00:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:00:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:00:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T15:00:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:00:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T15:00:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-08-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:00:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T15:00:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T15:00:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T15:00:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 10000;,success
51e8541665a8c04d0000000000000019,2024-01-18T15:01:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T15:01:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T15:01:41+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T15:01:41+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:01:41+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:01:41+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T15:01:41+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:01:41+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-07-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:01:41+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-08-01',success
51e8541665a8c04d0000000000000019,2024-01-18T15:01:41+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T15:01:41+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T15:01:41+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T15:01:41+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 10000;,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:06+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:06+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:06+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:06+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:06+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:06+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:06+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:06+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-06-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:06+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-07-01',success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:06+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:06+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:06+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:07+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 10000;,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:28+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-06-01',success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:28+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:28+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:28+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:28+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:28+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:28+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:28+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-05-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:28+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:29+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:29+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:29+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:29+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 10000;,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-04-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-05-01',success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T15:02:54+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 10000;,success
51e8541665a8c04d0000000000000019,2024-01-18T15:03:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T15:03:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T15:03:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T15:03:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:03:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:03:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T15:03:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:03:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-03-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:03:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-04-01',success
51e8541665a8c04d0000000000000019,2024-01-18T15:03:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T15:03:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T15:03:20+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T15:03:21+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 10000;,success
51e8541665a8c04d0000000000000019,2024-01-18T15:03:52+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T15:03:52+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T15:03:52+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T15:03:52+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:03:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 10000;,success
51e8541665a8c04d0000000000000019,2024-01-18T15:03:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T15:03:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:03:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-02-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:03:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:03:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T15:03:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T15:03:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T15:03:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-03-01',success
51e8541665a8c04d0000000000000019,2024-01-18T15:04:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
51e8541665a8c04d0000000000000019,2024-01-18T15:04:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from,success
51e8541665a8c04d0000000000000019,2024-01-18T15:04:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,(,success
51e8541665a8c04d0000000000000019,2024-01-18T15:04:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:04:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:04:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where amount >= 10000;,success
51e8541665a8c04d0000000000000019,2024-01-18T15:04:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:04:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2023-01-01' ,success
51e8541665a8c04d0000000000000019,2024-01-18T15:04:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) < '2023-02-01',success
51e8541665a8c04d0000000000000019,2024-01-18T15:04:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    and dw.f_state = 11,success
51e8541665a8c04d0000000000000019,2024-01-18T15:04:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    group by f_user_id,success
51e8541665a8c04d0000000000000019,2024-01-18T15:04:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,) user_amount,success
51e8541665a8c04d0000000000000019,2024-01-18T15:04:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
51e8541665a8c04d0000000000000019,2024-01-18T16:32:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,use hbg_trade; ,success
51e8541665a8c04d0000000000000019,2024-01-18T16:32:30+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from t_account_manage_fee_snapshot,success
51e8541665a8c04d0000000000000019,2024-01-18T16:32:30+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_currency,sum(f_amount),success
51e8541665a8c04d0000000000000019,2024-01-18T16:33:05+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where f_day like '2023%' and f_state = 1,success
51e8541665a8c04d0000000000000019,2024-01-18T16:33:05+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,and f_currency in ('btc', 'eth','usdt','ht','shib','doge','xrp','eos','ltc','trx'),success
51e8541665a8c04d0000000000000019,2024-01-18T16:33:06+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,group by f_currency order by f_currency;,success
51e8541665a8c04d0000000000000019,2024-01-18T17:13:09+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,exit,success
51e8541665a8c04d0000000000000019,2024-01-18T17:13:11+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,exit,success
ccc5235365a8f6ad000000000000000d,2024-01-18T18:00:17+08:00,cmd.Command,avenir_user01,192.168.10.10,miniglobal-deploy,172.28.3.27,cd /hbdata/soft/miniglobal-apps/apps/miniglobal/miniglobal-mulan-ams-dw/,success
ccc5235365a8f6ad000000000000000d,2024-01-18T18:00:18+08:00,cmd.Command,avenir_user01,192.168.10.10,miniglobal-deploy,172.28.3.27,ll,success
ccc5235365a8f6ad000000000000000d,2024-01-18T18:00:33+08:00,cmd.Command,avenir_user01,192.168.10.10,miniglobal-deploy,172.28.3.27,make build TAG=PRD_T20240118_1,success
0a551ef165a9d5b10000000000000019,2024-01-19T09:52:02+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
0a551ef165a9d5b10000000000000019,2024-01-19T09:52:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,use proamsdwdb;,success
0a551ef165a9d5b10000000000000019,2024-01-19T09:52:24+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
0a551ef165a9d5b10000000000000019,2024-01-19T09:52:24+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where f_state in(3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
0a551ef165a9d5b10000000000000019,2024-01-19T09:52:25+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,group by f_currency;,success
0a551ef165a9d5b10000000000000019,2024-01-19T10:05:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date ,success
0a551ef165a9d5b10000000000000019,2024-01-19T10:05:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,from t_withdraw_virtual  ,success
0a551ef165a9d5b10000000000000019,2024-01-19T10:05:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where f_state in (3,6,8),success
0a551ef165a9d5b10000000000000019,2024-01-19T10:05:17+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********, and from_unixtime(f_updated_at/1000) > '2022-11-01',success
0a551ef165a9d5b10000000000000019,2024-01-19T10:05:18+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,order by f_updated_at;,success
0a551ef165a9d5b10000000000000019,2024-01-19T10:05:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,c ,success
0a551ef165a9d5b10000000000000019,2024-01-19T10:06:40+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date  from t_withdraw_virtual   where f_state in (1,3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01' and f_currency = 'btc' order by f_updated_at;,success
0a551ef165a9d5b10000000000000019,2024-01-19T10:07:25+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date  from t_withdraw_virtual   where f_state in (1,3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01' and f_currency = 'btc' order by f_updated_at;,success
0a551ef165a9d5b10000000000000019,2024-01-19T10:07:38+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date  from t_withdraw_virtual   where f_state in (2,3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01' and f_currency = 'btc' order by f_updated_at;,success
0a551ef165a9d5b10000000000000019,2024-01-19T10:07:51+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date  from t_withdraw_virtual   where f_state in (4,3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01' and f_currency = 'btc' order by f_updated_at;,success
0a551ef165a9d5b10000000000000019,2024-01-19T10:09:26+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date  from t_withdraw_virtual   where f_state in (4,3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01' and f_currency = 'eth' order by f_updated_at;,success
0a551ef165a9d5b10000000000000019,2024-01-19T10:09:38+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date  from t_withdraw_virtual   where f_state in (4,3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01' and f_currency = 'usdt' order by f_updated_at;,success
a1944e0965aadb9d000000000000000d,2024-01-20T04:29:30+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,mysql -u superdba -P 3613 -p -h master-3613.docsl.com,success
a1944e0965aadb9d000000000000000d,2024-01-20T04:30:12+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,show proceslist;,success
a1944e0965aadb9d000000000000000d,2024-01-20T04:30:20+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,show processlist;,success
a1944e0965aadb9d000000000000000d,2024-01-20T04:35:09+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,quit,success
a1944e0965aadb9d000000000000000d,2024-01-20T04:35:17+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,ssh ***********,success
63547a7565aadd16000000000000000d,2024-01-20T04:35:43+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,cd /hbdata/mysql3613/,success
63547a7565aadd16000000000000000d,2024-01-20T04:35:44+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,ll,success
63547a7565aadd16000000000000000d,2024-01-20T04:35:49+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,cd logs/,success
63547a7565aadd16000000000000000d,2024-01-20T04:35:49+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,ll,success
63547a7565aadd16000000000000000d,2024-01-20T04:35:56+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,ll -h,success
63547a7565aadd16000000000000000d,2024-01-20T04:36:07+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,df -h,success
63547a7565aadd16000000000000000d,2024-01-20T04:37:13+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,tail 200 slow.log,success
63547a7565aadd16000000000000000d,2024-01-20T04:37:38+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,ll,success
63547a7565aadd16000000000000000d,2024-01-20T04:37:41+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,file slow.log,success
63547a7565aadd16000000000000000d,2024-01-20T04:37:59+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,tail -200 slow.log,success
63547a7565aadd16000000000000000d,2024-01-20T04:43:04+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,tail -1000 slow.log,success
63547a7565aadd16000000000000000d,2024-01-20T04:49:46+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,ll,success
63547a7565aadd16000000000000000d,2024-01-20T04:50:13+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,ll,success
63547a7565aadd16000000000000000d,2024-01-20T04:50:34+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,ll -h,success
63547a7565aadd16000000000000000d,2024-01-20T04:51:08+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,ll,success
63547a7565aadd16000000000000000d,2024-01-20T04:51:39+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,tail -100 slow.log,success
63547a7565aadd16000000000000000d,2024-01-20T04:52:07+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,head slow.log,success
63547a7565aadd16000000000000000d,2024-01-20T04:52:23+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,head 100 slow.log,success
63547a7565aadd16000000000000000d,2024-01-20T04:52:39+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,head -100 slow.log,success
63547a7565aadd16000000000000000d,2024-01-20T04:54:05+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,ll,success
63547a7565aadd16000000000000000d,2024-01-20T04:54:14+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,ll,success
63547a7565aadd16000000000000000d,2024-01-20T04:55:14+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,ll,success
63547a7565aadd16000000000000000d,2024-01-20T05:02:09+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,cat /etc/os-release,success
63547a7565aadd16000000000000000d,2024-01-20T05:06:56+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,cd /var/log/,success
63547a7565aadd16000000000000000d,2024-01-20T05:06:57+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,ll,success
63547a7565aadd16000000000000000d,2024-01-20T05:07:50+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,less sysl.1,success
63547a7565aadd16000000000000000d,2024-01-20T05:08:24+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,/Jan 19 19:,success
63547a7565aadd16000000000000000d,2024-01-20T05:08:46+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,Gqless syslog,success
63547a7565aadd16000000000000000d,2024-01-20T05:14:36+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,/Jan 19 19:40,success
cf1a34bf65aae790000000000000000d,2024-01-20T05:20:21+08:00,cmd.Command,avenir_user01,49.106.123.250,db-mysql-miniglobal-1c-1,***********,cd /var/log,success
cf1a34bf65aae790000000000000000d,2024-01-20T05:20:21+08:00,cmd.Command,avenir_user01,49.106.123.250,db-mysql-miniglobal-1c-1,***********,ll,success
cf1a34bf65aae790000000000000000d,2024-01-20T05:20:26+08:00,cmd.Command,avenir_user01,49.106.123.250,db-mysql-miniglobal-1c-1,***********,less syslog,success
cf1a34bf65aae790000000000000000d,2024-01-20T05:21:05+08:00,cmd.Command,avenir_user01,49.106.123.250,db-mysql-miniglobal-1c-1,***********,less syslog.1,success
cf1a34bf65aae790000000000000000d,2024-01-20T05:21:08+08:00,cmd.Command,avenir_user01,49.106.123.250,db-mysql-miniglobal-1c-1,***********,/ERROR [EC2Identity],success
cf1a34bf65aae790000000000000000d,2024-01-20T05:21:15+08:00,cmd.Command,avenir_user01,49.106.123.250,db-mysql-miniglobal-1c-1,***********,less syslog,success
cf1a34bf65aae790000000000000000d,2024-01-20T05:21:32+08:00,cmd.Command,avenir_user01,49.106.123.250,db-mysql-miniglobal-1c-1,***********,/Jan 19 20:,success
bd736c7065ab0af1000000000000000d,2024-01-20T07:51:25+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,mysql -u superdba -P 3613 -p -h master-3613.docsl.com,success
bd736c7065ab0af1000000000000000d,2024-01-20T07:51:43+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,databases;,success
bd736c7065ab0af1000000000000000d,2024-01-20T07:51:52+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,use huobi_uc,success
bd736c7065ab0af1000000000000000d,2024-01-20T07:52:03+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,show tables like '%login%';,success
bd736c7065ab0af1000000000000000d,2024-01-20T07:52:12+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,desc uc_login_history_v2;,success
bd736c7065ab0af1000000000000000d,2024-01-20T07:52:38+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,select * from uc_login_history_v2 order by id desc limit 100;,success
bd736c7065ab0af1000000000000000d,2024-01-20T07:52:49+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,select * from uc_login_history order by id desc limit 100;,success
cdad7ce765ab13c8000000000000000d,2024-01-20T08:29:06+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,cd /hbdata/mysql3613/logs/,success
cdad7ce765ab13c8000000000000000d,2024-01-20T08:29:06+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,ll,success
cdad7ce765ab13c8000000000000000d,2024-01-20T08:29:14+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,tail -200 slow.log,success
cdad7ce765ab13c8000000000000000d,2024-01-20T08:34:09+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,ll -h,success
cdad7ce765ab13c8000000000000000d,2024-01-20T08:34:37+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,less slow.log,success
cdad7ce765ab13c8000000000000000d,2024-01-20T08:36:02+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,tail -10000 slow.log > new_slow.txt,success
cdad7ce765ab13c8000000000000000d,2024-01-20T08:36:04+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,ll,success
cdad7ce765ab13c8000000000000000d,2024-01-20T08:36:07+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,less new_slow.txt,success
cdad7ce765ab13c8000000000000000d,2024-01-20T08:36:29+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,AAAAAAAq,success
cdad7ce765ab13c8000000000000000d,2024-01-20T08:36:50+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,2,success
cdad7ce765ab13c8000000000000000d,2024-01-20T08:36:54+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,less new_slow.txt,success
cdad7ce765ab13c8000000000000000d,2024-01-20T08:37:29+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,tail -1000000 slow.log > new_slow.txt,success
cdad7ce765ab13c8000000000000000d,2024-01-20T08:37:35+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,less new_slow.txt,success
bd736c7065ab0af1000000000000000d,2024-01-20T08:50:13+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,select gmt_created,user_id, state,client_ip from uc_login_history order by id desc limit 100;,success
bd736c7065ab0af1000000000000000d,2024-01-20T09:00:05+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,select gmt_created,user_id, state,client_ip from uc_login_history order by id desc limit 200;,success
bd736c7065ab0af1000000000000000d,2024-01-20T09:00:23+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,select gmt_created,user_id, state,client_ip from uc_login_history order by id desc limit 300;,success
bd736c7065ab0af1000000000000000d,2024-01-20T09:01:26+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,show create table uc_login_history;,success
bd736c7065ab0af1000000000000000d,2024-01-20T09:07:06+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,select gmt_created,user_id, state,client_ip from uc_login_history order by id desc limit 400;,success
cdad7ce765ab13c8000000000000000d,2024-01-20T09:10:38+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,ll,success
cdad7ce765ab13c8000000000000000d,2024-01-20T09:10:44+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,ll,success
cdad7ce765ab13c8000000000000000d,2024-01-20T09:10:53+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,tail -50 slow.log,success
cdad7ce765ab13c8000000000000000d,2024-01-20T09:11:35+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,man grep,success
cdad7ce765ab13c8000000000000000d,2024-01-20T09:12:36+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,jjjjjjjjjjjjjjjjq,success
cdad7ce765ab13c8000000000000000d,2024-01-20T09:12:58+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,"SET timestamp" ne-c 2,success
cdad7ce765ab13c8000000000000000d,2024-01-20T09:13:13+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,grep -c 2 "SET timestamp" new_slow.txt,success
cdad7ce765ab13c8000000000000000d,2024-01-20T09:13:26+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,grep -c=2 "SET timestamp" new_slow.txt,success
cdad7ce765ab13c8000000000000000d,2024-01-20T09:16:47+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,grep "SET timestamp" new_slow.txt |head -2,success
cdad7ce765ab13c8000000000000000d,2024-01-20T09:17:11+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,grep "SET timestamp" slow.log |head -2,success
9fca42e565ab200d0000000000000019,2024-01-20T09:21:25+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
9fca42e565ab200d0000000000000019,2024-01-20T09:21:48+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,use hbg_trade; ,success
9fca42e565ab200d0000000000000019,2024-01-20T09:22:41+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,select f_currency,sum(f_amount),success
9fca42e565ab200d0000000000000019,2024-01-20T09:22:41+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,from t_account_manage_fee_snapshot,success
9fca42e565ab200d0000000000000019,2024-01-20T09:22:41+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,where f_day in('********', '********', '********')  and f_state = 1 ,success
9fca42e565ab200d0000000000000019,2024-01-20T09:22:41+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,and f_currency in ('btc', 'eth','usdt','ht','shib','doge','xrp','eos','ltc','trx'),success
9fca42e565ab200d0000000000000019,2024-01-20T09:22:42+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,group by f_currency order by f_currency;,success
9fca42e565ab200d0000000000000019,2024-01-20T09:26:30+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,),success
9fca42e565ab200d0000000000000019,2024-01-20T09:26:30+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,select f_currency,sum(f_amount),success
9fca42e565ab200d0000000000000019,2024-01-20T09:26:30+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,where f_day in('********', '********', '********')  and f_state = 1 ,success
9fca42e565ab200d0000000000000019,2024-01-20T09:26:30+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,and f_currency in ('btc', 'usdt', 'eth', 'ht', 'hbpoint', 'shib', ,success
9fca42e565ab200d0000000000000019,2024-01-20T09:26:30+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,'doge', 'tfuel', 'xrp', 'eos', 'ltc', 'trx', 'husd', 'fil', 'ada', ,success
9fca42e565ab200d0000000000000019,2024-01-20T09:26:30+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,'etc', 'don', 'seele', 'iq', 'bcc', 'ong', 'elf', 'bsv', 'dot', 'cvnt', ,success
9fca42e565ab200d0000000000000019,2024-01-20T09:26:30+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,'beth', 'xec', 'link', 'iost', 'nest', 'sgb', 'btt',success
9fca42e565ab200d0000000000000019,2024-01-20T09:26:30+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,from t_account_manage_fee_snapshot,success
9fca42e565ab200d0000000000000019,2024-01-20T09:26:31+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,group by f_currency order by f_currency;,success
cdad7ce765ab13c8000000000000000d,2024-01-20T09:28:40+08:00,cmd.Command,avenir_user01,*************,db-mysql-miniglobal-1c-1,***********,top,success
bd736c7065ab0af1000000000000000d,2024-01-20T09:29:46+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,show processlist;,success
bd736c7065ab0af1000000000000000d,2024-01-20T09:32:29+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,show processlist limit 10;,success
bd736c7065ab0af1000000000000000d,2024-01-20T09:32:37+08:00,cmd.Command,avenir_user01,*************,miniglobal-devops,***********,show processlist;,success
9fca42e565ab200d0000000000000019,2024-01-20T10:21:31+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,use proamsdwdb;,success
9fca42e565ab200d0000000000000019,2024-01-20T10:21:49+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
9fca42e565ab200d0000000000000019,2024-01-20T10:21:49+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,from,success
9fca42e565ab200d0000000000000019,2024-01-20T10:21:49+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,(,success
9fca42e565ab200d0000000000000019,2024-01-20T10:21:49+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
9fca42e565ab200d0000000000000019,2024-01-20T10:21:49+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
9fca42e565ab200d0000000000000019,2024-01-20T10:21:50+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
9fca42e565ab200d0000000000000019,2024-01-20T10:21:50+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
9fca42e565ab200d0000000000000019,2024-01-20T10:21:50+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
9fca42e565ab200d0000000000000019,2024-01-20T10:21:50+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-01-18',success
9fca42e565ab200d0000000000000019,2024-01-20T10:21:50+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    and dw.f_state = 11,success
9fca42e565ab200d0000000000000019,2024-01-20T10:21:50+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    group by f_user_id,success
9fca42e565ab200d0000000000000019,2024-01-20T10:21:50+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,) user_amount,success
9fca42e565ab200d0000000000000019,2024-01-20T10:21:50+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,where amount >= 100;,success
9fca42e565ab200d0000000000000019,2024-01-20T10:22:34+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,0,success
9fca42e565ab200d0000000000000019,2024-01-20T10:23:03+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-01-18'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 10000;,success
9fca42e565ab200d0000000000000019,2024-01-20T10:23:34+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
9fca42e565ab200d0000000000000019,2024-01-20T10:23:34+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,from,success
9fca42e565ab200d0000000000000019,2024-01-20T10:23:34+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
9fca42e565ab200d0000000000000019,2024-01-20T10:23:34+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
9fca42e565ab200d0000000000000019,2024-01-20T10:23:34+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,(,success
9fca42e565ab200d0000000000000019,2024-01-20T10:23:35+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
9fca42e565ab200d0000000000000019,2024-01-20T10:23:35+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
9fca42e565ab200d0000000000000019,2024-01-20T10:23:35+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) > '2024-01-11' ,success
9fca42e565ab200d0000000000000019,2024-01-20T10:23:35+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-01-18',success
9fca42e565ab200d0000000000000019,2024-01-20T10:23:35+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    and dw.f_state = 11,success
9fca42e565ab200d0000000000000019,2024-01-20T10:23:35+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,    group by f_user_id,success
9fca42e565ab200d0000000000000019,2024-01-20T10:23:35+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,) user_amount,success
9fca42e565ab200d0000000000000019,2024-01-20T10:23:35+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,where amount >= 100;,success
9fca42e565ab200d0000000000000019,2024-01-20T10:27:16+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,exit,success
9fca42e565ab200d0000000000000019,2024-01-20T10:27:17+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,exit,success
644a4d6965abccc0000000000000000d,2024-01-20T21:39:06+08:00,cmd.Command,avenir_user01,49.106.131.151,miniglobal-deploy,172.28.3.27,cd /hbdata/soft/miniglobal-apps/apps/miniglobal/miniglobal-bitex-uc-gateway/,success
644a4d6965abccc0000000000000000d,2024-01-20T21:39:07+08:00,cmd.Command,avenir_user01,49.106.131.151,miniglobal-deploy,172.28.3.27,ll,success
644a4d6965abccc0000000000000000d,2024-01-20T21:41:11+08:00,cmd.Command,avenir_user01,49.106.131.151,miniglobal-deploy,172.28.3.27,make build TAG=PRD_T20240125,success
a3bf512065abd0b5000000000000000d,2024-01-20T21:55:41+08:00,cmd.Command,avenir_user01,49.106.131.151,db-mysql-miniglobal-1c-1,***********,cd /hbdata/mysql3613/logs/,success
a3bf512065abd0b5000000000000000d,2024-01-20T21:55:47+08:00,cmd.Command,avenir_user01,49.106.131.151,db-mysql-miniglobal-1c-1,***********,ll,success
a3bf512065abd0b5000000000000000d,2024-01-20T21:56:05+08:00,cmd.Command,avenir_user01,49.106.131.151,db-mysql-miniglobal-1c-1,***********,ll,success
a3bf512065abd0b5000000000000000d,2024-01-20T21:56:22+08:00,cmd.Command,avenir_user01,49.106.131.151,db-mysql-miniglobal-1c-1,***********,tail -50 slow.log,success
a3bf512065abd0b5000000000000000d,2024-01-20T21:56:53+08:00,cmd.Command,avenir_user01,49.106.131.151,db-mysql-miniglobal-1c-1,***********,tail -f slow.log,success
bb3e294265adc6850000000000000019,2024-01-22T09:36:16+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
bb3e294265adc6850000000000000019,2024-01-22T09:36:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,use proamsdwdb;,success
bb3e294265adc6850000000000000019,2024-01-22T09:37:00+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
bb3e294265adc6850000000000000019,2024-01-22T09:37:00+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where f_state in(3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
bb3e294265adc6850000000000000019,2024-01-22T09:37:00+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,group by f_currency;,success
bb3e294265adc6850000000000000019,2024-01-22T09:59:48+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,exit,success
bb3e294265adc6850000000000000019,2024-01-22T09:59:49+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,exit,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:24:22+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ssh 172.28.3.192,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:24:24+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,df -h,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:24:34+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,cd /hbdata/,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:24:36+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:24:44+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,cd logs/,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:24:45+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:24:49+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,cd nginx/,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:24:50+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:24:56+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:25:02+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,du -sh,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:25:29+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,tail -f access.json,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:25:40+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:25:43+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:26:01+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,df -sh,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:26:06+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,du -sh,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:26:09+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:26:22+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll -h access.json,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:26:26+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll -h access.json,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:26:38+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,tail -100 access.json,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:26:50+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:27:05+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,head -100 access.json,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:27:54+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,tail -10000 access.json | head -100,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:28:35+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:28:45+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,mv access.json.bak,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:29:24+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,mv  access.json access.json.20240123.bak,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:29:25+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:29:29+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:29:35+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,touch access.json,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:29:36+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:29:42+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:29:47+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:29:51+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:30:16+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:30:37+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,tail -10 error.log,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:32:07+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:32:33+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,cd /hbdata/,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:32:34+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:32:39+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,cd bin/,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:32:40+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:34:29+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,service nginx restart,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:36:12+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,cd -,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:36:21+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,cd logs/nginx/,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:36:21+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:36:39+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,rm access.json.20240123.bak,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:36:46+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,df -h,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:37:03+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,tail -10 access.json,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:37:31+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:37:52+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,tail -f error.log,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:38:17+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:38:20+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll -h,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:38:28+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,df -h,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:41:52+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:41:57+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll -h,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:42:15+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll -h,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:42:44+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll -h,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:43:15+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll -h,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:44:15+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll -h,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:49:52+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ssh 172.28.3.53,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:49:59+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,cd /hb,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:50:00+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:50:03+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,cd logs/nginx/,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:50:04+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:50:44+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,rm access.json; touch access.json,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:50:46+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:50:54+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,tail -f error.log,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:52:00+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:52:06+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,tail -f error.log,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:55:01+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:55:05+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,ll -h,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:55:15+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,tail -f access.json,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:56:34+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,df -h,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:56:49+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,du -sh,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:56:58+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,du -sh,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:57:16+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,du -sh,success
9e9cf20865af5b8d000000000000000d,2024-01-23T14:58:16+08:00,cmd.Command,avenir_user01,114.136.240.4,miniglobal-devops,***********,du -sh,success
66a357c165b70100000000000000000d,2024-01-29T09:39:27+08:00,file.Upload,avenir_user01,120.244.194.94,MiniAD04,172.28.2.251,=NG)U9]yT"42twvwwX_..txt,success
66a357c165b70100000000000000000d,2024-01-29T09:42:58+08:00,file.Upload,avenir_user01,120.244.194.94,MiniAD04,172.28.2.251,ocr.txt,success
66a357c165b70100000000000000000d,2024-01-29T09:42:58+08:00,file.UploadSave,avenir_user01,120.244.194.94,MiniAD04,172.28.2.251,keyboard.txt,success
611a09ac65b709bc0000000000000019,2024-01-29T10:13:28+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
611a09ac65b709bc0000000000000019,2024-01-29T10:13:42+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,use proamsdwdb;,success
611a09ac65b709bc0000000000000019,2024-01-29T10:13:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
611a09ac65b709bc0000000000000019,2024-01-29T10:13:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,where f_state in(3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
611a09ac65b709bc0000000000000019,2024-01-29T10:13:50+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,group by f_currency;,success
611a09ac65b709bc0000000000000019,2024-01-29T11:24:53+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,exit,success
611a09ac65b709bc0000000000000019,2024-01-29T11:24:55+08:00,cmd.Command,xiexinyu,59.148.189.233,miniglobal-devops,***********,exit,success
513c140f65b77f21000000000000000d,2024-01-29T18:34:18+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-deploy,172.28.3.27,cd /hbdata/soft/miniglobal-apps/apps/miniglobal/miniglobal-hbg-account-manage/,success
513c140f65b77f21000000000000000d,2024-01-29T18:34:19+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-deploy,172.28.3.27,ll,success
513c140f65b77f21000000000000000d,2024-01-29T18:34:39+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-deploy,172.28.3.27,make build TAG=Last login: Tue Jan 23 14:23:42 on ttys003,success
513c140f65b77f21000000000000000d,2024-01-29T18:35:05+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-deploy,172.28.3.27,make build TAG=PRD_T20240116,success
513c140f65b77f21000000000000000d,2024-01-29T23:22:13+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-deploy,172.28.3.27,ping oss-callback.ri16.com,success
513c140f65b77f21000000000000000d,2024-01-29T23:22:33+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-deploy,172.28.3.27,clear,success
513c140f65b77f21000000000000000d,2024-01-29T23:22:36+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-deploy,172.28.3.27,cd,success
db9dc54c65b7c2c4000000000000000d,2024-01-29T23:22:46+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,ping oss-callback.ri16.com,success
db9dc54c65b7c2c4000000000000000d,2024-01-29T23:23:25+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,dig oss-callback.ri16.com,success
db9dc54c65b7c2c4000000000000000d,2024-01-29T23:23:56+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,yum install dig,success
db9dc54c65b7c2c4000000000000000d,2024-01-29T23:25:12+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,yum install bind-utils,success
db9dc54c65b7c2c4000000000000000d,2024-01-29T23:25:17+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,y,success
db9dc54c65b7c2c4000000000000000d,2024-01-29T23:25:34+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,dig oss-callback.ri16.com,success
db9dc54c65b7c2c4000000000000000d,2024-01-29T23:32:50+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,dig oss-callback.ri16.com,success
f63e249465b852e7000000000000000d,2024-01-30T09:39:21+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,mysql -h 172.28.5.15 -p -P3614 -usuperdba,success
f63e249465b852e7000000000000000d,2024-01-30T09:39:51+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,use show daatabases like '%finance%';,success
f63e249465b852e7000000000000000d,2024-01-30T09:39:58+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,show databases like '%finance%';,success
f63e249465b852e7000000000000000d,2024-01-30T09:40:29+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,use finance_daily_report,success
f63e249465b852e7000000000000000d,2024-01-30T09:40:34+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,show tables;,success
f63e249465b852e7000000000000000d,2024-01-30T09:40:55+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,desc t_mini_agent_config;,success
f63e249465b852e7000000000000000d,2024-01-30T09:42:32+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,ALTER TABLE `t_mini_agent_config`  ADD `f_passphrase` varchar(200) NOT NULL DEFAULT '' COMMENT 'passphrase ' AFTER `f_api_secret_key`;,success
f63e249465b852e7000000000000000d,2024-01-30T09:42:36+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,desc t_mini_agent_config;,success
f63e249465b852e7000000000000000d,2024-01-30T09:43:31+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,quit,success
a7446ddd65b8544b000000000000000d,2024-01-30T09:43:46+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-deploy,172.28.3.27,cd /hbdata/soft/miniglobal-apps/apps/miniglobal/miniglobal-finance-daily-report/,success
a7446ddd65b8544b000000000000000d,2024-01-30T09:43:47+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-deploy,172.28.3.27,ll,success
a7446ddd65b8544b000000000000000d,2024-01-30T09:44:03+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-deploy,172.28.3.27,make build TAG=PRD_T20240130,success
a7446ddd65b8544b000000000000000d,2024-01-30T10:14:51+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-deploy,172.28.3.27,make build TAG=PRD_T20240130_1,success
a7446ddd65b8544b000000000000000d,2024-01-30T10:30:38+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-deploy,172.28.3.27,cd ../fe,success
a7446ddd65b8544b000000000000000d,2024-01-30T10:30:38+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-deploy,172.28.3.27,ll,success
a7446ddd65b8544b000000000000000d,2024-01-30T10:31:05+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-deploy,172.28.3.27,make build TAG=PRD_T401291,success
a7446ddd65b8544b000000000000000d,2024-01-30T11:04:10+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-deploy,172.28.3.27,cd -,success
a7446ddd65b8544b000000000000000d,2024-01-30T11:04:21+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-deploy,172.28.3.27,make build TAG=PRD_T20240130_2,success
669eb70a65b99dad0000000000000019,2024-01-31T09:09:07+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
669eb70a65b99dad0000000000000019,2024-01-31T09:09:22+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,use proamsdwdb;,success
669eb70a65b99dad0000000000000019,2024-01-31T09:09:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
669eb70a65b99dad0000000000000019,2024-01-31T09:09:33+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,where f_state in(3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
669eb70a65b99dad0000000000000019,2024-01-31T09:09:34+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,group by f_currency;,success
669eb70a65b99dad0000000000000019,2024-01-31T09:32:21+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,exit,success
669eb70a65b99dad0000000000000019,2024-01-31T09:32:23+08:00,cmd.Command,xiexinyu,219.77.42.44,miniglobal-devops,***********,exit,success
3ade52c765b9af90000000000000000d,2024-01-31T10:25:28+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-deploy,172.28.3.27,cd /hbdata/soft/miniglobal-apps/apps/miniglobal/fed-hbg-encn/,success
3ade52c765b9af90000000000000000d,2024-01-31T10:25:29+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-deploy,172.28.3.27,ll,success
3ade52c765b9af90000000000000000d,2024-01-31T10:25:36+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-deploy,172.28.3.27,make build TAG=PRD_T401301,success
b54ef29d65b9bd14000000000000000d,2024-01-31T11:23:10+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
b54ef29d65b9bd14000000000000000d,2024-01-31T11:51:20+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,ike '%pro%';,success
b54ef29d65b9bd14000000000000000d,2024-01-31T11:51:27+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,use proacctdb_1;,success
b54ef29d65b9bd14000000000000000d,2024-01-31T11:51:34+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,show tables;,success
b54ef29d65b9bd14000000000000000d,2024-01-31T11:51:41+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,desc t_subaccount;,success
b54ef29d65b9bd14000000000000000d,2024-01-31T11:51:54+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,show create table t_subaccount;,success
b54ef29d65b9bd14000000000000000d,2024-01-31T11:52:37+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,select * from t_subaccount where f_type =9 limit 3;,success
b54ef29d65b9bd14000000000000000d,2024-01-31T11:53:38+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,select count(distinct f_user_id) from t_subaccount where f_type =9;,success
b54ef29d65b9bd14000000000000000d,2024-01-31T11:54:27+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,select count(distinct f_user_id) from t_subaccount where f_type =9 and f_balance > 0;,success
b54ef29d65b9bd14000000000000000d,2024-01-31T11:55:25+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,select count(distinct f_user_id) from t_subaccount where f_type = 9 and f_balance > 0 and f_account_type = 2;,success
b54ef29d65b9bd14000000000000000d,2024-01-31T11:56:56+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,select f_currency,sum(f_balance)  from t_subaccount where f_type = 9 and f_balance > 0 and f_account_type = 2 group by f_currency ;,success
b54ef29d65b9bd14000000000000000d,2024-01-31T11:59:49+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,use proacctdb_2;,success
b54ef29d65b9bd14000000000000000d,2024-01-31T11:59:57+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,select count(distinct f_user_id) from t_subaccount where f_type = 9 and f_balance > 0 and f_account_type = 2;,success
b54ef29d65b9bd14000000000000000d,2024-01-31T12:00:24+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,use proacctdb_3;,success
b54ef29d65b9bd14000000000000000d,2024-01-31T12:00:27+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,select count(distinct f_user_id) from t_subaccount where f_type = 9 and f_balance > 0 and f_account_type = 2;,success
b54ef29d65b9bd14000000000000000d,2024-01-31T12:00:46+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,use proacctdb_4;,success
b54ef29d65b9bd14000000000000000d,2024-01-31T12:00:49+08:00,cmd.Command,avenir_user01,120.244.194.94,miniglobal-devops,***********,select count(distinct f_user_id) from t_subaccount where f_type = 9 and f_balance > 0 and f_account_type = 2;,success
7012cc5c65b9fc45000000000000000d,2024-01-31T15:52:38+08:00,cmd.Command,avenir_user01,223.104.40.164,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
7012cc5c65b9fc45000000000000000d,2024-01-31T15:52:58+08:00,cmd.Command,avenir_user01,223.104.40.164,miniglobal-devops,***********,show databases like '%kyc%';,success
7012cc5c65b9fc45000000000000000d,2024-01-31T15:53:08+08:00,cmd.Command,avenir_user01,223.104.40.164,miniglobal-devops,***********,use huobi_platform_kyc;,success
7012cc5c65b9fc45000000000000000d,2024-01-31T15:53:12+08:00,cmd.Command,avenir_user01,223.104.40.164,miniglobal-devops,***********,show tables;,success
7012cc5c65b9fc45000000000000000d,2024-01-31T15:54:04+08:00,cmd.Command,avenir_user01,223.104.40.164,miniglobal-devops,***********,desc t_user_kyc_item_record;,success
7012cc5c65b9fc45000000000000000d,2024-01-31T15:54:56+08:00,cmd.Command,avenir_user01,223.104.40.164,miniglobal-devops,***********,select * from t_user_kyc_item_record where f_uid=143876164 order by f_id asc limit 3;,success
7012cc5c65b9fc45000000000000000d,2024-01-31T15:55:05+08:00,cmd.Command,avenir_user01,223.104.40.164,miniglobal-devops,***********,select * from t_user_kyc_item_record where f_uid=143876164 order by f_id asc limit 3\G;,success
7012cc5c65b9fc45000000000000000d,2024-01-31T15:55:34+08:00,cmd.Command,avenir_user01,223.104.40.164,miniglobal-devops,***********,select * from t_user_kyc_item_record where f_uid=143876164 order by f_id asc limit 5\G;,success
7012cc5c65b9fc45000000000000000d,2024-01-31T16:02:09+08:00,cmd.Command,avenir_user01,223.104.40.164,miniglobal-devops,***********,3;,success
