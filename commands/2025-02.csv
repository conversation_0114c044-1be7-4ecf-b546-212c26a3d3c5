SessionID,EventTime,EventType,<PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,Result
746bb82067a02d760000000000000019,2025-02-03T10:44:12+08:00,cmd.<PERSON>,xiexinyu,182.239.93.71,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
746bb82067a02d760000000000000019,2025-02-03T10:44:28+08:00,cmd.<PERSON>,xiexinyu,182.239.93.71,miniglobal-devops,***********,use proamsdwdb;,success
746bb82067a02d760000000000000019,2025-02-03T10:44:40+08:00,cmd.Command,xiexinyu,182.239.93.71,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
746bb82067a02d760000000000000019,2025-02-03T10:44:40+08:00,cmd.<PERSON>,xiexinyu,182.239.93.71,miniglobal-devops,***********,where f_state in(4,3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
746bb82067a02d760000000000000019,2025-02-03T10:44:41+08:00,cmd.Command,xiexinyu,182.239.93.71,miniglobal-devops,***********,group by f_currency;,success
a7dcfaa167a2ba8f000000000000003f,2025-02-05T09:10:42+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
a7dcfaa167a2ba8f000000000000003f,2025-02-05T09:10:43+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
a7dcfaa167a2ba8f000000000000003f,2025-02-05T09:10:45+08:00,cmd.Command,dujiakai,**************,security-test,************,cd auto-scan-attack-surface/,success
a7dcfaa167a2ba8f000000000000003f,2025-02-05T09:10:46+08:00,cmd.Command,dujiakai,**************,security-test,************,sl,success
a7dcfaa167a2ba8f000000000000003f,2025-02-05T09:10:47+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
a7dcfaa167a2ba8f000000000000003f,2025-02-05T09:10:49+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
a7dcfaa167a2ba8f000000000000003f,2025-02-05T09:10:50+08:00,cmd.Command,dujiakai,**************,security-test,************,cd tmp-file/,success
a7dcfaa167a2ba8f000000000000003f,2025-02-05T09:10:50+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
a7dcfaa167a2ba8f000000000000003f,2025-02-05T09:10:52+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
a7dcfaa167a2ba8f000000000000003f,2025-02-05T09:11:01+08:00,cmd.Command,dujiakai,**************,security-test,************,vim diff.py ,success
a7dcfaa167a2ba8f000000000000003f,2025-02-05T09:11:13+08:00,cmd.Command,dujiakai,**************,security-test,************,python3 diff.py ,success
a7dcfaa167a2ba8f000000000000003f,2025-02-05T09:11:41+08:00,cmd.Command,dujiakai,**************,security-test,************,telnet 3.114.188.30 22,success
a7dcfaa167a2ba8f000000000000003f,2025-02-05T09:14:34+08:00,cmd.Command,dujiakai,**************,security-test,************,cd ..,success
a7dcfaa167a2ba8f000000000000003f,2025-02-05T09:14:34+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
a7dcfaa167a2ba8f000000000000003f,2025-02-05T09:14:45+08:00,cmd.Command,dujiakai,**************,security-test,************,grep -nr '3.114.188.30' org-assert/,success
a7dcfaa167a2ba8f000000000000003f,2025-02-05T11:28:35+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
a7dcfaa167a2ba8f000000000000003f,2025-02-05T11:35:56+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
a7dcfaa167a2ba8f000000000000003f,2025-02-05T11:35:57+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
a7dcfaa167a2ba8f000000000000003f,2025-02-05T11:38:47+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
a7dcfaa167a2ba8f000000000000003f,2025-02-05T11:38:49+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
a7dcfaa167a2ba8f000000000000003f,2025-02-05T11:38:50+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
a7dcfaa167a2ba8f000000000000003f,2025-02-05T11:59:34+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
a7dcfaa167a2ba8f000000000000003f,2025-02-05T11:59:40+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c37cb04a67a3011c0000000000000039,2025-02-05T14:11:58+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,mysql -h jp-am-prod-rds.cn2i2i0oifx6.ap-northeast-1.rds.amazonaws.com  -u superdba -p -e "SHOW GLOBAL VARIABLES" >/tmp/var.log,success
c37cb04a67a3011c0000000000000039,2025-02-05T14:12:15+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,cat /tmp/var.log,success
f2cdd1bb67a306a7000000000000001e,2025-02-05T14:36:12+08:00,cmd.Command,liulonglong,*************,yigeyun-vpn-node-sz-01,************,systemctl status connector_client,success
f2cdd1bb67a306a7000000000000001e,2025-02-05T14:52:55+08:00,cmd.Command,liulonglong,*************,yigeyun-vpn-node-sz-01,************,ping 120.24.234.157,success
f2cdd1bb67a306a7000000000000001e,2025-02-05T15:08:09+08:00,cmd.Command,liulonglong,*************,yigeyun-vpn-node-sz-01,************,exit,success
421c1b3367a30ee50000000000000042,2025-02-05T15:11:39+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd /data/nexvault-data-report/,success
421c1b3367a30ee50000000000000042,2025-02-05T15:11:40+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,ls,success
421c1b3367a30ee50000000000000042,2025-02-05T15:12:08+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim .env ,success
c37cb04a67a3011c0000000000000039,2025-02-05T15:13:21+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,mysql -h jp-am-prod-rds.cn2i2i0oifx6.ap-northeast-1.rds.amazonaws.com  -u superdba -p ,success
c37cb04a67a3011c0000000000000039,2025-02-05T15:13:30+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,show databases;,success
c37cb04a67a3011c0000000000000039,2025-02-05T15:13:41+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,use `trading-strategy`;,success
c37cb04a67a3011c0000000000000039,2025-02-05T15:13:44+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,show tables;,success
c37cb04a67a3011c0000000000000039,2025-02-05T15:14:46+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,show create table t_sfa_order_coinfuture;,success
c37cb04a67a3011c0000000000000039,2025-02-05T15:15:06+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,select ,success
c37cb04a67a3011c0000000000000039,2025-02-05T15:16:18+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,-- 替换为你要查询的数据库名称,success
c37cb04a67a3011c0000000000000039,2025-02-05T15:16:18+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,SELECT ,success
c37cb04a67a3011c0000000000000039,2025-02-05T15:16:18+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,    TABLE_NAME, ,success
c37cb04a67a3011c0000000000000039,2025-02-05T15:16:18+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,    COLUMN_NAME,success
c37cb04a67a3011c0000000000000039,2025-02-05T15:16:18+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,FROM ,success
c37cb04a67a3011c0000000000000039,2025-02-05T15:16:18+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,    INFORMATION_SCHEMA.COLUMNS,success
c37cb04a67a3011c0000000000000039,2025-02-05T15:16:18+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,WHERE ,success
c37cb04a67a3011c0000000000000039,2025-02-05T15:16:18+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,    TABLE_SCHEMA = 'your_database_name',success
c37cb04a67a3011c0000000000000039,2025-02-05T15:16:18+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,    AND DATA_TYPE = 'timestamp';,success
c37cb04a67a3011c0000000000000039,2025-02-05T15:16:33+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,SELECT      TABLE_NAME,      COLUMN_NAME FROM      INFORMATION_SCHEMA.COLUMNS WHERE      TABLE_SCHEMA = 'trading-strategy'     AND DATA_TYPE = 'timestamp';,success
c37cb04a67a3011c0000000000000039,2025-02-05T15:16:39+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,SELECT      TABLE_NAME,      COLUMN_NAME FROM      INFORMATION_SCHEMA.COLUMNS WHERE      TABLE_SCHEMA = 'trading-strategy' ；,success
c37cb04a67a3011c0000000000000039,2025-02-05T15:16:42+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,SELECT      TABLE_NAME,      COLUMN_NAME FROM      INFORMATION_SCHEMA.COLUMNS WHERE      TABLE_SCHEMA = 'trading-strategy' ;,success
421c1b3367a30ee50000000000000042,2025-02-05T15:25:05+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git status,success
421c1b3367a30ee50000000000000042,2025-02-05T15:25:15+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git pull,success
421c1b3367a30ee50000000000000042,2025-02-05T15:25:23+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git diff report/brief_market_target.py,success
541c25e867a312eb0000000000000019,2025-02-05T15:27:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
541c25e867a312eb0000000000000019,2025-02-05T15:28:01+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use proamsdwdb;,success
541c25e867a312eb0000000000000019,2025-02-05T15:28:10+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_state in(4,3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
541c25e867a312eb0000000000000019,2025-02-05T15:28:10+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
541c25e867a312eb0000000000000019,2025-02-05T15:28:11+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,group by f_currency;,success
421c1b3367a30ee50000000000000042,2025-02-05T15:31:24+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
421c1b3367a30ee50000000000000042,2025-02-05T15:32:07+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/brief_market_report_data.sh,success
421c1b3367a30ee50000000000000042,2025-02-05T15:32:49+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/cmc_currency_info.sh,success
f60efe8a67a3143a0000000000000042,2025-02-05T15:33:21+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd /data/data-spider/,success
f60efe8a67a3143a0000000000000042,2025-02-05T15:33:46+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/cmc_currency_info.sh,success
421c1b3367a30ee50000000000000042,2025-02-05T15:34:01+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/brief_market_report_data.sh,success
421c1b3367a30ee50000000000000042,2025-02-05T15:34:32+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -e,success
421c1b3367a30ee50000000000000042,2025-02-05T15:34:53+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
421c1b3367a30ee50000000000000042,2025-02-05T15:35:09+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim .env ,success
421c1b3367a30ee50000000000000042,2025-02-05T15:35:49+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/brief_market_report.sh,success
541c25e867a312eb0000000000000019,2025-02-05T16:01:34+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
541c25e867a312eb0000000000000019,2025-02-05T16:01:36+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:04:17+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,SELECT routine_name,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:04:19+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,FROM information_schema.routines;,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:04:28+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,SELECT routine_name,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:04:45+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,FROM information_schema.routines where routiine_schema='trading-strategy';,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:05:00+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,SELECT routine_name FROM information_schema.routines where routine_schema='trading-strategy';,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:05:02+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,SELECT routine_name FROM information_schema.routines where routine_schema='trading-strategy';,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:05:17+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,SELECT trigger_name,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:05:24+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,SELECT trigger_nameFROM information_schema.triggers;,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:05:44+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,SELECT trigger_name FROM information_schema.triggers t where t.evnet_object_schema='trading-strategy';,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:05:57+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,SELECT trigger_name FROM information_schema.triggers t where t.event_object_schema='trading-strategy';,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:06:06+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,WHERE event_schema = '您的数据库名';,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:06:06+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,SELECT event_name,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:06:06+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,FROM information_schema.events,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:06:15+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,SELECT event_name FROM information_schema.events WHERE event_schema = 'trading-strategy';,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:07:03+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,    REFERENCED_TABLE_NAME IS NOT NULL;,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:07:03+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,    TABLE_NAME AS '表名',,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:07:03+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,    CONSTRAINT_NAME AS '外键约束名',,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:07:03+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,    COLUMN_NAME AS '外键列名',,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:07:03+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,    REFERENCED_TABLE_NAME AS '引用的表名',,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:07:03+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,    REFERENCED_COLUMN_NAME AS '引用的列名',,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:07:03+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,    DELETE_RULE AS '删除规则',,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:07:03+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,SELECT,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:07:03+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,FROM,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:07:03+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,    INFORMATION_SCHEMA.KEY_COLUMN_USAGE,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:07:03+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,WHERE,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:07:03+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,    REFERENCED_TABLE_SCHEMA = '您的数据库名' AND,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:07:03+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,    UPDATE_RULE AS '更新规则',success
c37cb04a67a3011c0000000000000039,2025-02-05T16:07:40+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,select * from INFORMATION_SCHEMA.KEY_COLUMN_USAGE where REFERENCED_TABLE_SCHEMA = 'trading-strategy';,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:07:54+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,select * from INFORMATION_SCHEMA.KEY_COLUMN_USAGE,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:07:56+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,;,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:08:14+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,select * from INFORMATION_SCHEMA.KEY_COLUMN_USAGE where REFERENCED_TABLE_SCHEMA = 'trading-strategy';,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:08:26+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,SELECT event_name FROM information_schema.events WHERE event_schema = 'trading-strategy';,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:08:57+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,SELECT trigger_name FROM information_schema.triggers t where t.event_object_schema='trading-strategy';,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:09:10+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,SELECT routine_name FROM information_schema.routines where routine_schema='trading-strategy';,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:11:28+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,SHOW TRIGGERS FROM `trading-strategy`;,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:11:37+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,SHOW EVENTS FROM `trading-strategy`;,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:11:45+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,SHOW PROCEDURE STATUS WHERE Db = 'trading-strategy';,success
c37cb04a67a3011c0000000000000039,2025-02-05T16:11:45+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,SHOW FUNCTION STATUS WHERE Db = 'trading-strategy';,success
812ec05c67a325510000000000000042,2025-02-05T16:46:15+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd /data/nexvault-data-report/,success
812ec05c67a325510000000000000042,2025-02-05T16:46:18+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
812ec05c67a325510000000000000042,2025-02-05T16:46:34+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/brief_market_report_data.sh,success
812ec05c67a325510000000000000042,2025-02-05T16:47:07+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim .env ,success
812ec05c67a325510000000000000042,2025-02-05T16:47:41+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/brief_market_report.sh,success
6a2ca0b367a325ed000000000000003a,2025-02-05T16:48:57+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cd /var/log/zabbix/,success
6a2ca0b367a325ed000000000000003a,2025-02-05T16:48:58+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ls,success
6a2ca0b367a325ed000000000000003a,2025-02-05T16:49:04+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,more zabbix_server.log,success
812ec05c67a325510000000000000042,2025-02-05T16:59:48+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/brief_market_report_data.sh,success
812ec05c67a325510000000000000042,2025-02-05T17:03:07+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/brief_market_report_data.sh,success
812ec05c67a325510000000000000042,2025-02-05T17:20:24+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git pull,success
812ec05c67a325510000000000000042,2025-02-05T17:20:56+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git diff report/brief_market_target.py,success
812ec05c67a325510000000000000042,2025-02-05T17:21:05+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git checkout report/brief_market_target.py,success
812ec05c67a325510000000000000042,2025-02-05T17:21:09+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git pull,success
812ec05c67a325510000000000000042,2025-02-05T17:21:23+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim report/brief_market_target.py ,success
812ec05c67a325510000000000000042,2025-02-05T17:21:29+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim report/brief_market_target.py ,success
812ec05c67a325510000000000000042,2025-02-05T17:21:40+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/brief_market_report_data.sh,success
812ec05c67a325510000000000000042,2025-02-05T17:22:00+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/brief_market_report.sh,success
812ec05c67a325510000000000000042,2025-02-05T17:39:51+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
812ec05c67a325510000000000000042,2025-02-05T17:41:11+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -e,success
812ec05c67a325510000000000000042,2025-02-05T17:41:46+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
812ec05c67a325510000000000000042,2025-02-05T17:41:59+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd /data/data-spider; bash scripts/cmc_global_metrics.sh,success
812ec05c67a325510000000000000042,2025-02-05T17:51:29+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd ../nexvault-data-report/,success
812ec05c67a325510000000000000042,2025-02-05T17:55:50+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git pull,success
812ec05c67a325510000000000000042,2025-02-05T17:56:00+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git checkout report/brief_market_target.py,success
812ec05c67a325510000000000000042,2025-02-05T17:56:02+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git pull,success
812ec05c67a325510000000000000042,2025-02-05T17:56:15+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim report/brief_market_target.py,success
812ec05c67a325510000000000000042,2025-02-05T17:56:28+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/brief_market_report_data.sh,success
812ec05c67a325510000000000000042,2025-02-05T17:56:43+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/brief_market_report.sh,success
1538389b67a33ce40000000000000042,2025-02-05T18:26:59+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
1538389b67a33ce40000000000000042,2025-02-05T18:28:45+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -e,success
1538389b67a33ce40000000000000042,2025-02-05T18:31:18+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -e,success
1538389b67a33ce40000000000000042,2025-02-05T18:31:58+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
1538389b67a33ce40000000000000042,2025-02-05T18:32:08+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd /data/data-spider/,success
1538389b67a33ce40000000000000042,2025-02-05T18:32:15+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim scripts/brief_market_info_v2.sh,success
1538389b67a33ce40000000000000042,2025-02-05T18:38:47+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -e,success
1538389b67a33ce40000000000000042,2025-02-05T18:39:20+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
e879f8d867a426a30000000000000042,2025-02-06T11:04:39+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
f8e369d867a428cb0000000000000042,2025-02-06T11:13:36+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
f8e369d867a428cb0000000000000042,2025-02-06T11:15:56+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd /data/data-spider/,success
f8e369d867a428cb0000000000000042,2025-02-06T11:15:58+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim scripts/brief_market_info_v2.sh,success
f8e369d867a428cb0000000000000042,2025-02-06T11:16:25+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim .env ,success
16a00d0167a44c540000000000000042,2025-02-06T13:45:03+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
16a00d0167a44c540000000000000042,2025-02-06T13:45:15+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd /data/data-spider/,success
16a00d0167a44c540000000000000042,2025-02-06T13:45:25+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim scripts/brief_market_info_v2.sh ,success
16a00d0167a44c540000000000000042,2025-02-06T13:46:22+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim .env ,success
16a00d0167a44c540000000000000042,2025-02-06T13:46:46+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,/data/venv/bin/python manager.py task  --task=get_reserve_balances,success
16a00d0167a44c540000000000000042,2025-02-06T13:49:59+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim spiders/workers/spider_page.py ,success
16a00d0167a44c540000000000000042,2025-02-06T13:50:48+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,/data/venv/bin/python manager.py task  --task=get_reserve_balances,success
16a00d0167a44c540000000000000042,2025-02-06T13:51:25+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim spiders/workers/spider_page.py ,success
16a00d0167a44c540000000000000042,2025-02-06T13:56:47+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,/data/venv/bin/python manager.py task  --task=get_reserve_balances,success
16a00d0167a44c540000000000000042,2025-02-06T13:57:06+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim spiders/workers/spider_page.py ,success
16a00d0167a44c540000000000000042,2025-02-06T13:59:26+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,/data/venv/bin/python manager.py task  --task=get_reserve_balances,success
16a00d0167a44c540000000000000042,2025-02-06T13:59:54+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim spiders/workers/spider_page.py ,success
16a00d0167a44c540000000000000042,2025-02-06T14:00:09+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,/data/venv/bin/python manager.py task  --task=get_reserve_balances,success
16a00d0167a44c540000000000000042,2025-02-06T14:00:14+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim spiders/workers/spider_page.py ,success
16a00d0167a44c540000000000000042,2025-02-06T14:00:32+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,/data/venv/bin/python manager.py task  --task=get_reserve_balances,success
16a00d0167a44c540000000000000042,2025-02-06T14:05:05+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git status,success
16a00d0167a44c540000000000000042,2025-02-06T14:05:14+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git checkout spiders/workers/spider_page.py,success
16a00d0167a44c540000000000000042,2025-02-06T14:07:14+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git branch,success
16a00d0167a44c540000000000000042,2025-02-06T14:07:16+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git pull,success
16a00d0167a44c540000000000000042,2025-02-06T14:07:24+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,/data/venv/bin/python manager.py task  --task=get_reserve_balances,success
16a00d0167a44c540000000000000042,2025-02-06T14:37:21+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd ..,success
16a00d0167a44c540000000000000042,2025-02-06T14:37:34+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd nexvault-data-report/,success
16a00d0167a44c540000000000000042,2025-02-06T14:37:37+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git branch,success
16a00d0167a44c540000000000000042,2025-02-06T14:37:39+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git pull,success
16a00d0167a44c540000000000000042,2025-02-06T14:37:49+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim .env ,success
16a00d0167a44c540000000000000042,2025-02-06T14:38:34+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
16a00d0167a44c540000000000000042,2025-02-06T14:39:15+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/bitcoin_sentiment_report_v2.sh,success
16a00d0167a44c540000000000000042,2025-02-06T15:21:12+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git branch,success
16a00d0167a44c540000000000000042,2025-02-06T15:21:15+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git status,success
16a00d0167a44c540000000000000042,2025-02-06T15:21:28+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git diff report/brief_market_target.py,success
16a00d0167a44c540000000000000042,2025-02-06T15:21:37+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git checkout report/brief_market_target.py,success
16a00d0167a44c540000000000000042,2025-02-06T15:21:40+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git pull,success
16a00d0167a44c540000000000000042,2025-02-06T15:21:54+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim report/brief_market_target.py,success
16a00d0167a44c540000000000000042,2025-02-06T15:23:26+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
16a00d0167a44c540000000000000042,2025-02-06T15:23:38+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/brief_market_report_data.sh,success
408d4c2267a46b920000000000000039,2025-02-06T15:58:11+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,mysql -h jp-am-prod-rds.cn2i2i0oifx6.ap-northeast-1.rds.amazonaws.com  -u superdba -p ,success
408d4c2267a46b920000000000000039,2025-02-06T15:58:43+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,show global variables like '%coll%';,success
408d4c2267a46b920000000000000039,2025-02-06T15:59:56+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,show global variables like '%utf8mb4_general_ci%';,success
408d4c2267a46b920000000000000039,2025-02-06T16:00:04+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,show global variables like '%collation%';,success
408d4c2267a46b920000000000000039,2025-02-06T16:00:06+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,exit,success
408d4c2267a46b920000000000000039,2025-02-06T16:00:07+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,mysql -h jp-am-prod-rds.cn2i2i0oifx6.ap-northeast-1.rds.amazonaws.com  -u superdba -p ,success
408d4c2267a46b920000000000000039,2025-02-06T16:00:39+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,show databases;,success
408d4c2267a46b920000000000000039,2025-02-06T16:00:52+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,show create database `trading-strategy`;,success
81c29ad067a46ca90000000000000013,2025-02-06T16:02:52+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,screen -ls,success
81c29ad067a46ca90000000000000013,2025-02-06T16:02:55+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,screen -Dr f ,success
81c29ad067a46ca90000000000000013,2025-02-06T16:03:25+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,docker logs -f forensic_etl --tail 100,success
408d4c2267a46b920000000000000039,2025-02-06T16:03:47+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,show global variables like '%collation%';,success
408d4c2267a46b920000000000000039,2025-02-06T16:05:50+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,show global variables like '%collation%';,success
408d4c2267a46b920000000000000039,2025-02-06T16:05:51+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,show global variables like '%collation%';,success
408d4c2267a46b920000000000000039,2025-02-06T16:05:53+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,show global variables like '%collation%';,success
408d4c2267a46b920000000000000039,2025-02-06T16:05:57+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,show create database `trading-strategy`;,success
16a00d0167a44c540000000000000042,2025-02-06T16:07:10+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git status,success
16a00d0167a44c540000000000000042,2025-02-06T16:07:20+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git checkout report/brief_market_target.py,success
16a00d0167a44c540000000000000042,2025-02-06T16:07:23+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git pull,success
16a00d0167a44c540000000000000042,2025-02-06T16:07:34+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim report/brief_market_target.py,success
408d4c2267a46b920000000000000039,2025-02-06T16:07:37+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,show global variables like '%collation%';,success
408d4c2267a46b920000000000000039,2025-02-06T16:07:41+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,show create database `trading-strategy`;,success
16a00d0167a44c540000000000000042,2025-02-06T16:07:45+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
16a00d0167a44c540000000000000042,2025-02-06T16:08:01+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/brief_market_report_data.sh,success
16a00d0167a44c540000000000000042,2025-02-06T16:08:38+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/brief_market_report_data.sh,success
408d4c2267a46b920000000000000039,2025-02-06T16:13:03+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,use trading-strategy;,success
408d4c2267a46b920000000000000039,2025-02-06T16:13:10+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,show tables;,success
408d4c2267a46b920000000000000039,2025-02-06T16:13:17+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,show create table t_sfa_order_coinfuture;,success
16a00d0167a44c540000000000000042,2025-02-06T16:13:24+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/brief_market_report_data.sh,success
408d4c2267a46b920000000000000039,2025-02-06T16:14:10+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,show create table t_sfa_trans_account_asset;,success
408d4c2267a46b920000000000000039,2025-02-06T16:18:22+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,exit,success
408d4c2267a46b920000000000000039,2025-02-06T16:19:51+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,source_port=3306,success
408d4c2267a46b920000000000000039,2025-02-06T16:19:51+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,source_user=superdba,success
408d4c2267a46b920000000000000039,2025-02-06T16:20:12+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,more /tmp/trading_strategy_bigtable_ddl.sql,success
408d4c2267a46b920000000000000039,2025-02-06T16:20:53+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,mysqldump -h $source_host -u $source_user -p --default-character-set=utf8mb4 --skip-add-drop-table --set-gtid-purged=OFF --single-transaction --no-data trading-strategy "t_sfa_order_coinfuture" "t_sfa_order_spot" >/tmp/trading_strategy_bigtable_ddl.sql,success
408d4c2267a46b920000000000000039,2025-02-06T16:20:54+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,mysqldump -h $source_host -u $source_user -p --default-character-set=utf8mb4 --skip-add-drop-table --set-gtid-purged=OFF --single-transaction --no-data trading-strategy "t_sfa_order_coinfuture" "t_sfa_order_spot" >/tmp/trading_strategy_bigtable_ddl.sql,success
408d4c2267a46b920000000000000039,2025-02-06T16:21:04+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,more /tmp/trading_strategy_bigtable_ddl.sql,success
408d4c2267a46b920000000000000039,2025-02-06T16:21:34+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,mysqldump --help,success
408d4c2267a46b920000000000000039,2025-02-06T16:21:45+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,mysqldump --help|grep -i coll,success
408d4c2267a46b920000000000000039,2025-02-06T16:21:51+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,mysqldump --help|grep -i col,success
408d4c2267a46b920000000000000039,2025-02-06T16:24:27+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,mysqldump -h $source_host -u $source_user -p --default-character-set=utf8mb4 --skip-add-drop-table --set-gtid-purged=OFF --single-transaction --no-data trading-strategy >/tmp/ddl.sql,success
408d4c2267a46b920000000000000039,2025-02-06T16:24:44+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,more /tmp/ddl.sql,success
16a00d0167a44c540000000000000042,2025-02-06T16:25:19+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git checkout report/brief_market_target.py,success
16a00d0167a44c540000000000000042,2025-02-06T16:25:22+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git pull,success
16a00d0167a44c540000000000000042,2025-02-06T16:25:27+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim report/brief_market_target.py,success
16a00d0167a44c540000000000000042,2025-02-06T16:25:42+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/brief_market_report_data.sh,success
408d4c2267a46b920000000000000039,2025-02-06T16:26:05+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,  mysqldump --help|grep -i COLLATE,success
16a00d0167a44c540000000000000042,2025-02-06T16:28:55+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git checkout report/brief_market_target.py,success
16a00d0167a44c540000000000000042,2025-02-06T16:28:57+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git pull,success
16a00d0167a44c540000000000000042,2025-02-06T16:29:04+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim report/brief_market_target.py,success
16a00d0167a44c540000000000000042,2025-02-06T16:29:18+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim report/brief_market_target.py,success
16a00d0167a44c540000000000000042,2025-02-06T16:29:24+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/brief_market_report_data.sh,success
408d4c2267a46b920000000000000039,2025-02-06T16:29:29+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,> /tmp/trading_strategy.sql,success
408d4c2267a46b920000000000000039,2025-02-06T16:29:29+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,--log-error=/tmp/error_trading_strategy.sql \,success
408d4c2267a46b920000000000000039,2025-02-06T16:29:29+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,--skip-add-drop-table \,success
408d4c2267a46b920000000000000039,2025-02-06T16:29:29+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,--set-gtid-purged=OFF \,success
408d4c2267a46b920000000000000039,2025-02-06T16:29:29+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,--single-transaction \,success
408d4c2267a46b920000000000000039,2025-02-06T16:29:29+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,--triggers --routines --events \,success
408d4c2267a46b920000000000000039,2025-02-06T16:29:29+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,--default-character-set=utf8mb4 \,success
408d4c2267a46b920000000000000039,2025-02-06T16:29:29+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,--tz-utc \,success
408d4c2267a46b920000000000000039,2025-02-06T16:29:29+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,-u $source_user  \,success
408d4c2267a46b920000000000000039,2025-02-06T16:29:29+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,-v \,success
408d4c2267a46b920000000000000039,2025-02-06T16:29:29+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,-h $source_host \,success
408d4c2267a46b920000000000000039,2025-02-06T16:29:29+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,-P $source_port  \,success
408d4c2267a46b920000000000000039,2025-02-06T16:29:29+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,--no-data t_sfa_order_coinfuture t_sfa_order_spot \,success
408d4c2267a46b920000000000000039,2025-02-06T16:29:29+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,--ignore-table=trading-strategy.t_sfa_order_coinfuture \,success
408d4c2267a46b920000000000000039,2025-02-06T16:29:29+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,--ignore-table=trading-strategy.t_sfa_order_spot \,success
408d4c2267a46b920000000000000039,2025-02-06T16:29:29+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,-p \,success
408d4c2267a46b920000000000000039,2025-02-06T16:30:19+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,-v \,success
408d4c2267a46b920000000000000039,2025-02-06T16:30:19+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,--log-error=/tmp/error_trading_strategy.sql \,success
408d4c2267a46b920000000000000039,2025-02-06T16:30:19+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,--skip-add-drop-table \,success
408d4c2267a46b920000000000000039,2025-02-06T16:30:19+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,--set-gtid-purged=OFF \,success
408d4c2267a46b920000000000000039,2025-02-06T16:30:19+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,--single-transaction \,success
408d4c2267a46b920000000000000039,2025-02-06T16:30:19+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,--triggers --routines --events \,success
408d4c2267a46b920000000000000039,2025-02-06T16:30:19+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,--default-character-set=utf8mb4 \,success
408d4c2267a46b920000000000000039,2025-02-06T16:30:19+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,-u $source_user  \,success
408d4c2267a46b920000000000000039,2025-02-06T16:30:19+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,-p \,success
408d4c2267a46b920000000000000039,2025-02-06T16:30:19+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,-h $source_host \,success
408d4c2267a46b920000000000000039,2025-02-06T16:30:19+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,-P $source_port  \,success
408d4c2267a46b920000000000000039,2025-02-06T16:30:19+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,> /tmp/trading_strategy.sql,success
408d4c2267a46b920000000000000039,2025-02-06T16:30:19+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,--ignore-table=trading-strategy.t_sfa_order_coinfuture \,success
408d4c2267a46b920000000000000039,2025-02-06T16:30:19+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,--ignore-table=trading-strategy.t_sfa_order_spot \,success
408d4c2267a46b920000000000000039,2025-02-06T16:30:19+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,--no-data t_sfa_order_coinfuture t_sfa_order_spot \,success
408d4c2267a46b920000000000000039,2025-02-06T16:30:27+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,mysqldump trading-strategy -v --log-error=/tmp/error_trading_strategy.sql --skip-add-drop-table --set-gtid-purged=OFF --single-transaction --triggers --routines --events --default-character-set=utf8mb4 -u $source_user  -p -h $source_host -P $source_port  --no-data t_sfa_order_coinfuture t_sfa_order_spot --ignore-table=trading-strategy.t_sfa_order_coinfuture --ignore-table=trading-strategy.t_sfa_order_spot > /tmp/trading_strategy.sql,success
5395fae167a4732b0000000000000013,2025-02-06T16:30:40+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,screen -Dr f,success
408d4c2267a46b920000000000000039,2025-02-06T16:30:54+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,more /tmp/trading_strategy.sql,success
408d4c2267a46b920000000000000039,2025-02-06T16:33:07+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,mysqldump -h $source_host -u $source_user -p --default-character-set=utf8mb4 --triggers --routines --events --skip-add-drop-table --set-gtid-purged=OFF --single-transaction  trading-strategy "t_sfa_trans_account_asset" "t_sfa_trans_strategy_close_auto_order" "t_sfa_trans_strategy_extreme_close_auto_order" "t_sfa_trans_strategy_open_auto_order" "t_sfa_trans_strategy_param_config" > /tmp/trading_strategy_data.sql,success
408d4c2267a46b920000000000000039,2025-02-06T16:34:11+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,more /tmp/trading_strategy_data.sql,success
408d4c2267a46b920000000000000039,2025-02-06T16:34:20+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,mysqldump -h $source_host -u $source_user -p --default-character-set=utf8mb4 --skip-add-drop-table --set-gtid-purged=OFF --single-transaction  trading-strategy "t_sfa_trans_account_asset" "t_sfa_trans_strategy_close_auto_order" "t_sfa_trans_strategy_extreme_close_auto_order" "t_sfa_trans_strategy_open_auto_order" "t_sfa_trans_strategy_param_config" > /tmp/trading_strategy_data.sql,success
408d4c2267a46b920000000000000039,2025-02-06T16:34:40+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,more /tmp/trading_strategy_data.sql,success
408d4c2267a46b920000000000000039,2025-02-06T16:37:26+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,more /tmp/trading_strategy_data.sql,success
16a00d0167a44c540000000000000042,2025-02-06T16:50:00+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
16a00d0167a44c540000000000000042,2025-02-06T16:59:03+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/brief_market_report_data.sh,success
16a00d0167a44c540000000000000042,2025-02-06T16:59:16+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/brief_market_report.sh,success
408d4c2267a46b920000000000000039,2025-02-06T17:12:48+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,more /tmp/trading_strategy_data.sql|grep COLLATE utf8mb4_0900_ai_ci,success
408d4c2267a46b920000000000000039,2025-02-06T17:12:53+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,more /tmp/trading_strategy_data.sql|grep COLLATE utf8mb4_0900_ai_ci,success
408d4c2267a46b920000000000000039,2025-02-06T17:12:58+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,more /tmp/trading_strategy_data.sql,success
408d4c2267a46b920000000000000039,2025-02-06T17:13:05+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,grep "COLLATE utf8mb4_0900_ai_ci" /tmp/trading_strategy_data.sql,success
14c7022967a483f3000000000000003a,2025-02-06T17:42:19+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ping **************,success
14c7022967a483f3000000000000003a,2025-02-06T17:42:53+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ping 10.175.252.1,success
524a00b267a486d0000000000000003a,2025-02-06T17:54:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '.';cd '.';cd '.';pwd;ls -la,success
14c7022967a483f3000000000000003a,2025-02-06T17:54:27+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ping **************,success
16a00d0167a44c540000000000000042,2025-02-06T18:06:08+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l|grep market_kline_day,success
16a00d0167a44c540000000000000042,2025-02-06T18:09:01+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd /data/data-spider/,success
16a00d0167a44c540000000000000042,2025-02-06T18:09:06+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim log/market_kline_day.log ,success
8187ca7d67a48bde0000000000000013,2025-02-06T18:16:05+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,# omz update,success
8187ca7d67a48bde0000000000000013,2025-02-06T18:16:12+08:00,cmd.Command,avenir_forensic_deploy,*************,sec-forensic-01,************,# screen -Dr f ,success
408d4c2267a46b920000000000000039,2025-02-06T18:29:27+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,mysql -h jp-am-prod-rds.cn2i2i0oifx6.ap-northeast-1.rds.amazonaws.com  -u superdba -p ,success
408d4c2267a46b920000000000000039,2025-02-06T18:29:42+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,show databases;,success
408d4c2267a46b920000000000000039,2025-02-06T18:29:53+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,show create database `trading-strategy`;,success
302b625367a4a4c40000000000000042,2025-02-06T20:02:16+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd /data/nexvault-data-report/,success
302b625367a4a4c40000000000000042,2025-02-06T20:02:20+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git pull,success
302b625367a4a4c40000000000000042,2025-02-06T20:02:26+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
302b625367a4a4c40000000000000042,2025-02-06T20:02:44+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/brief_market_report_data.sh,success
302b625367a4a4c40000000000000042,2025-02-06T20:03:04+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/brief_market_report.sh,success
408d4c2267a46b920000000000000039,2025-02-06T20:19:45+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,exit,success
408d4c2267a46b920000000000000039,2025-02-06T20:19:49+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,ls -ltr,success
408d4c2267a46b920000000000000039,2025-02-06T20:20:10+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,mysqldump -h $source_host -u $source_user -p --default-character-set=utf8mb4 --triggers --routines --events --skip-add-drop-table --set-gtid-purged=OFF --single-transaction --no-data trading-strategy  >/tmp/trading_strategy_bigtable_ddl.sql,success
408d4c2267a46b920000000000000039,2025-02-06T20:20:36+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,more /tmp/trading_strategy_bigtable_ddl.sql,success
114a71d467a568f70000000000000042,2025-02-07T09:59:27+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
114a71d467a568f70000000000000042,2025-02-07T09:59:52+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd /data/data-spider/,success
114a71d467a568f70000000000000042,2025-02-07T09:59:55+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim .env ,success
fd186e0867a57125000000000000003a,2025-02-07T10:34:25+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,nslookup jamf.yorkapp.com,success
114a71d467a568f70000000000000042,2025-02-07T10:55:49+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd ..,success
114a71d467a568f70000000000000042,2025-02-07T10:55:50+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd nexvault-data-report/,success
114a71d467a568f70000000000000042,2025-02-07T10:55:52+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git pull,success
114a71d467a568f70000000000000042,2025-02-07T10:56:01+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
114a71d467a568f70000000000000042,2025-02-07T10:56:17+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/brief_market_report_data.sh,success
114a71d467a568f70000000000000042,2025-02-07T10:56:27+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/brief_market_report.sh,success
7f6b00fd67a59db70000000000000039,2025-02-07T13:44:26+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,mysql -h jp-am-prod-rds.cn2i2i0oifx6.ap-northeast-1.rds.amazonaws.com  -u superdba -p ,success
7f6b00fd67a59db70000000000000039,2025-02-07T13:45:04+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,show processlist;,success
7f6b00fd67a59db70000000000000039,2025-02-07T13:45:55+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,show processlist  where User ='trading_strategy_usrc';,success
7f6b00fd67a59db70000000000000039,2025-02-07T13:46:48+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,SHOW PROCESSLIST LIKE 'trading_strategy_usrc';,success
7f6b00fd67a59db70000000000000039,2025-02-07T13:47:15+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,SHOW PROCESSLIST WHERE User = 'trading_strategy_usrc';,success
7f6b00fd67a59db70000000000000039,2025-02-07T13:47:27+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,SHOW FULL PROCESSLIST WHERE User = 'trading_strategy_usrc';,success
7f6b00fd67a59db70000000000000039,2025-02-07T13:48:46+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,select * from information_schema.processlist ;,success
7f6b00fd67a59db70000000000000039,2025-02-07T13:49:02+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,select * from information_schema.processlist  p where p.USER='trading_strategy_usrc';,success
7f6b00fd67a59db70000000000000039,2025-02-07T13:49:11+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,select * from information_schema.processlist  p where p.USER <> 'trading_strategy_usrc';,success
20ccdb3467a5a89e0000000000000039,2025-02-07T14:30:54+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,mysql -h jp-am-prod-rds.cn2i2i0oifx6.ap-northeast-1.rds.amazonaws.com  -u superdba -p ,success
20ccdb3467a5a89e0000000000000039,2025-02-07T14:31:40+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,mysql -h jp-am-prod-rds.cn2i2i0oifx6.ap-northeast-1.rds.amazonaws.com  -u superdba -p ,success
20ccdb3467a5a89e0000000000000039,2025-02-07T14:31:52+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,select user,host from mysql.user;,success
20ccdb3467a5a89e0000000000000039,2025-02-07T14:32:32+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,select * from information_schema.processlist  p where p.USER <> 'trading_strategy_usrc';,success
c59d25d567a5ac170000000000000042,2025-02-07T14:45:55+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
20ccdb3467a5a89e0000000000000039,2025-02-07T15:05:23+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,exit,success
20ccdb3467a5a89e0000000000000039,2025-02-07T15:05:24+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,pwd,success
20ccdb3467a5a89e0000000000000039,2025-02-07T15:05:26+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,ls -ltr,success
20ccdb3467a5a89e0000000000000039,2025-02-07T15:05:31+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,ls -ltr /tmp/,success
20ccdb3467a5a89e0000000000000039,2025-02-07T15:07:12+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,sz trading_strategy_data.sql,success
20ccdb3467a5a89e0000000000000039,2025-02-07T15:07:23+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,sz /tmp/trading_strategy_data.sql,success
20ccdb3467a5a89e0000000000000039,2025-02-07T15:07:26+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,**B00000000000000,success
20ccdb3467a5a89e0000000000000039,2025-02-07T15:07:26+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,**B00000000000000,success
20ccdb3467a5a89e0000000000000039,2025-02-07T15:07:26+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,**B00000000000000,success
e92bf42e67a5b2f80000000000000039,2025-02-07T15:15:07+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,pwd,success
e92bf42e67a5b2f80000000000000039,2025-02-07T15:15:26+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,  exit,success
0c968c9d67a5b40a0000000000000039,2025-02-07T15:19:41+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,ls -ltr /tmp/,success
4649fdbe67a5b6990000000000000039,2025-02-07T15:30:38+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,ls -ltr /tmp/,success
4649fdbe67a5b6990000000000000039,2025-02-07T15:31:27+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,exit,success
7079976367a5bbce0000000000000039,2025-02-07T15:52:49+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,ls /tmp,success
7079976367a5bbce0000000000000039,2025-02-07T15:52:53+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,cd /tmp/,success
9e520e6167a5e1f50000000000000039,2025-02-07T18:35:39+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,mysql -h jp-am-prod-rds.cn2i2i0oifx6.ap-northeast-1.rds.amazonaws.com  -u superdba -p ,success
9e520e6167a5e1f50000000000000039,2025-02-07T18:36:02+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,select user,host from mysql.user;,success
9e520e6167a5e1f50000000000000039,2025-02-07T18:38:49+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,SELECT User, Host FROM user WHERE User = 'your_username';,success
9e520e6167a5e1f50000000000000039,2025-02-07T18:39:11+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,SELECT User, Host FROM user WHERE User = 'trading_strategy_usrc';,success
9e520e6167a5e1f50000000000000039,2025-02-07T18:39:19+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,use mysql,success
9e520e6167a5e1f50000000000000039,2025-02-07T18:39:25+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,SELECT User, Host FROM user WHERE User = 'trading_strategy_usrc';,success
9e520e6167a5e1f50000000000000039,2025-02-07T18:40:31+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,UPDATE user SET Host = '172.%' WHERE User = 'trading_strategy_usrc' AND Host = '**********';,success
9e520e6167a5e1f50000000000000039,2025-02-07T18:40:37+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,FLUSH PRIVILEGES;,success
9e520e6167a5e1f50000000000000039,2025-02-07T18:40:42+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,SELECT User, Host FROM user WHERE User = 'trading_strategy_usrc',success
9e520e6167a5e1f50000000000000039,2025-02-07T18:40:45+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,;,success
9e520e6167a5e1f50000000000000039,2025-02-07T18:41:05+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,show processlist;,success
9e520e6167a5e1f50000000000000039,2025-02-07T18:41:13+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,show processlist;,success
9e520e6167a5e1f50000000000000039,2025-02-07T18:41:28+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,show processlist;,success
9e520e6167a5e1f50000000000000039,2025-02-07T18:42:14+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,show processlist;,success
9e520e6167a5e1f50000000000000039,2025-02-07T18:43:18+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,show processlist;,success
9e520e6167a5e1f50000000000000039,2025-02-07T18:43:26+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,SELECT User, Host FROM user WHERE User = 'trading_strategy_usrc';,success
9e520e6167a5e1f50000000000000039,2025-02-07T18:45:49+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,show grants for 'trading_strategy_usrc'@'172.%';,success
9e520e6167a5e1f50000000000000039,2025-02-07T18:46:04+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,show grants for 'trading_strategy_usrc'@'**********';,success
9e520e6167a5e1f50000000000000039,2025-02-07T18:46:18+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,GRANT ALL PRIVILEGES ON `trading-strategy`.* TO 'trading_strategy_usrc'@'172.%';,success
9e520e6167a5e1f50000000000000039,2025-02-07T18:46:24+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,flush privileges;,success
9e520e6167a5e1f50000000000000039,2025-02-07T18:46:39+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,show grants for 'trading_strategy_usrc'@'10.1.%';,success
9e520e6167a5e1f50000000000000039,2025-02-07T18:46:48+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,show grants for 'trading_strategy_usrc'@'**********';,success
9e520e6167a5e1f50000000000000039,2025-02-07T18:47:01+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,show grants for 'trading_strategy_usrc'@'172.%';,success
9e520e6167a5e1f50000000000000039,2025-02-07T18:49:41+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,show processlist;,success
9e520e6167a5e1f50000000000000039,2025-02-07T18:50:57+08:00,cmd.Command,zouwanqiang,*************,dbadmin,************,show processlist;,success
58761d6067a5f3a7000000000000003f,2025-02-07T19:51:04+08:00,cmd.Command,dujiakai,**************,security-test,************,l,success
58761d6067a5f3a7000000000000003f,2025-02-07T19:51:04+08:00,cmd.Command,dujiakai,**************,security-test,************,s,success
58761d6067a5f3a7000000000000003f,2025-02-07T19:51:06+08:00,cmd.Command,dujiakai,**************,security-test,************,cd auto-scan-attack-surface/,success
58761d6067a5f3a7000000000000003f,2025-02-07T19:51:07+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
58761d6067a5f3a7000000000000003f,2025-02-07T19:51:07+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
58761d6067a5f3a7000000000000003f,2025-02-07T19:51:09+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
58761d6067a5f3a7000000000000003f,2025-02-07T19:51:09+08:00,cmd.Command,dujiakai,**************,security-test,************,cd org-assert/,success
58761d6067a5f3a7000000000000003f,2025-02-07T19:51:12+08:00,cmd.Command,dujiakai,**************,security-test,************,cd domain/,success
58761d6067a5f3a7000000000000003f,2025-02-07T19:51:13+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
58761d6067a5f3a7000000000000003f,2025-02-07T19:51:19+08:00,cmd.Command,dujiakai,**************,security-test,************,cat domain.txt ,success
58761d6067a5f3a7000000000000003f,2025-02-07T19:51:34+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
58761d6067a5f3a7000000000000003f,2025-02-07T19:51:40+08:00,cmd.Command,dujiakai,**************,security-test,************,clear,success
58761d6067a5f3a7000000000000003f,2025-02-07T19:51:41+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
58761d6067a5f3a7000000000000003f,2025-02-07T19:51:49+08:00,cmd.Command,dujiakai,**************,security-test,************,cat *,success
58761d6067a5f3a7000000000000003f,2025-02-07T19:53:48+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
58761d6067a5f3a7000000000000003f,2025-02-07T19:53:50+08:00,cmd.Command,dujiakai,**************,security-test,************,cd ..,success
58761d6067a5f3a7000000000000003f,2025-02-07T19:53:50+08:00,cmd.Command,dujiakai,**************,security-test,************,sl,success
58761d6067a5f3a7000000000000003f,2025-02-07T19:53:52+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
58761d6067a5f3a7000000000000003f,2025-02-07T19:53:54+08:00,cmd.Command,dujiakai,**************,security-test,************,cd ip/,success
58761d6067a5f3a7000000000000003f,2025-02-07T19:53:54+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
58761d6067a5f3a7000000000000003f,2025-02-07T19:54:03+08:00,cmd.Command,dujiakai,**************,security-test,************,cat *,success
58761d6067a5f3a7000000000000003f,2025-02-07T19:54:21+08:00,cmd.Command,dujiakai,**************,security-test,************,cat * | awk '{print $!}',success
58761d6067a5f3a7000000000000003f,2025-02-07T19:54:24+08:00,cmd.Command,dujiakai,**************,security-test,************,cat * | awk '{print $1}',success
58761d6067a5f3a7000000000000003f,2025-02-07T19:54:38+08:00,cmd.Command,dujiakai,**************,security-test,************,cat * | awk '{print $1}' | sort | uniq,success
58761d6067a5f3a7000000000000003f,2025-02-07T19:56:25+08:00,cmd.Command,dujiakai,**************,security-test,************,cd ..,success
58761d6067a5f3a7000000000000003f,2025-02-07T19:56:25+08:00,cmd.Command,dujiakai,**************,security-test,************,sl,success
58761d6067a5f3a7000000000000003f,2025-02-07T19:56:26+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
58761d6067a5f3a7000000000000003f,2025-02-07T19:56:27+08:00,cmd.Command,dujiakai,**************,security-test,************,cd domain/,success
58761d6067a5f3a7000000000000003f,2025-02-07T19:56:27+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
58761d6067a5f3a7000000000000003f,2025-02-07T19:56:29+08:00,cmd.Command,dujiakai,**************,security-test,************,cat *,success
af2ed34667a5f5680000000000000042,2025-02-07T19:58:35+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,crontab -l,success
af2ed34667a5f5680000000000000042,2025-02-07T19:58:46+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,cd /data/nexvault-data-report/,success
af2ed34667a5f5680000000000000042,2025-02-07T19:58:48+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,git branch,success
af2ed34667a5f5680000000000000042,2025-02-07T19:58:51+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,git pull,success
af2ed34667a5f5680000000000000042,2025-02-07T19:59:15+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,git checkout report/brief_market_target.py,success
af2ed34667a5f5680000000000000042,2025-02-07T19:59:17+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,git pull,success
af2ed34667a5f5680000000000000042,2025-02-07T19:59:26+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,vim report/brief_market_target.py ,success
af2ed34667a5f5680000000000000042,2025-02-07T19:59:43+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,crontab -l,success
af2ed34667a5f5680000000000000042,2025-02-07T19:59:47+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,vim .env ,success
af2ed34667a5f5680000000000000042,2025-02-07T20:00:24+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,vim .env ,success
af2ed34667a5f5680000000000000042,2025-02-07T20:00:37+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,bash scripts/brief_market_report.sh,success
58761d6067a5f3a7000000000000003f,2025-02-07T20:06:16+08:00,cmd.Command,dujiakai,**************,security-test,************,cd ..,success
58761d6067a5f3a7000000000000003f,2025-02-07T20:06:17+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
58761d6067a5f3a7000000000000003f,2025-02-07T20:06:18+08:00,cmd.Command,dujiakai,**************,security-test,************,c dip,success
58761d6067a5f3a7000000000000003f,2025-02-07T20:06:20+08:00,cmd.Command,dujiakai,**************,security-test,************,cd ip/,success
58761d6067a5f3a7000000000000003f,2025-02-07T20:06:21+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
58761d6067a5f3a7000000000000003f,2025-02-07T20:06:26+08:00,cmd.Command,dujiakai,**************,security-test,************,cat * | awk '{print $1}' | sort | uniq,success
eaa1869267a6baac0000000000000042,2025-02-08T10:01:24+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd /data/nexvault-data-report/,success
eaa1869267a6baac0000000000000042,2025-02-08T10:01:27+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git branch,success
eaa1869267a6baac0000000000000042,2025-02-08T10:01:30+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git pull,success
eaa1869267a6baac0000000000000042,2025-02-08T10:01:35+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim .env ,success
eaa1869267a6baac0000000000000042,2025-02-08T10:03:36+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
eaa1869267a6baac0000000000000042,2025-02-08T10:03:50+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/brief_market_report.sh,success
2c8bf22167a703ad000000000000003a,2025-02-08T15:11:57+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ping *************,success
2c8bf22167a703ad000000000000003a,2025-02-08T15:13:47+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ping -c 1000 -i 0.1 *************,success
2c8bf22167a703ad000000000000003a,2025-02-08T15:15:37+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ping -c 1000 -i 0.1 **************,success
2c8bf22167a703ad000000000000003a,2025-02-08T15:18:03+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ping -c 1000 -i 0.1 **************,success
6fdd926867a707ed0000000000000042,2025-02-08T15:29:53+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
6fdd926867a707ed0000000000000042,2025-02-08T15:30:08+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim log/bitcoin_sentiment_report_v2.log,success
6fdd926867a707ed0000000000000042,2025-02-08T15:30:35+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/bitcoin_sentiment_report_v2.sh,success
6fdd926867a707ed0000000000000042,2025-02-08T15:30:43+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd /data/nexvault-data-report/,success
6fdd926867a707ed0000000000000042,2025-02-08T15:30:45+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/bitcoin_sentiment_report_v2.sh,success
6fdd926867a707ed0000000000000042,2025-02-08T15:31:54+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim .env ,success
6fdd926867a707ed0000000000000042,2025-02-08T15:32:15+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/bitcoin_sentiment_report_v2.sh,success
a3c7272067a71381000000000000003a,2025-02-08T16:19:17+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ping -c 1000 -i 0.1 **************,success
a3c7272067a71381000000000000003a,2025-02-08T16:21:27+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ping -c 1000 -i 0.1 **************,success
a3c7272067a71381000000000000003a,2025-02-08T16:23:21+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ping -c 1000 -i 0.1 **************,success
a3c7272067a71381000000000000003a,2025-02-08T16:27:39+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ping -c 1000 -i 0.1 **************,success
a3c7272067a71381000000000000003a,2025-02-08T16:29:29+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ping -c 1000 -i 0.1 **************,success
a3c7272067a71381000000000000003a,2025-02-08T16:36:05+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ping -c 1000 -i 0.1 **************,success
a3c7272067a71381000000000000003a,2025-02-08T16:40:22+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ping -c 10000 -i 0.1 **************,success
a3c7272067a71381000000000000003a,2025-02-08T17:03:22+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ping -c 20000 -i 0.1 **************,success
e03969c967a7239e000000000000003a,2025-02-08T17:27:58+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '.';cd '.';cd '.';pwd;ls -la,success
c3c4d2f767a6b520000000000000003f,2025-02-10T09:44:45+08:00,cmd.Command,dujiakai,**************,security-test,************,l,success
c3c4d2f767a6b520000000000000003f,2025-02-10T09:44:45+08:00,cmd.Command,dujiakai,**************,security-test,************,d,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:00:47+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:00:49+08:00,cmd.Command,dujiakai,**************,security-test,************,LS,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:00:50+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:00:58+08:00,cmd.Command,dujiakai,**************,security-test,************,grep '18.179.229.15' -nr ./,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:01:04+08:00,cmd.Command,dujiakai,**************,security-test,************,l,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:01:04+08:00,cmd.Command,dujiakai,**************,security-test,************,s,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:07:34+08:00,cmd.Command,dujiakai,**************,security-test,************,grep 'rainware' -nr ./,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:07:52+08:00,cmd.Command,dujiakai,**************,security-test,************,grep 'rainware' -nr ./,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:07:57+08:00,cmd.Command,dujiakai,**************,security-test,************,grep 'rainware' -nr ./,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:07:59+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:08:03+08:00,cmd.Command,dujiakai,**************,security-test,************,grep 'rainware' -nr ./,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:09:35+08:00,cmd.Command,dujiakai,**************,security-test,************,l,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:09:35+08:00,cmd.Command,dujiakai,**************,security-test,************,s,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:09:37+08:00,cmd.Command,dujiakai,**************,security-test,************,cd tmp-file/,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:09:37+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:09:42+08:00,cmd.Command,dujiakai,**************,security-test,************,python3 diff.py ,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:10:12+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:10:13+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:10:16+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:10:22+08:00,cmd.Command,dujiakai,**************,security-test,************,x,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:10:23+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:10:36+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
61adfd8867a960e8000000000000003a,2025-02-10T10:14:23+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ping -c 20000 -i 0.1 **************,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:22:46+08:00,cmd.Command,dujiakai,**************,security-test,************,l,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:22:46+08:00,cmd.Command,dujiakai,**************,security-test,************,s,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:22:47+08:00,cmd.Command,dujiakai,**************,security-test,************,cd ..,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:22:47+08:00,cmd.Command,dujiakai,**************,security-test,************,sl,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:23:18+08:00,cmd.Command,dujiakai,**************,security-test,************,grep -nr 'rainware.top' ./,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:35:24+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:35:24+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:35:54+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:35:55+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:35:56+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:35:57+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:35:58+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:35:59+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:36:03+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:36:05+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:36:22+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:36:43+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:36:44+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:36:47+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:36:48+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:36:51+08:00,cmd.Command,dujiakai,**************,security-test,************,l,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:36:51+08:00,cmd.Command,dujiakai,**************,security-test,************,s,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:36:54+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:37:02+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:37:17+08:00,cmd.Command,dujiakai,**************,security-test,************,nohup python3 main.py &,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:37:34+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:37:35+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:37:36+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:37:38+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:37:57+08:00,cmd.Command,dujiakai,**************,security-test,************,grep '18.179.229.15' -nr ./,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:38:03+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:38:03+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:38:12+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:38:17+08:00,cmd.Command,dujiakai,**************,security-test,************,tail -f nohup.out ,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:38:22+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:38:23+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T10:38:26+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
61adfd8867a960e8000000000000003a,2025-02-10T10:57:27+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,nslookup sentry.sosovalue.xyz,success
9a5dd7a867a96e34000000000000003a,2025-02-10T11:10:44+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; echo $$;ps -ef,success
0321c97067a96e34000000000000003a,2025-02-10T11:10:44+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 2526992; ls -lad /proc/2526992/cwd,success
e478821c67a96e34000000000000003a,2025-02-10T11:10:44+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '.';cd '/root';cd '/root';pwd;ls -la,success
a36b197067a96e2d000000000000003a,2025-02-10T11:10:53+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,cd update/,success
95e6f06a67a96e3e000000000000003a,2025-02-10T11:10:54+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 2526992; ls -lad /proc/2526992/cwd,success
a36b197067a96e2d000000000000003a,2025-02-10T11:10:54+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,ls,success
59c84e0767a96e3f000000000000003a,2025-02-10T11:10:55+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/root/';cd '/root/update';cd '/root/update';pwd;ls -la,success
c49f483a67a96e3f000000000000003a,2025-02-10T11:10:55+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 2526992; ls -lad /proc/2526992/cwd,success
a36b197067a96e2d000000000000003a,2025-02-10T11:11:11+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,mkdir 20250210,success
21e66c2a67a96e50000000000000003a,2025-02-10T11:11:12+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 2526992; ls -lad /proc/2526992/cwd,success
a36b197067a96e2d000000000000003a,2025-02-10T11:11:21+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,cd 20250210/,success
0fc5952f67a96e5a000000000000003a,2025-02-10T11:11:22+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 2526992; ls -lad /proc/2526992/cwd,success
f6f801e967a96e5a000000000000003a,2025-02-10T11:11:22+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/root/update/';cd '/root/update/20250210';cd '/root/update/20250210';pwd;ls -la,success
829506c767a96e63000000000000003a,2025-02-10T11:11:31+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/root/update/20250210/';cd '/root/update/20250210/';cd '/root/update/20250210/';pwd;ls -la,success
8b59200467a96e63000000000000003a,2025-02-10T11:11:31+08:00,file.Upload,zengshengfa,**************,fanwei-OA-test,************,esb_ip_not_allow240814.xml,success
a36b197067a96e2d000000000000003a,2025-02-10T11:11:44+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,pwd,success
fbb10c0367a96e71000000000000003a,2025-02-10T11:11:45+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 2526992; ls -lad /proc/2526992/cwd,success
a36b197067a96e2d000000000000003a,2025-02-10T11:11:52+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,cd /data/,success
97973ba967a96e79000000000000003a,2025-02-10T11:11:53+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 2526992; ls -lad /proc/2526992/cwd,success
a36b197067a96e2d000000000000003a,2025-02-10T11:12:44+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,cd /usr/WEAVER/ecology/,success
e1234e9f67a96ead000000000000003a,2025-02-10T11:12:45+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 2526992; ls -lad /proc/2526992/cwd,success
8ef434d867a96ead000000000000003a,2025-02-10T11:12:45+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/root/update/20250210/';cd '/usr/WEAVER/ecology';cd '/usr/WEAVER/ecology';pwd;ls -la,success
483c796167a96eb5000000000000003a,2025-02-10T11:12:53+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '.';cd '.';cd '.';pwd;ls -la,success
4f31ceda67a96ec7000000000000003a,2025-02-10T11:13:23+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,cd /usr/WEAVER/ecology/,success
4f31ceda67a96ec7000000000000003a,2025-02-10T11:13:24+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,ls,success
4f31ceda67a96ec7000000000000003a,2025-02-10T11:13:43+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,cd WEB-INF/,success
4f31ceda67a96ec7000000000000003a,2025-02-10T11:13:44+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,LS,success
4f31ceda67a96ec7000000000000003a,2025-02-10T11:13:46+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,ls,success
4f31ceda67a96ec7000000000000003a,2025-02-10T11:13:57+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,cd securityXML/,success
4f31ceda67a96ec7000000000000003a,2025-02-10T11:13:58+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,ls,success
4f31ceda67a96ec7000000000000003a,2025-02-10T11:15:07+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,cp /root/update/20250210/esb_ip_not_allow240814.xml ./,success
4f31ceda67a96ec7000000000000003a,2025-02-10T11:15:09+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,ls,success
4f31ceda67a96ec7000000000000003a,2025-02-10T11:17:05+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,ll,success
0f05b21b67a97002000000000000003a,2025-02-10T11:18:26+08:00,cmd.Command,zengshengfa,**************,oa-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; echo $$;ps -ef,success
239e4e4467a97003000000000000003a,2025-02-10T11:18:27+08:00,cmd.Command,zengshengfa,**************,oa-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1554801; ls -lad /proc/1554801/cwd,success
a1a08c3e67a97003000000000000003a,2025-02-10T11:18:27+08:00,cmd.Command,zengshengfa,**************,oa-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '.';cd '/root';cd '/root';pwd;ls -la,success
04c6092e67a96fff000000000000003a,2025-02-10T11:18:33+08:00,cmd.Command,zengshengfa,**************,oa-app,************,cd update/,success
b092ea9c67a9700a000000000000003a,2025-02-10T11:18:34+08:00,cmd.Command,zengshengfa,**************,oa-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1554801; ls -lad /proc/1554801/cwd,success
2952f65267a9700a000000000000003a,2025-02-10T11:18:34+08:00,cmd.Command,zengshengfa,**************,oa-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1554801; ls -lad /proc/1554801/cwd,success
04c6092e67a96fff000000000000003a,2025-02-10T11:18:34+08:00,cmd.Command,zengshengfa,**************,oa-app,************,ls,success
318e15c967a9700a000000000000003a,2025-02-10T11:18:34+08:00,cmd.Command,zengshengfa,**************,oa-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/root/';cd '/root/update';cd '/root/update';pwd;ls -la,success
30a9c2ba67a97025000000000000003a,2025-02-10T11:19:01+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '.';cd '.';cd '.';pwd;ls -la,success
4f31ceda67a96ec7000000000000003a,2025-02-10T11:19:13+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,cd /root/update/,success
4f31ceda67a96ec7000000000000003a,2025-02-10T11:19:15+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,ls,success
04c6092e67a96fff000000000000003a,2025-02-10T11:20:28+08:00,cmd.Command,zengshengfa,**************,oa-app,************,mkdir 20250210,success
c0ed415367a9707c000000000000003a,2025-02-10T11:20:28+08:00,cmd.Command,zengshengfa,**************,oa-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1554801; ls -lad /proc/1554801/cwd,success
04c6092e67a96fff000000000000003a,2025-02-10T11:20:34+08:00,cmd.Command,zengshengfa,**************,oa-app,************,cd 20250210/,success
2ca21c6167a97083000000000000003a,2025-02-10T11:20:35+08:00,cmd.Command,zengshengfa,**************,oa-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1554801; ls -lad /proc/1554801/cwd,success
8427090467a97083000000000000003a,2025-02-10T11:20:35+08:00,cmd.Command,zengshengfa,**************,oa-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/root/update/';cd '/root/update/20250210';cd '/root/update/20250210';pwd;ls -la,success
b748dc3967a9708d000000000000003a,2025-02-10T11:20:45+08:00,cmd.Command,zengshengfa,**************,oa-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/root/update/20250210/';cd '/root/update/20250210/';cd '/root/update/20250210/';pwd;ls -la,success
78f6d7c267a9708d000000000000003a,2025-02-10T11:20:45+08:00,file.Upload,zengshengfa,**************,oa-app,************,esb_ip_not_allow240814.xml,success
04c6092e67a96fff000000000000003a,2025-02-10T11:21:07+08:00,cmd.Command,zengshengfa,**************,oa-app,************,cd /data/ecology/WEB-INF/securityXML,success
cec1d68667a970a4000000000000003a,2025-02-10T11:21:08+08:00,cmd.Command,zengshengfa,**************,oa-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1554801; ls -lad /proc/1554801/cwd,success
04c6092e67a96fff000000000000003a,2025-02-10T11:21:18+08:00,cmd.Command,zengshengfa,**************,oa-app,************,cd /data,success
9025203167a970af000000000000003a,2025-02-10T11:21:19+08:00,cmd.Command,zengshengfa,**************,oa-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/root/update/20250210/';cd '/data';cd '/data';pwd;ls -la,success
04c6092e67a96fff000000000000003a,2025-02-10T11:21:19+08:00,cmd.Command,zengshengfa,**************,oa-app,************,ls,success
1aadd2ca67a970af000000000000003a,2025-02-10T11:21:19+08:00,cmd.Command,zengshengfa,**************,oa-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1554801; ls -lad /proc/1554801/cwd,success
fce8d44767a970b0000000000000003a,2025-02-10T11:21:20+08:00,cmd.Command,zengshengfa,**************,oa-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1554801; ls -lad /proc/1554801/cwd,success
04c6092e67a96fff000000000000003a,2025-02-10T11:21:23+08:00,cmd.Command,zengshengfa,**************,oa-app,************,cd weaver/,success
8010023b67a970b4000000000000003a,2025-02-10T11:21:24+08:00,cmd.Command,zengshengfa,**************,oa-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1554801; ls -lad /proc/1554801/cwd,success
539a2bba67a970b4000000000000003a,2025-02-10T11:21:24+08:00,cmd.Command,zengshengfa,**************,oa-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/data/';cd '/data/weaver';cd '/data/weaver';pwd;ls -la,success
04c6092e67a96fff000000000000003a,2025-02-10T11:21:31+08:00,cmd.Command,zengshengfa,**************,oa-app,************,cd ./ecology/WEB-INF/securityXML,success
6ab4260d67a970bc000000000000003a,2025-02-10T11:21:32+08:00,cmd.Command,zengshengfa,**************,oa-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/data/weaver/';cd '/data/weaver/ecology/WEB-INF/securityXML';cd '/data/weaver/ecology/WEB-INF/securityXML';pwd;ls -la,success
3d1a35bf67a970bc000000000000003a,2025-02-10T11:21:32+08:00,cmd.Command,zengshengfa,**************,oa-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1554801; ls -lad /proc/1554801/cwd,success
04c6092e67a96fff000000000000003a,2025-02-10T11:21:34+08:00,cmd.Command,zengshengfa,**************,oa-app,************,ls,success
32aaa00567a970bf000000000000003a,2025-02-10T11:21:35+08:00,cmd.Command,zengshengfa,**************,oa-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1554801; ls -lad /proc/1554801/cwd,success
04c6092e67a96fff000000000000003a,2025-02-10T11:21:53+08:00,cmd.Command,zengshengfa,**************,oa-app,************,cp /root/update/20250210/esb_ip_not_allow240814.xml ./,success
0d8ac98767a970d2000000000000003a,2025-02-10T11:21:54+08:00,cmd.Command,zengshengfa,**************,oa-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1554801; ls -lad /proc/1554801/cwd,success
4dee6afe67a970d4000000000000003a,2025-02-10T11:21:56+08:00,cmd.Command,zengshengfa,**************,oa-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1554801; ls -lad /proc/1554801/cwd,success
04c6092e67a96fff000000000000003a,2025-02-10T11:21:56+08:00,cmd.Command,zengshengfa,**************,oa-app,************,ls,success
4f31ceda67a96ec7000000000000003a,2025-02-10T11:22:52+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,ls /usr/WEAVER/ecology/WEB-INF/securityXML,success
e77b6c7367a97b4c0000000000000019,2025-02-10T12:06:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
e77b6c7367a97b4c0000000000000019,2025-02-10T12:07:04+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use proamsdwdb;,success
e77b6c7367a97b4c0000000000000019,2025-02-10T12:07:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_state in(4,3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
e77b6c7367a97b4c0000000000000019,2025-02-10T12:07:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
e77b6c7367a97b4c0000000000000019,2025-02-10T12:07:15+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,'2group by f_currency;,success
4f31ceda67a96ec7000000000000003a,2025-02-10T12:07:46+08:00,cmd.Command,zengshengfa,**************,fanwei-OA-test,************,exit,success
04c6092e67a96fff000000000000003a,2025-02-10T12:07:52+08:00,cmd.Command,zengshengfa,**************,oa-app,************,exit,success
e77b6c7367a97b4c0000000000000019,2025-02-10T12:23:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
e77b6c7367a97b4c0000000000000019,2025-02-10T12:23:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
96d5260767a983c20000000000000042,2025-02-10T12:42:49+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd /data/nexvault-data-report/,success
96d5260767a983c20000000000000042,2025-02-10T12:42:52+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git branch,success
96d5260767a983c20000000000000042,2025-02-10T12:42:55+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git status,success
96d5260767a983c20000000000000042,2025-02-10T12:43:12+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git checkout report/brief_market_target.py,success
96d5260767a983c20000000000000042,2025-02-10T12:43:15+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,git pull,success
96d5260767a983c20000000000000042,2025-02-10T12:43:24+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim report/brief_market_target.py ,success
96d5260767a983c20000000000000042,2025-02-10T12:43:43+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim report/brief_market_target.py ,success
96d5260767a983c20000000000000042,2025-02-10T12:44:03+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
96d5260767a983c20000000000000042,2025-02-10T12:44:14+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/brief_market_report_data.sh,success
96d5260767a983c20000000000000042,2025-02-10T12:44:24+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
96d5260767a983c20000000000000042,2025-02-10T12:44:32+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,bash scripts/brief_market_report.sh,success
8764bc9667a99c510000000000000042,2025-02-10T14:27:35+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
8764bc9667a99c510000000000000042,2025-02-10T14:31:42+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -e,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:14:30+08:00,cmd.Command,dujiakai,**************,security-test,************,s,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:14:30+08:00,cmd.Command,dujiakai,**************,security-test,************,l,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:14:38+08:00,cmd.Command,dujiakai,**************,security-test,************,grep '18.181' -nr ./,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:14:43+08:00,cmd.Command,dujiakai,**************,security-test,************,grep '18.181.186' -nr ./,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:24:16+08:00,cmd.Command,dujiakai,**************,security-test,************,grep '18.183' -nr ./,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:24:48+08:00,cmd.Command,dujiakai,**************,security-test,************,telnet ************ 22,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:25:10+08:00,cmd.Command,dujiakai,**************,security-test,************,grep '177.41' -nr ./,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:25:36+08:00,cmd.Command,dujiakai,**************,security-test,************,grep '43.207' -nr ./,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:25:47+08:00,cmd.Command,dujiakai,**************,security-test,************,grep '35.77' -nr ./,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:25:49+08:00,cmd.Command,dujiakai,**************,security-test,************,rate:  0.99-kpps, 85.00% done,   1:07:45 remaining, found=366       ,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:25:51+08:00,cmd.Command,dujiakai,**************,security-test,************,rate:  0.99-kpps, 19.99% done,   6:40:49 remaining, found=94       maining, found=0       ,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:25:57+08:00,cmd.Command,dujiakai,**************,security-test,************,grep '35.77.189' -nr ./,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:26:19+08:00,cmd.Command,dujiakai,**************,security-test,************,grep '18.183.28' -nr ./,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:27:05+08:00,cmd.Command,dujiakai,**************,security-test,************,nmap -sS ************ -p 22,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:27:27+08:00,cmd.Command,dujiakai,**************,security-test,************,nmap -sS ************ -p 22,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:27:54+08:00,cmd.Command,dujiakai,**************,security-test,************,ssh root@************,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:27:56+08:00,cmd.Command,dujiakai,**************,security-test,************,yes,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:28:44+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:31:37+08:00,cmd.Command,dujiakai,**************,security-test,************,cat ./tmp-file/all-ip-after-dig-domain-and-org-ip.txt,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:31:44+08:00,cmd.Command,dujiakai,**************,security-test,************,grep '18.183.28' -nr ./,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:32:01+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:32:04+08:00,cmd.Command,dujiakai,**************,security-test,************,tail -f nohup.out ,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:32:48+08:00,cmd.Command,dujiakai,**************,security-test,************,cat tmp-file/all-ip-after-dig-domain-and-org-ip.txt ,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:32:50+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:33:05+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:33:42+08:00,cmd.Command,dujiakai,**************,security-test,************,grep '18.183.28' -nr ./,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:34:14+08:00,cmd.Command,dujiakai,**************,security-test,************,grep '18.183.28' -nr ./,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:35:11+08:00,cmd.Command,dujiakai,**************,security-test,************,grep '18.183.28' -nr ./,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:37:54+08:00,cmd.Command,dujiakai,**************,security-test,************,grep '18.183.28' -nr ./,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:38:01+08:00,cmd.Command,dujiakai,**************,security-test,************,grep '18.183.28' -nr ./,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:38:12+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:38:13+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:38:56+08:00,cmd.Command,dujiakai,**************,security-test,************,ssh root@************,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:39:03+08:00,cmd.Command,dujiakai,**************,security-test,************,ssh root@************,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:39:15+08:00,cmd.Command,dujiakai,**************,security-test,************,ssh root@************,success
c3c4d2f767a6b520000000000000003f,2025-02-10T15:39:23+08:00,cmd.Command,dujiakai,**************,security-test,************,ssh root@************,success
61adfd8867a960e8000000000000003a,2025-02-10T17:19:39+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,nslookup ex-www-test-1.yorkapp.com,success
61adfd8867a960e8000000000000003a,2025-02-10T17:21:07+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,nslookup atlas-platform-gateway-public.test-1.common-test.yorkapp.com,success
716d06f767a9c9d3000000000000003f,2025-02-10T17:41:40+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
716d06f767a9c9d3000000000000003f,2025-02-10T17:41:41+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
716d06f767a9c9d3000000000000003f,2025-02-10T17:41:42+08:00,cmd.Command,dujiakai,**************,security-test,************,cd auto-scan-attack-surface/,success
716d06f767a9c9d3000000000000003f,2025-02-10T17:41:42+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
716d06f767a9c9d3000000000000003f,2025-02-10T17:41:54+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
716d06f767a9c9d3000000000000003f,2025-02-10T17:42:08+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
716d06f767a9c9d3000000000000003f,2025-02-10T17:42:10+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
716d06f767a9c9d3000000000000003f,2025-02-10T17:42:11+08:00,cmd.Command,dujiakai,**************,security-test,************,cd ..,success
716d06f767a9c9d3000000000000003f,2025-02-10T17:42:11+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
716d06f767a9c9d3000000000000003f,2025-02-10T17:43:52+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
716d06f767a9c9d3000000000000003f,2025-02-10T17:43:54+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
716d06f767a9c9d3000000000000003f,2025-02-10T17:44:06+08:00,cmd.Command,dujiakai,**************,security-test,************,zip -r auto-scan-attack-surface.zip auto-scan-attack-surface/,success
716d06f767a9c9d3000000000000003f,2025-02-10T17:44:09+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
716d06f767a9c9d3000000000000003f,2025-02-10T17:44:11+08:00,cmd.Command,dujiakai,**************,security-test,************,pwd,success
716d06f767a9c9d3000000000000003f,2025-02-10T17:44:13+08:00,cmd.Command,dujiakai,**************,security-test,************,exit,success
2f41522267a9cb0c000000000000003f,2025-02-10T17:46:56+08:00,file.Download,dujiakai,**************,security-test,************,auto-scan-attack-surface.zip,success
61adfd8867a960e8000000000000003a,2025-02-10T18:15:19+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,nslookup ex-stream-test-1.yorkapp.com,success
cdeb623367aae3760000000000000042,2025-02-11T13:43:22+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
cdeb623367aae3760000000000000042,2025-02-11T13:43:26+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l|grep twitt,success
cdeb623367aae3760000000000000042,2025-02-11T13:44:29+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd /data/,success
cdeb623367aae3760000000000000042,2025-02-11T13:44:30+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,ls,success
cdeb623367aae3760000000000000042,2025-02-11T13:44:34+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd data-spider/,success
cdeb623367aae3760000000000000042,2025-02-11T13:44:35+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,ls,success
cdeb623367aae3760000000000000042,2025-02-11T13:44:42+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim twitter_cookie.json ,success
cdeb623367aae3760000000000000042,2025-02-11T13:45:04+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim twitter_cookie.json ,success
cdeb623367aae3760000000000000042,2025-02-11T13:47:10+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
cdeb623367aae3760000000000000042,2025-02-11T13:47:44+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim scripts/brief_market_info_v2.sh,success
cdeb623367aae3760000000000000042,2025-02-11T13:49:07+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l|grep brief,success
cdeb623367aae3760000000000000042,2025-02-11T13:49:16+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim scripts/brief_market_info.sh,success
5f29ca5367ab3cf90000000000000042,2025-02-11T20:05:22+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd /data/workspace/yantou/,success
5f29ca5367ab3cf90000000000000042,2025-02-11T20:05:23+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,ls,success
5f29ca5367ab3cf90000000000000042,2025-02-11T20:05:26+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd data-spider/,success
5f29ca5367ab3cf90000000000000042,2025-02-11T20:05:27+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,ls,success
60a575d667ac008b0000000000000042,2025-02-12T09:59:42+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd /data/data-spider/,success
60a575d667ac008b0000000000000042,2025-02-12T09:59:44+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim .env ,success
60a575d667ac008b0000000000000042,2025-02-12T10:17:21+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd ..,success
60a575d667ac008b0000000000000042,2025-02-12T10:17:24+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd workspace/yantou/,success
60a575d667ac008b0000000000000042,2025-02-12T10:17:25+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,ls,success
60a575d667ac008b0000000000000042,2025-02-12T10:17:28+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd data-spider/,success
60a575d667ac008b0000000000000042,2025-02-12T10:17:30+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,ls,success
60a575d667ac008b0000000000000042,2025-02-12T10:17:34+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim twitter_cookie.json ,success
8422626867ac88440000000000000046,2025-02-12T19:38:49+08:00,cmd.Command,fwsys,**************,E10-app,************,sh ,success
8422626867ac88440000000000000046,2025-02-12T19:39:05+08:00,cmd.Command,fwsys,**************,E10-app,************, cd.,success
8422626867ac88440000000000000046,2025-02-12T19:39:16+08:00,cmd.Command,fwsys,**************,E10-app,************,cd /,success
8422626867ac88440000000000000046,2025-02-12T19:39:22+08:00,cmd.Command,fwsys,**************,E10-app,************,cd . ,success
8422626867ac88440000000000000046,2025-02-12T19:39:37+08:00,cmd.Command,fwsys,**************,E10-app,************,exit,success
8422626867ac88440000000000000046,2025-02-12T19:39:50+08:00,cmd.Command,fwsys,**************,E10-app,************,ls,success
8422626867ac88440000000000000046,2025-02-12T19:40:00+08:00,cmd.Command,fwsys,**************,E10-app,************, ll,success
f29842bf67aea1d30000000000000042,2025-02-14T09:53:19+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
f29842bf67aea1d30000000000000042,2025-02-14T09:53:38+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -e,success
f29842bf67aea1d30000000000000042,2025-02-14T09:53:59+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
61e9ce4f67aebc880000000000000019,2025-02-14T11:46:23+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
61e9ce4f67aebc880000000000000019,2025-02-14T11:46:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use proamsdwdb;,success
61e9ce4f67aebc880000000000000019,2025-02-14T11:46:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_state in(4,3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
61e9ce4f67aebc880000000000000019,2025-02-14T11:46:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,group by f_currency;,success
61e9ce4f67aebc880000000000000019,2025-02-14T11:46:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
61e9ce4f67aebc880000000000000019,2025-02-14T12:02:29+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
61e9ce4f67aebc880000000000000019,2025-02-14T12:02:31+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
be92f86367aede07000000000000003f,2025-02-14T14:09:12+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
be92f86367aede07000000000000003f,2025-02-14T14:09:12+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
be92f86367aede07000000000000003f,2025-02-14T14:09:14+08:00,cmd.Command,dujiakai,**************,security-test,************,pwd,success
be92f86367aede07000000000000003f,2025-02-14T14:09:14+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
be92f86367aede07000000000000003f,2025-02-14T14:09:21+08:00,cmd.Command,dujiakai,**************,security-test,************,vim tmp.txt,success
be92f86367aede07000000000000003f,2025-02-14T14:10:35+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
be92f86367aede07000000000000003f,2025-02-14T14:10:38+08:00,cmd.Command,dujiakai,**************,security-test,************,vim tmp,success
be92f86367aede07000000000000003f,2025-02-14T14:10:41+08:00,cmd.Command,dujiakai,**************,security-test,************,vim tmp.txt ,success
be92f86367aede07000000000000003f,2025-02-14T14:10:59+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
be92f86367aede07000000000000003f,2025-02-14T14:11:04+08:00,cmd.Command,dujiakai,**************,security-test,************,cat tmp.txt | wc -l,success
be92f86367aede07000000000000003f,2025-02-14T14:11:11+08:00,cmd.Command,dujiakai,**************,security-test,************,cat tmp.txt | sort | uniq | wc l,success
be92f86367aede07000000000000003f,2025-02-14T14:11:14+08:00,cmd.Command,dujiakai,**************,security-test,************,cat tmp.txt | sort | uniq | wc -l,success
be92f86367aede07000000000000003f,2025-02-14T14:11:16+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
d362bbb267aedeb3000000000000003f,2025-02-14T14:12:03+08:00,cmd.Command,dujiakai,*************,security-test,************,ls,success
d362bbb267aedeb3000000000000003f,2025-02-14T14:12:04+08:00,cmd.Command,dujiakai,*************,security-test,************,ls,success
d362bbb267aedeb3000000000000003f,2025-02-14T14:12:07+08:00,cmd.Command,dujiakai,*************,security-test,************,vim tmp.txt ,success
be92f86367aede07000000000000003f,2025-02-14T14:16:59+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
be92f86367aede07000000000000003f,2025-02-14T14:17:05+08:00,cmd.Command,dujiakai,**************,security-test,************,cat tmp.txt | wc -l,success
be92f86367aede07000000000000003f,2025-02-14T14:18:20+08:00,cmd.Command,dujiakai,**************,security-test,************,vim tmp.txt ,success
d362bbb267aedeb3000000000000003f,2025-02-14T14:18:30+08:00,cmd.Command,dujiakai,*************,security-test,************,l,success
d362bbb267aedeb3000000000000003f,2025-02-14T14:18:30+08:00,cmd.Command,dujiakai,*************,security-test,************,ls,success
d362bbb267aedeb3000000000000003f,2025-02-14T14:18:38+08:00,cmd.Command,dujiakai,*************,security-test,************,ls,success
d362bbb267aedeb3000000000000003f,2025-02-14T14:18:49+08:00,cmd.Command,dujiakai,*************,security-test,************,vim  tmp.txt,success
be92f86367aede07000000000000003f,2025-02-14T15:37:13+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
be92f86367aede07000000000000003f,2025-02-14T15:37:13+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
be92f86367aede07000000000000003f,2025-02-14T15:37:15+08:00,cmd.Command,dujiakai,**************,security-test,************,l,success
be92f86367aede07000000000000003f,2025-02-14T15:37:15+08:00,cmd.Command,dujiakai,**************,security-test,************,s,success
be92f86367aede07000000000000003f,2025-02-14T16:00:10+08:00,cmd.Command,dujiakai,**************,security-test,************,sudo vim /etc/host,success
be92f86367aede07000000000000003f,2025-02-14T16:00:15+08:00,cmd.Command,dujiakai,**************,security-test,************,sudo vim /etc/hosts,success
be92f86367aede07000000000000003f,2025-02-14T16:00:34+08:00,cmd.Command,dujiakai,**************,security-test,************,sudo vim /etc/hosts,success
be92f86367aede07000000000000003f,2025-02-14T18:46:54+08:00,cmd.Command,dujiakai,**************,security-test,************,sl,success
be92f86367aede07000000000000003f,2025-02-14T18:46:55+08:00,cmd.Command,dujiakai,**************,security-test,************,ls,success
9a5e508a67b04bd2000000000000001e,2025-02-15T16:10:03+08:00,cmd.Command,liulonglong,************,yige-vpn-uk-gateway-1,***********,curl cip.cc,success
9a5e508a67b04bd2000000000000001e,2025-02-15T16:10:53+08:00,cmd.Command,liulonglong,************,yige-vpn-uk-gateway-1,***********,curl https://***********:2117/health  -Lk,success
9a5e508a67b04bd2000000000000001e,2025-02-15T16:11:15+08:00,cmd.Command,liulonglong,************,yige-vpn-uk-gateway-1,***********,curl https://***********:2117/health  -Lk,success
9a5e508a67b04bd2000000000000001e,2025-02-15T16:12:04+08:00,cmd.Command,liulonglong,************,yige-vpn-uk-gateway-1,***********,curl https://***********:2117/health  -Lk,success
9a5e508a67b04bd2000000000000001e,2025-02-15T17:12:17+08:00,cmd.Command,liulonglong,************,yige-vpn-uk-gateway-1,***********,exit,success
9037231b67b193f1000000000000003a,2025-02-16T15:29:56+08:00,cmd.Command,zengshengfa,*************,zabbix-server,************,ls,success
f28b0c5567b193fb000000000000003a,2025-02-16T15:30:03+08:00,cmd.Command,zengshengfa,*************,zabbix-server,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; echo $$;ps -ef,success
6f667e1e67b193fb000000000000003a,2025-02-16T15:30:03+08:00,cmd.Command,zengshengfa,*************,zabbix-server,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1179242; ls -lad /proc/1179242/cwd,success
901117fb67b193fc000000000000003a,2025-02-16T15:30:04+08:00,cmd.Command,zengshengfa,*************,zabbix-server,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '.';cd '/root';cd '/root';pwd;ls -la,success
9037231b67b193f1000000000000003a,2025-02-16T15:30:15+08:00,cmd.Command,zengshengfa,*************,zabbix-server,************,pwd,success
c7bc96bb67b19408000000000000003a,2025-02-16T15:30:16+08:00,cmd.Command,zengshengfa,*************,zabbix-server,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1179242; ls -lad /proc/1179242/cwd,success
da49503a67b19418000000000000003a,2025-02-16T15:30:32+08:00,file.Upload,zengshengfa,*************,zabbix-server,************,Wiki.png,success
f1855f7b67b19419000000000000003a,2025-02-16T15:30:33+08:00,cmd.Command,zengshengfa,*************,zabbix-server,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/root/';cd '/root/';cd '/root/';pwd;ls -la,success
9037231b67b193f1000000000000003a,2025-02-16T15:30:39+08:00,cmd.Command,zengshengfa,*************,zabbix-server,************,ll,success
d1cdf55567b19420000000000000003a,2025-02-16T15:30:40+08:00,cmd.Command,zengshengfa,*************,zabbix-server,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1179242; ls -lad /proc/1179242/cwd,success
c799942d67b194f4000000000000003a,2025-02-16T15:34:12+08:00,file.Upload,zengshengfa,*************,zabbix-server,************,n9e.png,success
00aabf0167b1a0210000000000000046,2025-02-16T16:21:53+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; echo $$;ps -ef,success
65120fbf67b1a0220000000000000046,2025-02-16T16:21:54+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '.';cd '/root';cd '/root';pwd;ls -la,success
5b69320567b1a0220000000000000046,2025-02-16T16:21:54+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 17104; ls -lad /proc/17104/cwd,success
9d2ec16d67b1a0410000000000000046,2025-02-16T16:22:25+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 17104; ls -lad /proc/17104/cwd,success
fa237ddf67b1a0680000000000000046,2025-02-16T16:23:04+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/root/';cd '/root/E10/';cd '/root/E10/';pwd;ls -la,success
d887003067aeecdb0000000000000040,2025-02-16T19:43:01+08:00,cmd.Command,ams-deploy,************,ams-app-prod,************,cd ..,success
d887003067aeecdb0000000000000040,2025-02-16T19:43:02+08:00,cmd.Command,ams-deploy,************,ams-app-prod,************,ls,success
d887003067aeecdb0000000000000040,2025-02-16T19:43:07+08:00,cmd.Command,ams-deploy,************,ams-app-prod,************,cd dc-main-service/,success
d887003067aeecdb0000000000000040,2025-02-16T19:43:08+08:00,cmd.Command,ams-deploy,************,ams-app-prod,************,vi application.yml ,success
d887003067aeecdb0000000000000040,2025-02-16T19:43:34+08:00,cmd.Command,ams-deploy,************,ams-app-prod,************,cd ..,success
d887003067aeecdb0000000000000040,2025-02-16T19:43:35+08:00,cmd.Command,ams-deploy,************,ams-app-prod,************,ls,success
d887003067aeecdb0000000000000040,2025-02-16T19:43:38+08:00,cmd.Command,ams-deploy,************,ams-app-prod,************,cd dc-hr-service/,success
d887003067aeecdb0000000000000040,2025-02-16T19:43:40+08:00,cmd.Command,ams-deploy,************,ams-app-prod,************,vi application.yml ,success
d887003067aeecdb0000000000000040,2025-02-16T19:43:49+08:00,cmd.Command,ams-deploy,************,ams-app-prod,************,cd ..,success
d887003067aeecdb0000000000000040,2025-02-16T19:43:49+08:00,cmd.Command,ams-deploy,************,ams-app-prod,************,ls,success
d887003067aeecdb0000000000000040,2025-02-16T19:43:53+08:00,cmd.Command,ams-deploy,************,ams-app-prod,************,cd file-service/,success
d887003067aeecdb0000000000000040,2025-02-16T19:43:55+08:00,cmd.Command,ams-deploy,************,ams-app-prod,************,vi application.yml ,success
d887003067aeecdb0000000000000040,2025-02-16T19:44:08+08:00,cmd.Command,ams-deploy,************,ams-app-prod,************,cd ..,success
d887003067aeecdb0000000000000040,2025-02-16T19:44:08+08:00,cmd.Command,ams-deploy,************,ams-app-prod,************,ls,success
d887003067aeecdb0000000000000040,2025-02-16T19:44:10+08:00,cmd.Command,ams-deploy,************,ams-app-prod,************,cd flow-service/,success
d887003067aeecdb0000000000000040,2025-02-16T19:44:11+08:00,cmd.Command,ams-deploy,************,ams-app-prod,************,vi application.yml ,success
d887003067aeecdb0000000000000040,2025-02-16T19:44:24+08:00,cmd.Command,ams-deploy,************,ams-app-prod,************,cd ..,success
d887003067aeecdb0000000000000040,2025-02-16T19:44:25+08:00,cmd.Command,ams-deploy,************,ams-app-prod,************,ls,success
d887003067aeecdb0000000000000040,2025-02-16T19:44:26+08:00,cmd.Command,ams-deploy,************,ams-app-prod,************,cd portal-service/,success
d887003067aeecdb0000000000000040,2025-02-16T19:44:27+08:00,cmd.Command,ams-deploy,************,ams-app-prod,************,vi application.yml ,success
b8e2618f67b1a0740000000000000046,2025-02-16T19:45:27+08:00,file.Upload,fwsys,**************,E10-app,************,Ecology10.0.2501.01_forLinux_x86_64.tar.gz,success
c2d4bd8e67b1cfd90000000000000046,2025-02-16T19:45:29+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/root/E10/';cd '/root/E10/';cd '/root/E10/';pwd;ls -la,success
8714252167b1d13e0000000000000046,2025-02-16T19:51:26+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; echo $$;ps -ef,success
0a173e8c67b1d13f0000000000000046,2025-02-16T19:51:27+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/root/E10/';cd '/root';cd '/root';pwd;ls -la,success
9eb2b79067b1d13f0000000000000046,2025-02-16T19:51:27+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 17104; ls -lad /proc/17104/cwd,success
d887003067aeecdb0000000000000040,2025-02-16T20:02:09+08:00,cmd.Command,ams-deploy,************,ams-app-prod,************,cd ~,success
d887003067aeecdb0000000000000040,2025-02-16T20:02:10+08:00,cmd.Command,ams-deploy,************,ams-app-prod,************,ls,success
d887003067aeecdb0000000000000040,2025-02-16T20:02:12+08:00,cmd.Command,ams-deploy,************,ams-app-prod,************,cd /a,success
d887003067aeecdb0000000000000040,2025-02-16T20:02:12+08:00,cmd.Command,ams-deploy,************,ams-app-prod,************,ls,success
d887003067aeecdb0000000000000040,2025-02-16T20:02:14+08:00,cmd.Command,ams-deploy,************,ams-app-prod,************,cd /opt/,success
d887003067aeecdb0000000000000040,2025-02-16T20:02:15+08:00,cmd.Command,ams-deploy,************,ams-app-prod,************,ls,success
d887003067aeecdb0000000000000040,2025-02-16T20:02:18+08:00,cmd.Command,ams-deploy,************,ams-app-prod,************,ls,success
d887003067aeecdb0000000000000040,2025-02-16T20:02:18+08:00,cmd.Command,ams-deploy,************,ams-app-prod,************,cd webapp/,success
d887003067aeecdb0000000000000040,2025-02-16T20:05:59+08:00,cmd.Command,ams-deploy,************,ams-app-prod,************,curl https://nexus.webxtx.com/repository/bxtx-hosted/ ,success
d887003067aeecdb0000000000000040,2025-02-16T20:06:23+08:00,cmd.Command,ams-deploy,************,ams-app-prod,************,exit,success
728577c667b1a0210000000000000046,2025-02-17T10:40:50+08:00,cmd.Command,fwsys,**************,E10-app,************,ll,success
e8ac48e167b2a1b30000000000000046,2025-02-17T10:40:51+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 17104; ls -lad /proc/17104/cwd,success
728577c667b1a0210000000000000046,2025-02-17T10:40:55+08:00,cmd.Command,fwsys,**************,E10-app,************,df -h,success
51db898167b2a1b80000000000000046,2025-02-17T10:40:56+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 17104; ls -lad /proc/17104/cwd,success
728577c667b1a0210000000000000046,2025-02-17T10:41:28+08:00,cmd.Command,fwsys,**************,E10-app,************,mv Ecology10.0.2501.01_forLinux_x86_64.tar.gz /data/,success
372abe3e67b2a1d90000000000000046,2025-02-17T10:41:29+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 17104; ls -lad /proc/17104/cwd,success
728577c667b1a0210000000000000046,2025-02-17T10:43:55+08:00,cmd.Command,fwsys,**************,E10-app,************,ll,success
656cffca67b2a26d0000000000000046,2025-02-17T10:43:57+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 17104; ls -lad /proc/17104/cwd,success
728577c667b1a0210000000000000046,2025-02-17T10:44:10+08:00,cmd.Command,fwsys,**************,E10-app,************,cd /data/,success
923f454667b2a27b0000000000000046,2025-02-17T10:44:11+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 17104; ls -lad /proc/17104/cwd,success
c661e97767b2a27b0000000000000046,2025-02-17T10:44:11+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/root/';cd '/data';cd '/data';pwd;ls -la,success
728577c667b1a0210000000000000046,2025-02-17T10:44:11+08:00,cmd.Command,fwsys,**************,E10-app,************,ll,success
b3c6549667b2a27c0000000000000046,2025-02-17T10:44:12+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 17104; ls -lad /proc/17104/cwd,success
728577c667b1a0210000000000000046,2025-02-17T10:44:30+08:00,cmd.Command,fwsys,**************,E10-app,************,mkdir weaver,success
ad6ee00a67b2a28f0000000000000046,2025-02-17T10:44:31+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 17104; ls -lad /proc/17104/cwd,success
728577c667b1a0210000000000000046,2025-02-17T10:44:32+08:00,cmd.Command,fwsys,**************,E10-app,************,ll,success
5a71c77b67b2a2910000000000000046,2025-02-17T10:44:33+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 17104; ls -lad /proc/17104/cwd,success
728577c667b1a0210000000000000046,2025-02-17T10:44:46+08:00,cmd.Command,fwsys,**************,E10-app,************,mv /data/Ecology10.0.2501.01_forLinux_x86_64.tar.gz /data/weaver/,success
81f48be667b2a29f0000000000000046,2025-02-17T10:44:47+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 17104; ls -lad /proc/17104/cwd,success
728577c667b1a0210000000000000046,2025-02-17T10:44:49+08:00,cmd.Command,fwsys,**************,E10-app,************,ll,success
c65188c867b2a2a20000000000000046,2025-02-17T10:44:50+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 17104; ls -lad /proc/17104/cwd,success
728577c667b1a0210000000000000046,2025-02-17T10:44:54+08:00,cmd.Command,fwsys,**************,E10-app,************,cd weaver/,success
bf92f21d67b2a2a70000000000000046,2025-02-17T10:44:55+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 17104; ls -lad /proc/17104/cwd,success
728577c667b1a0210000000000000046,2025-02-17T10:44:55+08:00,cmd.Command,fwsys,**************,E10-app,************,ll,success
586a1f8c67b2a2a70000000000000046,2025-02-17T10:44:55+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/data/';cd '/data/weaver';cd '/data/weaver';pwd;ls -la,success
c7a3ccaf67b2a2a80000000000000046,2025-02-17T10:44:56+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 17104; ls -lad /proc/17104/cwd,success
728577c667b1a0210000000000000046,2025-02-17T10:45:06+08:00,cmd.Command,fwsys,**************,E10-app,************,tar -xvf Ecology10.0.2501.01_forLinux_x86_64.tar.gz ,success
68cd104667b2a2b30000000000000046,2025-02-17T10:45:07+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 17104; ls -lad /proc/17104/cwd,success
de3a0d6167b2a3670000000000000019,2025-02-17T10:48:13+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
de3a0d6167b2a3670000000000000019,2025-02-17T10:48:27+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use proamsdwdb;,success
de3a0d6167b2a3670000000000000019,2025-02-17T10:48:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_state in(4,3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
de3a0d6167b2a3670000000000000019,2025-02-17T10:48:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
de3a0d6167b2a3670000000000000019,2025-02-17T10:48:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w group by f_currency;,success
728577c667b1a0210000000000000046,2025-02-17T10:50:10+08:00,cmd.Command,fwsys,**************,E10-app,************,ll,success
d07dd12e67b2a3e30000000000000046,2025-02-17T10:50:11+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 17104; ls -lad /proc/17104/cwd,success
728577c667b1a0210000000000000046,2025-02-17T10:50:41+08:00,cmd.Command,fwsys,**************,E10-app,************,cd ecology/product/,success
2b8fcce067b2a4020000000000000046,2025-02-17T10:50:42+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 17104; ls -lad /proc/17104/cwd,success
ffad117967b2a4020000000000000046,2025-02-17T10:50:42+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/data/weaver/';cd '/data/weaver/ecology/product';cd '/data/weaver/ecology/product';pwd;ls -la,success
728577c667b1a0210000000000000046,2025-02-17T10:52:13+08:00,cmd.Command,fwsys,**************,E10-app,************,cd /data/,success
b9c55cd167b2a45e0000000000000046,2025-02-17T10:52:14+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 17104; ls -lad /proc/17104/cwd,success
0621a47367b2a45e0000000000000046,2025-02-17T10:52:14+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/data/weaver/ecology/product/';cd '/data';cd '/data';pwd;ls -la,success
728577c667b1a0210000000000000046,2025-02-17T10:52:26+08:00,cmd.Command,fwsys,**************,E10-app,************,mkdir monitor,success
eba8f19e67b2a46b0000000000000046,2025-02-17T10:52:27+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 17104; ls -lad /proc/17104/cwd,success
728577c667b1a0210000000000000046,2025-02-17T10:52:43+08:00,cmd.Command,fwsys,**************,E10-app,************,cd weaver/ecology//product/,success
56ee9bc067b2a47c0000000000000046,2025-02-17T10:52:44+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 17104; ls -lad /proc/17104/cwd,success
fdd4352967b2a47c0000000000000046,2025-02-17T10:52:44+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/data/';cd '/data/weaver/ecology/product';cd '/data/weaver/ecology/product';pwd;ls -la,success
728577c667b1a0210000000000000046,2025-02-17T10:53:05+08:00,cmd.Command,fwsys,**************,E10-app,************,sh setup.sh /data/monitor/,success
5147e34267b2a4920000000000000046,2025-02-17T10:53:06+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 17104; ls -lad /proc/17104/cwd,success
728577c667b1a0210000000000000046,2025-02-17T10:55:26+08:00,cmd.Command,fwsys,**************,E10-app,************,uname -a,success
872e562a67b2a51f0000000000000046,2025-02-17T10:55:27+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 17104; ls -lad /proc/17104/cwd,success
728577c667b1a0210000000000000046,2025-02-17T10:55:48+08:00,cmd.Command,fwsys,**************,E10-app,************,bash setup.sh /data/monitor/,success
8abc7f9167b2a5350000000000000046,2025-02-17T10:55:49+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 17104; ls -lad /proc/17104/cwd,success
728577c667b1a0210000000000000046,2025-02-17T11:01:31+08:00,cmd.Command,fwsys,**************,E10-app,************,ip addr,success
4604d3d867b2a68c0000000000000046,2025-02-17T11:01:32+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 17104; ls -lad /proc/17104/cwd,success
728577c667b1a0210000000000000046,2025-02-17T11:03:21+08:00,cmd.Command,fwsys,**************,E10-app,************,sudo ufw status,success
4756260167b2a6fa0000000000000046,2025-02-17T11:03:22+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 17104; ls -lad /proc/17104/cwd,success
bd56b0d867b2aed80000000000000042,2025-02-17T11:37:00+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab,success
bd56b0d867b2aed80000000000000042,2025-02-17T11:37:04+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
bd56b0d867b2aed80000000000000042,2025-02-17T11:37:08+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l|grep gov,success
bd56b0d867b2aed80000000000000042,2025-02-17T11:37:21+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim /data/arkham_data_monitor/arkham_data_monitor/log/crontab_gov_monitor.log,success
bd56b0d867b2aed80000000000000042,2025-02-17T11:37:58+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd /data/arkham_data_monitor/,success
bd56b0d867b2aed80000000000000042,2025-02-17T11:37:59+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,ls,success
bd56b0d867b2aed80000000000000042,2025-02-17T11:38:02+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd arkham_data_monitor/,success
bd56b0d867b2aed80000000000000042,2025-02-17T11:38:03+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,ls,success
bd56b0d867b2aed80000000000000042,2025-02-17T11:38:05+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim .env,success
bd56b0d867b2aed80000000000000042,2025-02-17T11:38:16+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd ..,success
bd56b0d867b2aed80000000000000042,2025-02-17T11:38:36+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd arkham_data_monitor/,success
bd56b0d867b2aed80000000000000042,2025-02-17T11:38:37+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,ls,success
bd56b0d867b2aed80000000000000042,2025-02-17T11:38:43+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim .env,success
bd56b0d867b2aed80000000000000042,2025-02-17T11:39:00+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
bd56b0d867b2aed80000000000000042,2025-02-17T11:39:04+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l|grep gov,success
de3a0d6167b2a3670000000000000019,2025-02-17T11:54:33+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use proamsdwdb;,success
de3a0d6167b2a3670000000000000019,2025-02-17T11:54:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T11:54:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
de3a0d6167b2a3670000000000000019,2025-02-17T11:54:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
de3a0d6167b2a3670000000000000019,2025-02-17T11:54:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
de3a0d6167b2a3670000000000000019,2025-02-17T11:54:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
de3a0d6167b2a3670000000000000019,2025-02-17T11:54:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
de3a0d6167b2a3670000000000000019,2025-02-17T11:54:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T11:54:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T11:54:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-12-06',success
de3a0d6167b2a3670000000000000019,2025-02-17T11:54:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
de3a0d6167b2a3670000000000000019,2025-02-17T11:54:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
de3a0d6167b2a3670000000000000019,2025-02-17T11:54:51+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T11:54:52+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where amount >= 100;,success
de3a0d6167b2a3670000000000000019,2025-02-17T11:56:15+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-12-06'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 1000;,success
de3a0d6167b2a3670000000000000019,2025-02-17T11:56:49+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-12-06'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 10000;,success
de3a0d6167b2a3670000000000000019,2025-02-17T11:57:55+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T11:57:55+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
de3a0d6167b2a3670000000000000019,2025-02-17T11:57:55+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
de3a0d6167b2a3670000000000000019,2025-02-17T11:57:55+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
de3a0d6167b2a3670000000000000019,2025-02-17T11:57:55+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
de3a0d6167b2a3670000000000000019,2025-02-17T11:57:55+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
de3a0d6167b2a3670000000000000019,2025-02-17T11:57:55+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T11:57:55+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T11:57:55+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-12-13',success
de3a0d6167b2a3670000000000000019,2025-02-17T11:57:55+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T11:57:55+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
de3a0d6167b2a3670000000000000019,2025-02-17T11:57:55+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
de3a0d6167b2a3670000000000000019,2025-02-17T11:57:56+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where amount >= 100;,success
de3a0d6167b2a3670000000000000019,2025-02-17T11:58:30+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-12-13'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 1000;,success
de3a0d6167b2a3670000000000000019,2025-02-17T11:59:09+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-12-13'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 10000;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:00:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:00:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:00:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:00:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:00:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:00:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:00:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:00:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:00:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-12-20',success
de3a0d6167b2a3670000000000000019,2025-02-17T12:00:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:00:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:00:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:00:07+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where amount >= 100;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:00:52+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-12-20'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 1000;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:01:29+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-12-20'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 10000;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:03:02+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:03:02+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:03:02+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:03:02+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:03:02+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:03:02+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:03:02+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:03:02+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:03:02+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-12-27',success
de3a0d6167b2a3670000000000000019,2025-02-17T12:03:02+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:03:02+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:03:02+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:03:03+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where amount >= 100;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:03:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-12-27'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 1000;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:04:20+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-12-27'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 10000;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:12:29+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:12:29+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:12:29+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:12:29+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:12:29+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:12:29+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:12:29+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:12:29+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where amount >= 100; ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:12:29+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025/01/03',success
de3a0d6167b2a3670000000000000019,2025-02-17T12:12:29+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:12:29+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:12:29+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:12:29+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:13:24+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025/01/03'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 1000;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:14:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025/01/03'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 10000;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:12+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where amount >= 100;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:12+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:12+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:12+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:12+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:12+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:12+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:12+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) > '2024-11-29' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:12+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-12-06',success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:12+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:12+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:12+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:12+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:35+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:35+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:35+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:35+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_idwhere amount >= 100;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:35+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:35+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:35+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:35+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) > '2024-12-06' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:35+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-12-13',success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:35+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:35+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:35+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:35+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) > '2024-12-13' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-12-20',success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:15:55+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amwhere amount >= 100;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:16:15+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:16:15+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:16:15+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:16:15+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:16:15+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:16:15+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:16:15+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:16:15+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) > '2024-12-20' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:16:15+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2024-12-27',success
de3a0d6167b2a3670000000000000019,2025-02-17T12:16:15+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:16:15+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:16:15+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:16:16+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11where amount >= 100;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:16:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:16:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:16:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:16:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:16:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:16:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:16:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:16:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) > '2024-12-27' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:16:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-01-03',success
de3a0d6167b2a3670000000000000019,2025-02-17T12:16:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:16:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:16:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:16:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_idwhere amount >= 100;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:21:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:21:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:21:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:21:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:21:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:21:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:21:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:21:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) > '2024-01-03' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:21:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-01-10',success
de3a0d6167b2a3670000000000000019,2025-02-17T12:21:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:21:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:21:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:21:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    grouwhere amount >= 100;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:21:57+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:21:57+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:21:57+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:21:57+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:21:57+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:21:57+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:21:57+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:21:57+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) > '2025-01-03' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:21:57+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-01-10',success
de3a0d6167b2a3670000000000000019,2025-02-17T12:21:57+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:21:57+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:21:57+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:21:58+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_idwhere amount >= 100;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:22:22+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:22:22+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:22:22+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:22:22+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:22:22+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:22:22+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:22:22+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:22:22+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) > '2025-01-10' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:22:22+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-01-17',success
de3a0d6167b2a3670000000000000019,2025-02-17T12:22:22+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:22:22+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:22:22+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:22:23+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) usewhere amount >= 100;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:22:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:22:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:22:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:22:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:22:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:22:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:22:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:22:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) > '2025-01-17' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:22:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-01-24',success
de3a0d6167b2a3670000000000000019,2025-02-17T12:22:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:22:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:22:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:22:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where amount >= 100;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:23:05+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:23:05+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:23:05+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:23:05+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:23:05+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:23:05+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:23:05+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:23:05+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) > '2025-01-24' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:23:05+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-01-31',success
de3a0d6167b2a3670000000000000019,2025-02-17T12:23:05+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:23:05+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:23:05+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:23:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_idwhere amount >= 100;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:23:25+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:23:25+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:23:25+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:23:25+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:23:25+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:23:25+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:23:25+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:23:25+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:23:25+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025/01/10',success
de3a0d6167b2a3670000000000000019,2025-02-17T12:23:25+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:23:25+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:23:25+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:23:26+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_idwhere amount >= 100;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:24:03+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025/01/10'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 1000;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:24:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025/01/10'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 10000;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:25:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:25:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:25:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:25:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:25:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:25:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:25:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:25:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:25:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025/01/17',success
de3a0d6167b2a3670000000000000019,2025-02-17T12:25:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:25:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:25:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:25:29+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where amount >= 100;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:26:27+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025/01/17'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 1000;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:27:02+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025/01/17'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 10000;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:28:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:28:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:28:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:28:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:28:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:28:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:28:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:28:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:28:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025/01/24',success
de3a0d6167b2a3670000000000000019,2025-02-17T12:28:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:28:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:28:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:28:07+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where amount >= 100;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:28:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025/01/24'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 1000;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:29:21+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025/01/24'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 10000;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:30:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:30:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:30:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:30:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:30:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:30:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:30:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:30:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:30:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-01-31',success
de3a0d6167b2a3670000000000000019,2025-02-17T12:30:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:30:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:30:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:30:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where amount >= 100;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:31:12+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-01-31'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 1000;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:31:48+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-01-31'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 10000;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:34:07+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where amount >= 100;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:34:07+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:34:07+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:34:07+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:34:07+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:34:07+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:34:07+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:34:07+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:34:07+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-02-07',success
de3a0d6167b2a3670000000000000019,2025-02-17T12:34:07+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:34:07+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:34:07+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:34:07+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:34:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-02-07'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 1000;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:35:17+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-02-07'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 10000;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:36:07+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:36:07+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as                                            ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:36:07+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:36:07+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:36:07+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:36:08+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:36:08+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-02-14',success
de3a0d6167b2a3670000000000000019,2025-02-17T12:36:08+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:36:08+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:36:08+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:36:08+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:36:08+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where amount >= 100;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:36:08+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:36:46+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-02-14'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 1000;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:37:22+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount from (     select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount      from t_withdraw_virtual dw      inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type     where cy.rate_date = '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) >= '2022-10-15'      and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-02-14'     and dw.f_state = 11     group by f_user_id ) user_amount where amount >= 10000;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:38:08+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:38:08+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:38:08+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:38:08+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:38:08+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:38:08+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:38:08+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:38:08+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) > '2025-01-31' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:38:08+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-02-07',success
de3a0d6167b2a3670000000000000019,2025-02-17T12:38:08+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:38:08+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:38:08+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:38:09+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where amount >= 100;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:38:26+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:38:26+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(f_user_id) as count_user, sum(amount) as amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:38:26+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:38:26+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    select f_user_id, sum(dw.f_amount * cy.usdt_rate) as amount ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:38:26+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    from t_withdraw_virtual dw ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:38:26+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    inner join hbg_trade.exchange_rate cy on dw.f_currency = cy.currcy_type,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:38:26+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    where cy.rate_date = '2022-10-15' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:38:26+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) > '2025-02-07' ,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:38:26+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and FROM_UNIXTIME(dw.f_updated_at/1000) <= '2025-02-14',success
de3a0d6167b2a3670000000000000019,2025-02-17T12:38:26+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    and dw.f_state = 11,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:38:26+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    group by f_user_id,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:38:26+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) user_amount,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:38:27+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,    grouwhere amount >= 100;,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:39:36+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
de3a0d6167b2a3670000000000000019,2025-02-17T12:39:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
0ffeb69467b2ccfd0000000000000042,2025-02-17T13:45:44+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
0ffeb69467b2ccfd0000000000000042,2025-02-17T13:45:57+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l|grep gov,success
0ffeb69467b2ccfd0000000000000042,2025-02-17T13:46:12+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd /data/arkham_data_monitor/,success
0ffeb69467b2ccfd0000000000000042,2025-02-17T13:46:13+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,ls,success
0ffeb69467b2ccfd0000000000000042,2025-02-17T13:46:20+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,source /data/venv/bin/activate,success
0ffeb69467b2ccfd0000000000000042,2025-02-17T13:46:41+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim arkham_data_monitor/run_gov_v2.sh ,success
0ffeb69467b2ccfd0000000000000042,2025-02-17T13:46:58+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd arkham_data_monitor/,success
0ffeb69467b2ccfd0000000000000042,2025-02-17T13:47:04+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,./run_gov_v2.sh ,success
0ffeb69467b2ccfd0000000000000042,2025-02-17T13:47:43+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,ls,success
0ffeb69467b2ccfd0000000000000042,2025-02-17T13:50:00+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,ls,success
0ffeb69467b2ccfd0000000000000042,2025-02-17T13:50:06+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd utils/,success
0ffeb69467b2ccfd0000000000000042,2025-02-17T13:50:07+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,ls,success
0ffeb69467b2ccfd0000000000000042,2025-02-17T13:50:10+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim config.py ,success
21ed0ed767b2cfb50000000000000039,2025-02-17T13:57:17+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,source_port=xxx,success
21ed0ed767b2cfb50000000000000039,2025-02-17T13:57:17+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,source_user=superdba,success
21ed0ed767b2cfb50000000000000039,2025-02-17T13:57:23+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,source_port=3306,success
21ed0ed767b2cfb50000000000000039,2025-02-17T13:57:31+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,mysqldump -h $source_host -u $source_user -p --default-character-set=utf8mb4 --triggers --routines --events --skip-add-drop-table --set-gtid-purged=OFF --single-transaction --no-data --databases trading-strategy > /tmp/trading_strategy_bigtable_ddl.sql,success
21ed0ed767b2cfb50000000000000039,2025-02-17T13:57:52+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,cat /tmp/trading_strategy_bigtable_ddl.sql,success
21ed0ed767b2cfb50000000000000039,2025-02-17T13:57:57+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,cat /tmp/trading_strategy_bigtable_ddl.sql|grep -i mb3,success
21ed0ed767b2cfb50000000000000039,2025-02-17T13:58:00+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,cat /tmp/trading_strategy_bigtable_ddl.sql|grep -i 900,success
21ed0ed767b2cfb50000000000000039,2025-02-17T13:59:33+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,mv /tmp/trading_strategy_bigtable_ddl.sql  /tmp/trading-strategy_nodata.sql ,success
21ed0ed767b2cfb50000000000000039,2025-02-17T13:59:41+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,grep -i utf8mb4_0900_ai_ci /tmp/trading-strategy_nodata.sql,success
21ed0ed767b2cfb50000000000000039,2025-02-17T13:59:56+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,grep -i utf8mb3 /tmp/trading-strategy_nodata.sql,success
21ed0ed767b2cfb50000000000000039,2025-02-17T14:00:10+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,sed -i 's/utf8mb4_0900_ai_ci/utf8mb4_general_ci/g' /tmp/trading-strategy_nodata.sql,success
21ed0ed767b2cfb50000000000000039,2025-02-17T14:02:52+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,mysqldump -h $source_host -u $source_user -p --default-character-set=utf8mb4 --triggers --routines --events --skip-add-drop-table --set-gtid-purged=OFF --single-transaction --no-create-info trading-strategy "t_sfa_trans_account_asset" "t_sfa_trans_strategy_close_auto_order" "t_sfa_trans_strategy_extreme_close_auto_order" "t_sfa_trans_strategy_open_auto_order" "t_sfa_trans_strategy_param_config" > /tmp/trading_strategy_data.sql,success
21ed0ed767b2cfb50000000000000039,2025-02-17T14:03:16+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,more /tmp/trading_strategy_data.sql,success
21ed0ed767b2cfb50000000000000039,2025-02-17T14:03:38+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,cat /tmp/trading_strategy_data.sql|grep -i create,success
21ed0ed767b2cfb50000000000000039,2025-02-17T14:04:12+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,ifconfig,success
21ed0ed767b2cfb50000000000000039,2025-02-17T14:05:17+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,ls -ltr /tmp/,success
21ed0ed767b2cfb50000000000000039,2025-02-17T14:05:20+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,ls -ltr /tmp/*.sql,success
0ffeb69467b2ccfd0000000000000042,2025-02-17T14:05:38+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd,success
0ffeb69467b2ccfd0000000000000042,2025-02-17T14:05:40+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l,success
0ffeb69467b2ccfd0000000000000042,2025-02-17T14:05:50+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim scripts/brief_market_info_v2.sh,success
0ffeb69467b2ccfd0000000000000042,2025-02-17T14:05:56+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,cd /data/data-spider/,success
0ffeb69467b2ccfd0000000000000042,2025-02-17T14:05:58+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,vim scripts/brief_market_info_v2.sh,success
728577c667b1a0210000000000000046,2025-02-17T14:17:25+08:00,cmd.Command,fwsys,**************,E10-app,************,curl http://************:9081,success
36693f4f67b2d4770000000000000046,2025-02-17T14:17:27+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 17104; ls -lad /proc/17104/cwd,success
065cb58467b2d5480000000000000019,2025-02-17T14:21:01+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
065cb58467b2d5480000000000000019,2025-02-17T14:21:20+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use hbg_trade; ,success
065cb58467b2d5480000000000000019,2025-02-17T14:21:35+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_state, count(*) as user_count,success
065cb58467b2d5480000000000000019,2025-02-17T14:21:35+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from t_account_manage_fee_snapshot  ,success
065cb58467b2d5480000000000000019,2025-02-17T14:21:35+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_day like '202412%',success
065cb58467b2d5480000000000000019,2025-02-17T14:21:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,group by f_state;,success
065cb58467b2d5480000000000000019,2025-02-17T14:24:57+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_currency,sum(f_amount),success
065cb58467b2d5480000000000000019,2025-02-17T14:24:57+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_day like '202412%'  and f_state = 1 ,success
065cb58467b2d5480000000000000019,2025-02-17T14:24:57+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from t_account_manage_fee_snapshot,success
065cb58467b2d5480000000000000019,2025-02-17T14:24:58+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,group by f_currency order by f_currency;,success
065cb58467b2d5480000000000000019,2025-02-17T14:36:10+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_currency,sum(f_amount),success
065cb58467b2d5480000000000000019,2025-02-17T14:36:10+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from t_account_manage_fee_snapshot,success
065cb58467b2d5480000000000000019,2025-02-17T14:36:10+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_day like '202412%'  and f_state = 1 ,success
065cb58467b2d5480000000000000019,2025-02-17T14:36:10+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,and f_currency in ('btc', 'eth', 'tfuel', 'usdt', 'doge', 'shib', 'xrp', 'trx', 'ltc', 'ada', 'fil', 'elf', 'eos', 'ong', 'etc', 'cvnt', 'btt', 'bsv', 'link', 'dot', 'nft', 'theta', 'mx', 'uni'),success
065cb58467b2d5480000000000000019,2025-02-17T14:36:11+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,group by f_currency order by f_currency;,success
065cb58467b2d5480000000000000019,2025-02-17T14:42:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_state, count(*) as user_count,success
065cb58467b2d5480000000000000019,2025-02-17T14:42:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from t_account_manage_fee_snapshot  ,success
065cb58467b2d5480000000000000019,2025-02-17T14:42:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_day like '202501%',success
065cb58467b2d5480000000000000019,2025-02-17T14:42:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,group by f_state;,success
065cb58467b2d5480000000000000019,2025-02-17T14:46:15+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_currency,sum(f_amount),success
065cb58467b2d5480000000000000019,2025-02-17T14:46:15+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from t_account_manage_fee_snapshot,success
065cb58467b2d5480000000000000019,2025-02-17T14:46:15+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_day like '202501%'  and f_state = 1 ,success
065cb58467b2d5480000000000000019,2025-02-17T14:46:15+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,group by f_currency order by f_currency;,success
065cb58467b2d5480000000000000019,2025-02-17T14:50:35+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_currency,sum(f_amount) as fee_amount,count(distinct f_user_id) as user_count,success
065cb58467b2d5480000000000000019,2025-02-17T14:50:35+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from t_account_manage_fee_snapshot,success
065cb58467b2d5480000000000000019,2025-02-17T14:50:35+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_day in('********') and f_currency ,success
065cb58467b2d5480000000000000019,2025-02-17T14:50:35+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,in('btc', 'eth','usdt','ht','shib','doge','xrp','eos','ltc','trx'),success
065cb58467b2d5480000000000000019,2025-02-17T14:50:35+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,group by f_currency order by f_currency;,success
065cb58467b2d5480000000000000019,2025-02-17T14:51:18+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,and f_currency in ('btc', 'eth', 'tfuel', 'usdt', 'doge', 'shib', 'xrp', 'trx', 'ltc', 'ada', 'fil', 'elf', 'eos', 'ong', 'etc', 'cvnt', 'btt', 'bsv', 'link', 'dot', 'nft', 'theta', 'mx', 'uni'),success
065cb58467b2d5480000000000000019,2025-02-17T14:51:18+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from t_account_manage_fee_snapshot,success
065cb58467b2d5480000000000000019,2025-02-17T14:51:18+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_day like '202501%'  and f_state = 1 ,success
065cb58467b2d5480000000000000019,2025-02-17T14:51:18+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_currency,sum(f_amount),success
065cb58467b2d5480000000000000019,2025-02-17T14:51:22+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,group by f_currency order by f_currency;,success
065cb58467b2d5480000000000000019,2025-02-17T14:58:21+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
065cb58467b2d5480000000000000019,2025-02-17T14:58:22+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
0ffeb69467b2ccfd0000000000000042,2025-02-17T15:06:53+08:00,cmd.Command,chenzili,*************,data-research-test-01,************,crontab -l |grep cmc_global_metrics,success
21ed0ed767b2cfb50000000000000039,2025-02-17T15:44:16+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,time mysqldump -h $source_host -u $source_user -p --default-character-set=utf8mb4 --triggers --routines --events --skip-add-drop-table --set-gtid-purged=OFF --single-transaction --no-create-info trading-strategy "t_sfa_trans_account_asset" "t_sfa_trans_strategy_close_auto_order" "t_sfa_trans_strategy_extreme_close_auto_order" "t_sfa_trans_strategy_open_auto_order" "t_sfa_trans_strategy_param_config" > /tmp/trading_strategy_data.sql,success
21ed0ed767b2cfb50000000000000039,2025-02-17T15:46:20+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,ifconfig,success
21ed0ed767b2cfb50000000000000039,2025-02-17T15:48:20+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,mysql -h $source_host -u $source_user -p,success
21ed0ed767b2cfb50000000000000039,2025-02-17T15:48:28+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,show databases;,success
21ed0ed767b2cfb50000000000000039,2025-02-17T15:48:36+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,use `trading-strategy`;,success
21ed0ed767b2cfb50000000000000039,2025-02-17T15:48:52+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,SELECT ,success
21ed0ed767b2cfb50000000000000039,2025-02-17T15:48:52+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,WHERE ,success
21ed0ed767b2cfb50000000000000039,2025-02-17T15:48:52+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,    table_rows ,success
21ed0ed767b2cfb50000000000000039,2025-02-17T15:48:52+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,FROM ,success
21ed0ed767b2cfb50000000000000039,2025-02-17T15:48:52+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,    information_schema.tables ,success
21ed0ed767b2cfb50000000000000039,2025-02-17T15:48:52+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,    table_name, ,success
21ed0ed767b2cfb50000000000000039,2025-02-17T15:49:00+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,    table_schema = 'trading-strategy';,success
21ed0ed767b2cfb50000000000000039,2025-02-17T15:50:19+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,exit,success
21ed0ed767b2cfb50000000000000039,2025-02-17T16:06:53+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,top,success
c2411e1367b4246e0000000000000040,2025-02-18T14:15:34+08:00,file.Download,ams-deploy,182.150.22.71,ams-db-win,172.16.16.68,fetch("https:  ams-test.docsl.com ams portal-service azure_l.txt,success
1bd1d92767b42a840000000000000046,2025-02-18T14:36:52+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; echo $$;ps -ef,success
0bf95b7f67b42a840000000000000046,2025-02-18T14:36:52+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
3ae2958e67b42a840000000000000046,2025-02-18T14:36:52+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '.';cd '/root';cd '/root';pwd;ls -la,success
b8bb83e367b42a9c0000000000000046,2025-02-18T14:38:07+08:00,file.Upload,fwsys,**************,oa-migrate,************,jdk-8u151-linux-x64.tar.gz,success
21cb068d67b42acf0000000000000046,2025-02-18T14:39:57+08:00,file.Upload,fwsys,**************,oa-migrate,************,weaverFlink241226.zip,success
fdd5b41767b42b3e0000000000000046,2025-02-18T14:39:58+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/root/';cd '/root/';cd '/root/';pwd;ls -la,success
9539109f67b42a840000000000000046,2025-02-18T14:45:34+08:00,cmd.Command,fwsys,**************,oa-migrate,************,ll,success
23ac3fde67b42c8f0000000000000046,2025-02-18T14:45:35+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
9539109f67b42a840000000000000046,2025-02-18T14:45:43+08:00,cmd.Command,fwsys,**************,oa-migrate,************,df -h,success
6d5be05567b42c980000000000000046,2025-02-18T14:45:44+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
0d3f19c067b42cbc0000000000000046,2025-02-18T14:46:20+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
9539109f67b42a840000000000000046,2025-02-18T14:46:20+08:00,cmd.Command,fwsys,**************,oa-migrate,************,/usr/,success
9539109f67b42a840000000000000046,2025-02-18T14:46:21+08:00,cmd.Command,fwsys,**************,oa-migrate,************,ll,success
8f96862b67b42cbd0000000000000046,2025-02-18T14:46:21+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
9539109f67b42a840000000000000046,2025-02-18T14:46:33+08:00,cmd.Command,fwsys,**************,oa-migrate,************,cd /usr/,success
c3bb3baf67b42cc90000000000000046,2025-02-18T14:46:33+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
49a313e267b42cca0000000000000046,2025-02-18T14:46:34+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/root/';cd '/usr';cd '/usr';pwd;ls -la,success
837c2f4a67b42cca0000000000000046,2025-02-18T14:46:34+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
9539109f67b42a840000000000000046,2025-02-18T14:46:34+08:00,cmd.Command,fwsys,**************,oa-migrate,************,ll,success
1dbab49a67b42cd20000000000000046,2025-02-18T14:46:42+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
9909fa4167b42cd20000000000000046,2025-02-18T14:46:42+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/usr/';cd '/';cd '/';pwd;ls -la,success
9539109f67b42a840000000000000046,2025-02-18T14:46:42+08:00,cmd.Command,fwsys,**************,oa-migrate,************,cd ..,success
9539109f67b42a840000000000000046,2025-02-18T14:46:57+08:00,cmd.Command,fwsys,**************,oa-migrate,************,cd /opt/,success
ddba57c767b42ce10000000000000046,2025-02-18T14:46:57+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
9539109f67b42a840000000000000046,2025-02-18T14:46:58+08:00,cmd.Command,fwsys,**************,oa-migrate,************,ll,success
9b1b503e67b42ce20000000000000046,2025-02-18T14:46:58+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/';cd '/opt';cd '/opt';pwd;ls -la,success
34d5651667b42ce20000000000000046,2025-02-18T14:46:58+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
ce83020467b42cea0000000000000046,2025-02-18T14:47:06+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/opt/';cd '/';cd '/';pwd;ls -la,success
9539109f67b42a840000000000000046,2025-02-18T14:47:06+08:00,cmd.Command,fwsys,**************,oa-migrate,************,cd ..,success
f6c84d5d67b42cea0000000000000046,2025-02-18T14:47:06+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
2dbb69aa67b42d040000000000000046,2025-02-18T14:47:32+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
9539109f67b42a840000000000000046,2025-02-18T14:47:32+08:00,cmd.Command,fwsys,**************,oa-migrate,************,cd,success
86b7b19567b42d050000000000000046,2025-02-18T14:47:33+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/';cd '/root';cd '/root';pwd;ls -la,success
9539109f67b42a840000000000000046,2025-02-18T14:47:44+08:00,cmd.Command,fwsys,**************,oa-migrate,************,mv jdk-8u151-linux-x64.tar.gz /opt/,success
86a5136667b42d100000000000000046,2025-02-18T14:47:44+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
71f56abd67b42d1a0000000000000046,2025-02-18T14:47:54+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
9539109f67b42a840000000000000046,2025-02-18T14:47:54+08:00,cmd.Command,fwsys,**************,oa-migrate,************,mv weaverFlink241226.zip /opt/,success
9539109f67b42a840000000000000046,2025-02-18T14:47:57+08:00,cmd.Command,fwsys,**************,oa-migrate,************,cd /opt/,success
67865db667b42d1d0000000000000046,2025-02-18T14:47:57+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
4f0c67df67b42d1e0000000000000046,2025-02-18T14:47:58+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/root/';cd '/opt';cd '/opt';pwd;ls -la,success
d41be73467b42d1f0000000000000046,2025-02-18T14:47:59+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
9539109f67b42a840000000000000046,2025-02-18T14:47:59+08:00,cmd.Command,fwsys,**************,oa-migrate,************,ll,success
fa63924367b42d280000000000000046,2025-02-18T14:48:08+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
9539109f67b42a840000000000000046,2025-02-18T14:48:08+08:00,cmd.Command,fwsys,**************,oa-migrate,************,tar -xvf jdk-8u151-linux-x64.tar.gz ,success
9539109f67b42a840000000000000046,2025-02-18T14:48:18+08:00,cmd.Command,fwsys,**************,oa-migrate,************,ll,success
cb209eeb67b42d330000000000000046,2025-02-18T14:48:19+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
99a2482e67b42d390000000000000046,2025-02-18T14:48:25+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/opt/';cd '/opt/jdk1.8.0_151';cd '/opt/jdk1.8.0_151';pwd;ls -la,success
d682e59167b42d390000000000000046,2025-02-18T14:48:25+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
9539109f67b42a840000000000000046,2025-02-18T14:48:25+08:00,cmd.Command,fwsys,**************,oa-migrate,************,cd jdk1.8.0_151/,success
9539109f67b42a840000000000000046,2025-02-18T14:50:45+08:00,cmd.Command,fwsys,**************,oa-migrate,************,cd ..,success
a22f141667b42dc50000000000000046,2025-02-18T14:50:45+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
abefa4c667b42dc50000000000000046,2025-02-18T14:50:45+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/opt/jdk1.8.0_151/';cd '/opt';cd '/opt';pwd;ls -la,success
f62e3efc67b42dce0000000000000046,2025-02-18T14:50:54+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
9539109f67b42a840000000000000046,2025-02-18T14:50:54+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unzip -o weaverFlink241226.zip ,success
ace23e0e67b42de60000000000000046,2025-02-18T14:51:18+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
9539109f67b42a840000000000000046,2025-02-18T14:51:18+08:00,cmd.Command,fwsys,**************,oa-migrate,************,apt install unzip,success
fa0c2a4d67b42e700000000000000046,2025-02-18T14:53:36+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
9539109f67b42a840000000000000046,2025-02-18T14:53:36+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unzip -o weaverFlink241226.zip ,success
9539109f67b42a840000000000000046,2025-02-18T14:53:40+08:00,cmd.Command,fwsys,**************,oa-migrate,************,ll,success
a9afb08467b42e750000000000000046,2025-02-18T14:53:41+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
4a38206467b42e780000000000000046,2025-02-18T14:53:44+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
5f9436c667b42e780000000000000046,2025-02-18T14:53:44+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/opt/';cd '/opt/weaverFlink';cd '/opt/weaverFlink';pwd;ls -la,success
9539109f67b42a840000000000000046,2025-02-18T14:53:44+08:00,cmd.Command,fwsys,**************,oa-migrate,************,cd weaverFlink/,success
9539109f67b42a840000000000000046,2025-02-18T14:53:45+08:00,cmd.Command,fwsys,**************,oa-migrate,************,ll,success
0d56572967b42e7a0000000000000046,2025-02-18T14:53:46+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
9539109f67b42a840000000000000046,2025-02-18T14:54:04+08:00,cmd.Command,fwsys,**************,oa-migrate,************,cd bin,success
5902538567b42e8c0000000000000046,2025-02-18T14:54:04+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
853ed50d67b42e8c0000000000000046,2025-02-18T14:54:04+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/opt/weaverFlink/';cd '/opt/weaverFlink/bin';cd '/opt/weaverFlink/bin';pwd;ls -la,success
db3b7d3267b42e940000000000000046,2025-02-18T14:54:12+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
9539109f67b42a840000000000000046,2025-02-18T14:54:12+08:00,cmd.Command,fwsys,**************,oa-migrate,************,vi jdk.ini ,success
f679eab867b42eb20000000000000046,2025-02-18T14:54:42+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
1c4ff36767b42ebc0000000000000046,2025-02-18T14:54:52+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
9539109f67b42a840000000000000046,2025-02-18T14:54:58+08:00,cmd.Command,fwsys,**************,oa-migrate,************,cd /opt/jdk1.8.0_151/,success
0a3eba8167b42ec20000000000000046,2025-02-18T14:54:58+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
34991dac67b42ec30000000000000046,2025-02-18T14:54:59+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/opt/weaverFlink/bin/';cd '/opt/jdk1.8.0_151';cd '/opt/jdk1.8.0_151';pwd;ls -la,success
9539109f67b42a840000000000000046,2025-02-18T14:55:00+08:00,cmd.Command,fwsys,**************,oa-migrate,************,pwd,success
247a3f0d67b42ec40000000000000046,2025-02-18T14:55:00+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
9539109f67b42a840000000000000046,2025-02-18T14:55:28+08:00,cmd.Command,fwsys,**************,oa-migrate,************,cd /opt/weaverFlink/bin/,success
70bc162667b42ee00000000000000046,2025-02-18T14:55:28+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/opt/jdk1.8.0_151/';cd '/opt/weaverFlink/bin';cd '/opt/weaverFlink/bin';pwd;ls -la,success
45ac256667b42ee00000000000000046,2025-02-18T14:55:28+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
9539109f67b42a840000000000000046,2025-02-18T14:55:31+08:00,cmd.Command,fwsys,**************,oa-migrate,************,vi jdk.ini ,success
dfa7adb967b42ee30000000000000046,2025-02-18T14:55:31+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
2c7fe02c67b42ef20000000000000046,2025-02-18T14:55:46+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
d823510167b42f1c0000000000000046,2025-02-18T14:56:28+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/opt/weaverFlink/bin/';cd '/opt';cd '/opt';pwd;ls -la,success
9539109f67b42a840000000000000046,2025-02-18T14:56:28+08:00,cmd.Command,fwsys,**************,oa-migrate,************,cd ../../,success
35a13bb567b42f1c0000000000000046,2025-02-18T14:56:28+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
9539109f67b42a840000000000000046,2025-02-18T14:56:53+08:00,cmd.Command,fwsys,**************,oa-migrate,************,chmod -R 777 weaverFlink/,success
bab9f54a67b42f350000000000000046,2025-02-18T14:56:53+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
9539109f67b42a840000000000000046,2025-02-18T14:56:55+08:00,cmd.Command,fwsys,**************,oa-migrate,************,ll,success
1a8bb80567b42f370000000000000046,2025-02-18T14:56:55+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
9539109f67b42a840000000000000046,2025-02-18T14:57:11+08:00,cmd.Command,fwsys,**************,oa-migrate,************,cd weaverFlink/bin,success
cc3eefa167b42f480000000000000046,2025-02-18T14:57:12+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
e0b4fdcb67b42f480000000000000046,2025-02-18T14:57:12+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/opt/';cd '/opt/weaverFlink/bin';cd '/opt/weaverFlink/bin';pwd;ls -la,success
1558606c67b42f490000000000000046,2025-02-18T14:57:13+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
9539109f67b42a840000000000000046,2025-02-18T14:57:13+08:00,cmd.Command,fwsys,**************,oa-migrate,************,ll,success
1afb255067b42f650000000000000046,2025-02-18T14:57:41+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
9539109f67b42a840000000000000046,2025-02-18T14:57:41+08:00,cmd.Command,fwsys,**************,oa-migrate,************,./start-cluster.sh ,success
ca37603667b43bd00000000000000042,2025-02-18T15:52:43+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,crontab -l |grep replenishment_address_scheduler,success
ca37603667b43bd00000000000000042,2025-02-18T15:52:54+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,cd /data/arkham_data_monitor/arkham_data_monitor,success
ca37603667b43bd00000000000000042,2025-02-18T15:52:56+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,ls,success
ca37603667b43bd00000000000000042,2025-02-18T15:57:34+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,ls,success
ca37603667b43bd00000000000000042,2025-02-18T15:57:37+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,cd utils/,success
ca37603667b43bd00000000000000042,2025-02-18T15:57:38+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,ls,success
ca37603667b43bd00000000000000042,2025-02-18T15:57:41+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,vim config.py ,success
ca37603667b43bd00000000000000042,2025-02-18T15:57:54+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,cd ..,success
ca37603667b43bd00000000000000042,2025-02-18T15:57:55+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,ls,success
ca37603667b43bd00000000000000042,2025-02-18T15:57:57+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,vim .env,success
ca37603667b43bd00000000000000042,2025-02-18T15:58:57+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,vim .env,success
ca37603667b43bd00000000000000042,2025-02-18T15:59:43+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,poetry run python replenishment_address_scheduler.py,success
ca37603667b43bd00000000000000042,2025-02-18T16:00:02+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,ls,success
ca37603667b43bd00000000000000042,2025-02-18T16:00:10+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,crontab -l,success
ca37603667b43bd00000000000000042,2025-02-18T16:00:17+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,crontab -l|grep gov,success
ca37603667b43bd00000000000000042,2025-02-18T16:00:23+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,vim run_gov_v2.sh ,success
ca37603667b43bd00000000000000042,2025-02-18T16:01:12+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,crontab -l,success
ca37603667b43bd00000000000000042,2025-02-18T16:01:16+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,crontab -l|grep gov,success
ca37603667b43bd00000000000000042,2025-02-18T16:01:24+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,source /data/venv/bin/activate,success
ca37603667b43bd00000000000000042,2025-02-18T16:01:29+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,poetry run python replenishment_address_scheduler.py,success
cffbc3ee67b4428f0000000000000046,2025-02-18T16:19:27+08:00,cmd.Command,fwsys,**************,oa-migrate,************,while true; do sleep 1;head -v -n 8 /proc/meminfo; head -v -n 2 /proc/stat /proc/version /proc/uptime /proc/loadavg /proc/sys/fs/file-nr /proc/sys/kernel/hostname; tail -v -n 32 /proc/net/dev;echo '==> /proc/df <==';df -l;echo '==> /proc/who <==';who;echo '==> /proc/end <==';echo '##Moba##'; done,success
d632adac67b442910000000000000046,2025-02-18T16:19:29+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/opt/weaverFlink/bin/';cd '/root';cd '/root';pwd;ls -la,success
01aa63d767b442910000000000000046,2025-02-18T16:19:29+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
9539109f67b42a840000000000000046,2025-02-18T16:19:29+08:00,cmd.Command,fwsys,**************,oa-migrate,************,cd,success
9539109f67b42a840000000000000046,2025-02-18T16:19:37+08:00,cmd.Command,fwsys,**************,oa-migrate,************,exit,success
8c580c2f67b442990000000000000046,2025-02-18T16:19:37+08:00,cmd.Command,fwsys,**************,oa-migrate,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 31795; ls -lad /proc/31795/cwd,success
6f34ef4467b442e60000000000000046,2025-02-18T16:20:58+08:00,cmd.Command,fwsys,**************,E10-app,************,curl http://************:9081,success
ca37603667b43bd00000000000000042,2025-02-18T16:44:14+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,curl -X GET "https://api.arkhamintelligence.com/transfers?base=alameda-research&to=ftx,deposit:ftx&usdGte=1000000" -H "API-Key: WnmJ7j6rvp78DPj2d1XrtV9wwRE1PJHY",success
ca37603667b43bd00000000000000042,2025-02-18T16:50:23+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,poetry run python replenishment_address_scheduler.py,success
ca37603667b43bd00000000000000042,2025-02-18T17:13:42+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,cd ..,success
ca37603667b43bd00000000000000042,2025-02-18T17:13:45+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,git branch,success
ca37603667b43bd00000000000000042,2025-02-18T17:13:48+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,git pull,success
ca37603667b43bd00000000000000042,2025-02-18T17:13:58+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,cd arkham_data_monitor/,success
ca37603667b43bd00000000000000042,2025-02-18T17:14:01+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,poetry run python replenishment_address_scheduler.py,success
ca37603667b43bd00000000000000042,2025-02-18T17:18:41+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,poetry run python replenishment_address_scheduler.py,success
ca37603667b43bd00000000000000042,2025-02-18T17:19:56+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,poetry run python replenishment_address_transfer_and_label_scheduler.py,success
b21541c267b453230000000000000046,2025-02-18T17:30:11+08:00,cmd.Command,fwsys,**************,E10-app,************,while true; do sleep 1;head -v -n 8 /proc/meminfo; head -v -n 2 /proc/stat /proc/version /proc/uptime /proc/loadavg /proc/sys/fs/file-nr /proc/sys/kernel/hostname; tail -v -n 32 /proc/net/dev;echo '==> /proc/df <==';df -l;echo '==> /proc/who <==';who;echo '==> /proc/end <==';echo '##Moba##'; done,success
c81746db67b453230000000000000046,2025-02-18T17:30:11+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '.';cd '.';cd '.';pwd;ls -la,success
6f34ef4467b442e60000000000000046,2025-02-18T17:30:16+08:00,cmd.Command,fwsys,**************,E10-app,************,cd /data/,success
6f34ef4467b442e60000000000000046,2025-02-18T17:30:17+08:00,cmd.Command,fwsys,**************,E10-app,************,ll,success
6f34ef4467b442e60000000000000046,2025-02-18T17:30:20+08:00,cmd.Command,fwsys,**************,E10-app,************,cd weaver/,success
6f34ef4467b442e60000000000000046,2025-02-18T17:30:22+08:00,cmd.Command,fwsys,**************,E10-app,************,ll,success
6f34ef4467b442e60000000000000046,2025-02-18T17:30:34+08:00,cmd.Command,fwsys,**************,E10-app,************,cd /data/monitor/,success
6f34ef4467b442e60000000000000046,2025-02-18T17:30:38+08:00,cmd.Command,fwsys,**************,E10-app,************,ll,success
6f34ef4467b442e60000000000000046,2025-02-18T17:30:48+08:00,cmd.Command,fwsys,**************,E10-app,************,cd ..,success
6f34ef4467b442e60000000000000046,2025-02-18T17:30:50+08:00,cmd.Command,fwsys,**************,E10-app,************,cd,success
ca37603667b43bd00000000000000042,2025-02-18T17:37:41+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,poetry run python replenishment_address_scheduler.py,success
22fb8a1c67b53b4e0000000000000019,2025-02-19T10:00:55+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com ,success
22fb8a1c67b53b4e0000000000000019,2025-02-19T10:01:19+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use proamsdwdb;,success
22fb8a1c67b53b4e0000000000000019,2025-02-19T10:01:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
22fb8a1c67b53b4e0000000000000019,2025-02-19T10:01:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_state in(4,3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
22fb8a1c67b53b4e0000000000000019,2025-02-19T10:01:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,group by f_currency;,success
22fb8a1c67b53b4e0000000000000019,2025-02-19T10:30:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use hbg_trade; ,success
22fb8a1c67b53b4e0000000000000019,2025-02-19T10:31:01+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_day like '202502%',success
22fb8a1c67b53b4e0000000000000019,2025-02-19T10:31:01+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_state, count(*) as user_count,success
22fb8a1c67b53b4e0000000000000019,2025-02-19T10:31:01+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from t_account_manage_fee_snapshot  ,success
22fb8a1c67b53b4e0000000000000019,2025-02-19T10:31:02+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,group by f_state;,success
22fb8a1c67b53b4e0000000000000019,2025-02-19T10:44:10+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_currency,sum(f_amount),success
22fb8a1c67b53b4e0000000000000019,2025-02-19T10:44:10+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from t_account_manage_fee_snapshot,success
22fb8a1c67b53b4e0000000000000019,2025-02-19T10:44:10+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_day like '202502%'  and f_state = 1 ,success
22fb8a1c67b53b4e0000000000000019,2025-02-19T10:44:10+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,and f_currency in ('btc', 'eth', 'tfuel', 'usdt', 'doge', 'shib', 'xrp', 'trx', 'ltc', 'ada', 'fil', 'elf', 'eos', 'ong', 'etc', 'cvnt', 'btt', 'bsv', 'link', 'dot', 'nft', 'theta', 'mx', 'uni'),success
22fb8a1c67b53b4e0000000000000019,2025-02-19T10:44:10+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,group by f_currency order by f_currency;,success
22fb8a1c67b53b4e0000000000000019,2025-02-19T10:44:11+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,and f_currency in ,success
22fb8a1c67b53b4e0000000000000019,2025-02-19T11:13:36+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_currency,sum(f_amount),success
22fb8a1c67b53b4e0000000000000019,2025-02-19T11:13:36+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from t_account_manage_fee_snapshot,success
22fb8a1c67b53b4e0000000000000019,2025-02-19T11:13:36+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_day like '202502%'  and f_state = 1 ,success
22fb8a1c67b53b4e0000000000000019,2025-02-19T11:13:36+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,group by f_currency order by f_currency;,success
22fb8a1c67b53b4e0000000000000019,2025-02-19T11:18:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
22fb8a1c67b53b4e0000000000000019,2025-02-19T11:18:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
986cf8a467b576ef0000000000000042,2025-02-19T14:15:19+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,cd /data/arkham_data_monitor/,success
986cf8a467b576ef0000000000000042,2025-02-19T14:15:21+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,cd arkham_data_monitor/,success
986cf8a467b576ef0000000000000042,2025-02-19T14:15:38+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,crontab -l,success
986cf8a467b576ef0000000000000042,2025-02-19T14:15:42+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,crontab -l|grep gov,success
986cf8a467b576ef0000000000000042,2025-02-19T14:15:50+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,source /data/venv/bin/activate,success
986cf8a467b576ef0000000000000042,2025-02-19T14:16:22+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,cd ..,success
986cf8a467b576ef0000000000000042,2025-02-19T14:16:24+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,git pull,success
986cf8a467b576ef0000000000000042,2025-02-19T14:17:53+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,git pull,success
986cf8a467b576ef0000000000000042,2025-02-19T14:18:30+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,git pull,success
986cf8a467b576ef0000000000000042,2025-02-19T14:18:35+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,cd arkham_data_monitor/,success
986cf8a467b576ef0000000000000042,2025-02-19T14:18:47+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,poetry run python replenishment_address_transfer_and_label_scheduler.py,success
986cf8a467b576ef0000000000000042,2025-02-19T14:25:02+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,cd ..,success
986cf8a467b576ef0000000000000042,2025-02-19T14:25:04+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,git pull,success
986cf8a467b576ef0000000000000042,2025-02-19T14:25:08+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,cd arkham_data_monitor/,success
986cf8a467b576ef0000000000000042,2025-02-19T14:25:12+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,poetry run python replenishment_address_transfer_and_label_scheduler.py,success
986cf8a467b576ef0000000000000042,2025-02-19T15:00:58+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,cd ..,success
986cf8a467b576ef0000000000000042,2025-02-19T15:01:00+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,git pull,success
986cf8a467b576ef0000000000000042,2025-02-19T15:01:06+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,cd arkham_data_monitor/,success
986cf8a467b576ef0000000000000042,2025-02-19T15:01:15+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,vim replenishment_address_scheduler_repair.py,success
986cf8a467b576ef0000000000000042,2025-02-19T15:02:23+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,poetry run python replenishment_address_scheduler_repair.py,success
986cf8a467b576ef0000000000000042,2025-02-19T15:02:43+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,ls,success
986cf8a467b576ef0000000000000042,2025-02-19T15:02:51+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,poetry run python replenishment_address_scheduler_repair.py,success
986cf8a467b576ef0000000000000042,2025-02-19T15:03:15+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,vim run_gov_v2.sh ,success
986cf8a467b576ef0000000000000042,2025-02-19T15:03:46+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,crontab -l|grep gov,success
986cf8a467b576ef0000000000000042,2025-02-19T15:04:14+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,poetry run python replenishment_address_scheduler_repair.py ,success
986cf8a467b576ef0000000000000042,2025-02-19T15:12:26+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,git status,success
986cf8a467b576ef0000000000000042,2025-02-19T15:12:40+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,git diff replenishment_address_scheduler_repair.py,success
986cf8a467b576ef0000000000000042,2025-02-19T15:12:50+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,git checkout replenishment_address_scheduler_repair.py,success
986cf8a467b576ef0000000000000042,2025-02-19T16:34:07+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,vim replenishment_address_scheduler_repair.py,success
986cf8a467b576ef0000000000000042,2025-02-19T16:34:32+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,poetry run python replenishment_address_scheduler_repair.py ,success
986cf8a467b576ef0000000000000042,2025-02-19T16:34:36+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,vim replenishment_address_scheduler_repair.py,success
986cf8a467b576ef0000000000000042,2025-02-19T16:35:02+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,poetry run python replenishment_address_scheduler_repair.py ,success
986cf8a467b576ef0000000000000042,2025-02-19T16:35:07+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,vim replenishment_address_scheduler_repair.py,success
986cf8a467b576ef0000000000000042,2025-02-19T16:35:18+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,vim replenishment_address_scheduler_repair.py,success
986cf8a467b576ef0000000000000042,2025-02-19T16:35:30+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,poetry run python replenishment_address_scheduler_repair.py ,success
986cf8a467b576ef0000000000000042,2025-02-19T16:35:37+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,vim replenishment_address_scheduler_repair.py,success
986cf8a467b576ef0000000000000042,2025-02-19T16:36:05+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,poetry run python replenishment_address_scheduler_repair.py ,success
986cf8a467b576ef0000000000000042,2025-02-19T17:34:53+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,cd,success
986cf8a467b576ef0000000000000042,2025-02-19T17:34:55+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,cd /data/data-spider/,success
986cf8a467b576ef0000000000000042,2025-02-19T17:34:57+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,ls,success
986cf8a467b576ef0000000000000042,2025-02-19T17:35:04+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,vim .env ,success
f4c3c56967b68b530000000000000019,2025-02-20T09:54:34+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
f4c3c56967b68b530000000000000019,2025-02-20T09:54:50+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use proamsdwdb;,success
f4c3c56967b68b530000000000000019,2025-02-20T09:55:01+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
f4c3c56967b68b530000000000000019,2025-02-20T09:55:01+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_state in(4,3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
f4c3c56967b68b530000000000000019,2025-02-20T09:55:01+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w group by f_currency;,success
f4c3c56967b68b530000000000000019,2025-02-20T11:29:30+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
f4c3c56967b68b530000000000000019,2025-02-20T11:29:31+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
32732cc267b6bf860000000000000042,2025-02-20T13:37:13+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,crontab -l,success
32732cc267b6bf860000000000000042,2025-02-20T13:37:16+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,crontab -e,success
32732cc267b6bf860000000000000042,2025-02-20T13:37:37+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,crontab -l,success
0b9c36db67b7211f0000000000000042,2025-02-20T20:33:39+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,curl "https://api.glassnode.com/v2/metrics/endpoints?api_key=***************************",success
0b9c36db67b7211f0000000000000042,2025-02-20T20:34:04+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,time curl "https://api.glassnode.com/v2/metrics/endpoints?api_key=***************************",success
0b9c36db67b7211f0000000000000042,2025-02-20T20:42:42+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,curl "https://api.glassnode.com/v2/metrics/endpoints?api_key=***************************" > 1.txt,success
0b9c36db67b7211f0000000000000042,2025-02-20T20:42:48+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,ls,success
0b9c36db67b7211f0000000000000042,2025-02-20T20:42:56+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,vim 1.txt ,success
a908affa67b8460f000000000000001e,2025-02-21T17:23:55+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,telnet ************ 10000,success
a908affa67b8460f000000000000001e,2025-02-21T17:25:39+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,sudo yum update -y,success
a908affa67b8460f000000000000001e,2025-02-21T17:27:42+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,sudo yum install -y telnet,success
a908affa67b8460f000000000000001e,2025-02-21T17:27:47+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,telnet ************ 10000,success
a908affa67b8460f000000000000001e,2025-02-21T17:27:51+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,^C,success
a908affa67b8460f000000000000001e,2025-02-21T17:27:59+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,telnet ************ 10000,success
a908affa67b8460f000000000000001e,2025-02-21T17:29:00+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,ping 10.165.60.100,success
a908affa67b8460f000000000000001e,2025-02-21T17:29:22+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,telnet 10.165.60.100 80,success
a908affa67b8460f000000000000001e,2025-02-21T17:35:59+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,telnet 10.173.252.2 80,success
a908affa67b8460f000000000000001e,2025-02-21T17:36:04+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,ping 10.173.252.2,success
a908affa67b8460f000000000000001e,2025-02-21T17:39:48+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,exit,success
dec8f07d67b851dd0000000000000042,2025-02-21T18:13:52+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,cd /data/data-spider/,success
dec8f07d67b851dd0000000000000042,2025-02-21T18:13:55+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,vim .env ,success
f3569d0267b88fe60000000000000046,2025-02-21T22:38:30+08:00,cmd.Command,fwsys,**************,E10-app,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '.';cd '.';cd '.';pwd;ls -la,success
746b129a67bbdc50000000000000003a,2025-02-24T10:41:30+08:00,cmd.Command,zengshengfa,**************,snipe-it,************,df -Th,success
746b129a67bbdc50000000000000003a,2025-02-24T10:45:07+08:00,cmd.Command,zengshengfa,**************,snipe-it,************,top,success
8047f20467bbe2e7000000000000003a,2025-02-24T11:09:27+08:00,cmd.Command,zengshengfa,**************,snipe-it,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '.';cd '.';cd '.';pwd;ls -la,success
746b129a67bbdc50000000000000003a,2025-02-24T16:43:19+08:00,cmd.Command,zengshengfa,**************,snipe-it,************,netstat -tunpl,success
746b129a67bbdc50000000000000003a,2025-02-24T16:43:26+08:00,cmd.Command,zengshengfa,**************,snipe-it,************,nginx -t,success
9d6ecdaf67be910f0000000000000019,2025-02-26T11:57:12+08:00,cmd.Command,xiexinyu,**************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
9d6ecdaf67be910f0000000000000019,2025-02-26T11:57:30+08:00,cmd.Command,xiexinyu,**************,miniglobal-devops,***********,use proamsdwdb;,success
9d6ecdaf67be910f0000000000000019,2025-02-26T11:57:43+08:00,cmd.Command,xiexinyu,**************,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
9d6ecdaf67be910f0000000000000019,2025-02-26T11:57:43+08:00,cmd.Command,xiexinyu,**************,miniglobal-devops,***********,where f_state in(4,3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
9d6ecdaf67be910f0000000000000019,2025-02-26T11:57:43+08:00,cmd.Command,xiexinyu,**************,miniglobal-devops,***********,'2022-11-01group by f_currency;,success
9d6ecdaf67be910f0000000000000019,2025-02-26T12:15:39+08:00,cmd.Command,xiexinyu,**************,miniglobal-devops,***********,exit,success
9d6ecdaf67be910f0000000000000019,2025-02-26T12:15:40+08:00,cmd.Command,xiexinyu,**************,miniglobal-devops,***********,exit,success
84a5d28667bebaf60000000000000046,2025-02-26T14:55:56+08:00,cmd.Command,fwsys,**************,E10-app,************,cd /data/,success
84a5d28667bebaf60000000000000046,2025-02-26T14:55:57+08:00,cmd.Command,fwsys,**************,E10-app,************,ll,success
84a5d28667bebaf60000000000000046,2025-02-26T14:56:01+08:00,cmd.Command,fwsys,**************,E10-app,************,cd monitor/,success
8743c81d67bed4f20000000000000042,2025-02-26T16:46:47+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,cd /data/data-spider/,success
8743c81d67bed4f20000000000000042,2025-02-26T16:46:50+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,git branch,success
8743c81d67bed4f20000000000000042,2025-02-26T16:47:07+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,crontab -l |grep market_kline_day,success
8743c81d67bed4f20000000000000042,2025-02-26T16:47:27+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,ls,success
8743c81d67bed4f20000000000000042,2025-02-26T16:47:35+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,vim log/market_kline_day.log ,success
8743c81d67bed4f20000000000000042,2025-02-26T16:48:07+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,crontab -e,success
8743c81d67bed4f20000000000000042,2025-02-26T16:48:44+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,ls,success
8743c81d67bed4f20000000000000042,2025-02-26T16:48:48+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,crontab -l |grep market_kline_day,success
7b4e8bd867bee059000000000000003a,2025-02-26T17:35:37+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,传到,success
7b4e8bd867bee059000000000000003a,2025-02-26T17:35:48+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,cd /opt/uchome/local/,success
7b4e8bd867bee059000000000000003a,2025-02-26T17:35:49+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,ls,success
7b4e8bd867bee059000000000000003a,2025-02-26T17:35:52+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,more defaultApp.conf ,success
7b4e8bd867bee059000000000000003a,2025-02-26T17:36:17+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,cd ~,success
7b4e8bd867bee059000000000000003a,2025-02-26T17:36:19+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,ls,success
7b4e8bd867bee059000000000000003a,2025-02-26T17:36:22+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,cd update/,success
7b4e8bd867bee059000000000000003a,2025-02-26T17:36:23+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,ls,success
7b4e8bd867bee059000000000000003a,2025-02-26T17:36:34+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,mkdir 20250226,success
7b4e8bd867bee059000000000000003a,2025-02-26T17:36:41+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,cd 20250226/,success
7b4e8bd867bee059000000000000003a,2025-02-26T17:36:42+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,ls,success
7b4e8bd867bee059000000000000003a,2025-02-26T17:36:49+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,mkdir bak,success
7b4e8bd867bee059000000000000003a,2025-02-26T17:36:52+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,cd bak/,success
7b4e8bd867bee059000000000000003a,2025-02-26T17:36:53+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,ls,success
7b4e8bd867bee059000000000000003a,2025-02-26T17:37:21+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,cp /opt/uchome/local/defaultApp.conf ./,success
7b4e8bd867bee059000000000000003a,2025-02-26T17:37:22+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,ls,success
7b4e8bd867bee059000000000000003a,2025-02-26T17:37:26+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,cat defaultApp.conf ,success
7b4e8bd867bee059000000000000003a,2025-02-26T17:50:31+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,ls,success
7b4e8bd867bee059000000000000003a,2025-02-26T17:51:02+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,touch dir.txt,success
7b4e8bd867bee059000000000000003a,2025-02-26T17:51:08+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,vi dir.txt ,success
7b4e8bd867bee059000000000000003a,2025-02-26T17:51:22+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,vi /opt/uchome/local/defaultApp.conf,success
7b4e8bd867bee059000000000000003a,2025-02-26T17:51:41+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,exit,success
731c1d8767bee434000000000000003a,2025-02-26T17:52:02+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,ls,success
731c1d8767bee434000000000000003a,2025-02-26T17:52:05+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,cd update/,success
731c1d8767bee434000000000000003a,2025-02-26T17:52:07+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,ls,success
84a5d28667bebaf60000000000000046,2025-02-26T17:52:08+08:00,cmd.Command,fwsys,**************,E10-app,************,cd weaver-im/,success
84a5d28667bebaf60000000000000046,2025-02-26T17:52:09+08:00,cmd.Command,fwsys,**************,E10-app,************,ls,success
84a5d28667bebaf60000000000000046,2025-02-26T17:52:11+08:00,cmd.Command,fwsys,**************,E10-app,************,sh bin/check.sh ,success
84a5d28667bebaf60000000000000046,2025-02-26T17:52:16+08:00,cmd.Command,fwsys,**************,E10-app,************,bash bin/check.sh ,success
731c1d8767bee434000000000000003a,2025-02-26T17:52:27+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,mkdir 20250226,success
84a5d28667bebaf60000000000000046,2025-02-26T17:52:29+08:00,cmd.Command,fwsys,**************,E10-app,************,vi log/weaver-im-async-3.log,success
731c1d8767bee434000000000000003a,2025-02-26T17:52:31+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,cd 20250226/,success
731c1d8767bee434000000000000003a,2025-02-26T17:52:37+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,mkdir bak,success
731c1d8767bee434000000000000003a,2025-02-26T17:52:40+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,cd bak,success
731c1d8767bee434000000000000003a,2025-02-26T17:52:51+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,cp /opt/uchome/local/defaultApp.conf ./,success
731c1d8767bee434000000000000003a,2025-02-26T17:52:52+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,ls,success
731c1d8767bee434000000000000003a,2025-02-26T17:53:08+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,cdt defaultApp.conf ,success
731c1d8767bee434000000000000003a,2025-02-26T17:53:13+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,cat defaultApp.conf ,success
731c1d8767bee434000000000000003a,2025-02-26T17:53:18+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,vi /opt/uchome/local/defaultApp.conf,success
84a5d28667bebaf60000000000000046,2025-02-26T17:53:18+08:00,cmd.Command,fwsys,**************,E10-app,************,netstat -anp | grep 32700,success
84a5d28667bebaf60000000000000046,2025-02-26T17:53:32+08:00,cmd.Command,fwsys,**************,E10-app,************,vi webapps/ROOT/WEB-INF/classes/weaver/config/config-center/weaver-im-fix.properties ,success
84a5d28667bebaf60000000000000046,2025-02-26T17:53:52+08:00,cmd.Command,fwsys,**************,E10-app,************,bash weaver-im-async/start.sh ,success
84a5d28667bebaf60000000000000046,2025-02-26T17:54:00+08:00,cmd.Command,fwsys,**************,E10-app,************,vi webapps/ROOT/WEB-INF/classes/weaver/config/config-center/weaver-im-fix.properties ,success
84a5d28667bebaf60000000000000046,2025-02-26T17:54:11+08:00,cmd.Command,fwsys,**************,E10-app,************,netstat -anp | grep 25222,success
84a5d28667bebaf60000000000000046,2025-02-26T17:54:16+08:00,cmd.Command,fwsys,**************,E10-app,************,netstat -anp | grep 27070,success
84a5d28667bebaf60000000000000046,2025-02-26T17:54:42+08:00,cmd.Command,fwsys,**************,E10-app,************,ls,success
84a5d28667bebaf60000000000000046,2025-02-26T17:54:52+08:00,cmd.Command,fwsys,**************,E10-app,************,ps -ef|grep nginx,success
84a5d28667bebaf60000000000000046,2025-02-26T17:55:01+08:00,cmd.Command,fwsys,**************,E10-app,************,cd /opt/weasl,success
84a5d28667bebaf60000000000000046,2025-02-26T17:55:02+08:00,cmd.Command,fwsys,**************,E10-app,************,ls,success
84a5d28667bebaf60000000000000046,2025-02-26T17:55:08+08:00,cmd.Command,fwsys,**************,E10-app,************,cd /opt/weaver,success
84a5d28667bebaf60000000000000046,2025-02-26T17:55:23+08:00,cmd.Command,fwsys,**************,E10-app,************,ls,success
84a5d28667bebaf60000000000000046,2025-02-26T17:55:24+08:00,cmd.Command,fwsys,**************,E10-app,************,cd ..,success
84a5d28667bebaf60000000000000046,2025-02-26T17:55:25+08:00,cmd.Command,fwsys,**************,E10-app,************,ls,success
84a5d28667bebaf60000000000000046,2025-02-26T17:55:26+08:00,cmd.Command,fwsys,**************,E10-app,************,cd nginx/,success
84a5d28667bebaf60000000000000046,2025-02-26T17:55:27+08:00,cmd.Command,fwsys,**************,E10-app,************,ls,success
84a5d28667bebaf60000000000000046,2025-02-26T17:55:37+08:00,cmd.Command,fwsys,**************,E10-app,************,grep 27070 ./conf.d/*,success
84a5d28667bebaf60000000000000046,2025-02-26T17:55:55+08:00,cmd.Command,fwsys,**************,E10-app,************,vi ./conf.d/7070.conf ,success
84a5d28667bebaf60000000000000046,2025-02-26T17:56:07+08:00,cmd.Command,fwsys,**************,E10-app,************,netstat -anp | grep 27070,success
84a5d28667bebaf60000000000000046,2025-02-26T17:56:14+08:00,cmd.Command,fwsys,**************,E10-app,************,netstat -anp | grep 25222,success
84a5d28667bebaf60000000000000046,2025-02-26T17:56:44+08:00,cmd.Command,fwsys,**************,E10-app,************,ps -ef |grep e-monito,success
84a5d28667bebaf60000000000000046,2025-02-26T17:56:53+08:00,cmd.Command,fwsys,**************,E10-app,************,pwd,success
84a5d28667bebaf60000000000000046,2025-02-26T17:57:00+08:00,cmd.Command,fwsys,**************,E10-app,************,cd ../,success
84a5d28667bebaf60000000000000046,2025-02-26T17:57:01+08:00,cmd.Command,fwsys,**************,E10-app,************,ls,success
84a5d28667bebaf60000000000000046,2025-02-26T17:57:21+08:00,cmd.Command,fwsys,**************,E10-app,************,cd e-monitor,success
84a5d28667bebaf60000000000000046,2025-02-26T17:57:25+08:00,cmd.Command,fwsys,**************,E10-app,************,l,success
84a5d28667bebaf60000000000000046,2025-02-26T17:57:29+08:00,cmd.Command,fwsys,**************,E10-app,************,cd app/,success
84a5d28667bebaf60000000000000046,2025-02-26T17:57:30+08:00,cmd.Command,fwsys,**************,E10-app,************,ls,success
84a5d28667bebaf60000000000000046,2025-02-26T17:58:12+08:00,cmd.Command,fwsys,**************,E10-app,************,cd importantConfig/,success
84a5d28667bebaf60000000000000046,2025-02-26T17:58:13+08:00,cmd.Command,fwsys,**************,E10-app,************,ls,success
84a5d28667bebaf60000000000000046,2025-02-26T17:58:15+08:00,cmd.Command,fwsys,**************,E10-app,************,ls,success
84a5d28667bebaf60000000000000046,2025-02-26T17:58:17+08:00,cmd.Command,fwsys,**************,E10-app,************,ls,success
84a5d28667bebaf60000000000000046,2025-02-26T17:58:29+08:00,cmd.Command,fwsys,**************,E10-app,************,cd ../,success
84a5d28667bebaf60000000000000046,2025-02-26T17:58:29+08:00,cmd.Command,fwsys,**************,E10-app,************,ls,success
84a5d28667bebaf60000000000000046,2025-02-26T17:58:39+08:00,cmd.Command,fwsys,**************,E10-app,************,cd imServerTmp/,success
84a5d28667bebaf60000000000000046,2025-02-26T17:58:40+08:00,cmd.Command,fwsys,**************,E10-app,************,ls,success
84a5d28667bebaf60000000000000046,2025-02-26T17:58:50+08:00,cmd.Command,fwsys,**************,E10-app,************,cd nacos/,success
84a5d28667bebaf60000000000000046,2025-02-26T17:58:50+08:00,cmd.Command,fwsys,**************,E10-app,************,ls,success
84a5d28667bebaf60000000000000046,2025-02-26T17:58:51+08:00,cmd.Command,fwsys,**************,E10-app,************,ls,success
84a5d28667bebaf60000000000000046,2025-02-26T17:58:57+08:00,cmd.Command,fwsys,**************,E10-app,************,rm -rf ./*,success
84a5d28667bebaf60000000000000046,2025-02-26T17:58:58+08:00,cmd.Command,fwsys,**************,E10-app,************,ls,success
84a5d28667bebaf60000000000000046,2025-02-26T17:58:58+08:00,cmd.Command,fwsys,**************,E10-app,************,cd ..,success
84a5d28667bebaf60000000000000046,2025-02-26T17:58:59+08:00,cmd.Command,fwsys,**************,E10-app,************,cd ..,success
84a5d28667bebaf60000000000000046,2025-02-26T17:59:00+08:00,cmd.Command,fwsys,**************,E10-app,************,ls,success
84a5d28667bebaf60000000000000046,2025-02-26T17:59:01+08:00,cmd.Command,fwsys,**************,E10-app,************,cd ..,success
84a5d28667bebaf60000000000000046,2025-02-26T17:59:01+08:00,cmd.Command,fwsys,**************,E10-app,************,ls,success
84a5d28667bebaf60000000000000046,2025-02-26T17:59:02+08:00,cmd.Command,fwsys,**************,E10-app,************,cd .,success
84a5d28667bebaf60000000000000046,2025-02-26T17:59:02+08:00,cmd.Command,fwsys,**************,E10-app,************,ls,success
84a5d28667bebaf60000000000000046,2025-02-26T17:59:03+08:00,cmd.Command,fwsys,**************,E10-app,************,ls,success
84a5d28667bebaf60000000000000046,2025-02-26T17:59:05+08:00,cmd.Command,fwsys,**************,E10-app,************,cd ..,success
84a5d28667bebaf60000000000000046,2025-02-26T17:59:06+08:00,cmd.Command,fwsys,**************,E10-app,************,ls,success
84a5d28667bebaf60000000000000046,2025-02-26T17:59:08+08:00,cmd.Command,fwsys,**************,E10-app,************,cd weaver-im/,success
84a5d28667bebaf60000000000000046,2025-02-26T17:59:08+08:00,cmd.Command,fwsys,**************,E10-app,************,ls,success
84a5d28667bebaf60000000000000046,2025-02-26T17:59:12+08:00,cmd.Command,fwsys,**************,E10-app,************,netstat -anp | grep 27070,success
84a5d28667bebaf60000000000000046,2025-02-26T17:59:20+08:00,cmd.Command,fwsys,**************,E10-app,************,netstat -anp | grep 252222,success
84a5d28667bebaf60000000000000046,2025-02-26T17:59:21+08:00,cmd.Command,fwsys,**************,E10-app,************,netstat -anp | grep 25222,success
731c1d8767bee434000000000000003a,2025-02-26T18:03:56+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,cat /opt/uchome/local/defaultApp.conf,success
731c1d8767bee434000000000000003a,2025-02-26T18:03:59+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,exit,success
c891f5aa67bee715000000000000003a,2025-02-26T18:04:10+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-AM-test,172.16.17.39,cat /opt/uchome/local/defaultApp.conf,success
c891f5aa67bee715000000000000003a,2025-02-26T18:06:44+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-AM-test,172.16.17.39,cd /opt/uchome/,success
c891f5aa67bee715000000000000003a,2025-02-26T18:06:45+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-AM-test,172.16.17.39,ls,success
c891f5aa67bee715000000000000003a,2025-02-26T18:06:55+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-AM-test,172.16.17.39,more shutdown.sh ,success
20d25c0767bef20d000000000000003a,2025-02-26T18:50:53+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-AM-test,172.16.17.39,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '.';cd '.';cd '.';pwd;ls -la,success
c891f5aa67bee715000000000000003a,2025-02-26T18:50:54+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-AM-test,172.16.17.39,exit,success
ad28874167bef215000000000000003a,2025-02-26T18:51:04+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-AM,172.16.17.26,df -Th,success
ad28874167bef215000000000000003a,2025-02-26T18:51:12+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-AM,172.16.17.26,exit,success
5fa77add67bef227000000000000003a,2025-02-26T20:23:37+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,cd /opt/uchome/,success
5fa77add67bef227000000000000003a,2025-02-26T20:23:38+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,ls,success
5fa77add67bef227000000000000003a,2025-02-26T20:24:28+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,exit,success
2c6ceeae67bf0800000000000000003a,2025-02-26T20:24:38+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,cd /opt/uchome/,success
2c6ceeae67bf0800000000000000003a,2025-02-26T20:24:39+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,ls,success
2c6ceeae67bf0800000000000000003a,2025-02-26T20:24:57+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,./shutdown.sh ,success
2c6ceeae67bf0800000000000000003a,2025-02-26T20:25:02+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,./startup.sh ,success
2c6ceeae67bf0800000000000000003a,2025-02-26T20:25:12+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,ip addr,success
2c6ceeae67bf0800000000000000003a,2025-02-26T20:25:23+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,netstat -tunpl,success
2c6ceeae67bf0800000000000000003a,2025-02-26T20:26:49+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,exit,success
c304396767bf088e000000000000003a,2025-02-26T20:26:56+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,ip addr,success
c304396767bf088e000000000000003a,2025-02-26T20:28:55+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,exi,success
c304396767bf088e000000000000003a,2025-02-26T20:28:58+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,exit,success
88b0a19167bf090e000000000000003a,2025-02-26T20:29:15+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,ip add,success
88b0a19167bf090e000000000000003a,2025-02-26T20:29:30+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,curl http://************:7080,success
88b0a19167bf090e000000000000003a,2025-02-26T20:29:36+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,curl -v http://************:7080,success
88b0a19167bf090e000000000000003a,2025-02-26T20:30:24+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,curl -v http://************:7080/uc/main/release/html/index.html,success
88b0a19167bf090e000000000000003a,2025-02-26T20:31:16+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,exit,success
b284cb4a67bf0998000000000000003a,2025-02-26T20:31:23+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,ip addr,success
b284cb4a67bf0998000000000000003a,2025-02-26T20:31:49+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,curl http://************:7080/uc/main/release/html/index.html,success
b284cb4a67bf0998000000000000003a,2025-02-26T20:31:57+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,curl -v http://************:7080/uc/main/release/html/index.html,success
b284cb4a67bf0998000000000000003a,2025-02-26T20:32:51+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,curl -L -v http://************:7080,success
b284cb4a67bf0998000000000000003a,2025-02-26T20:33:11+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,exit,success
b513c5a367bf0a0b000000000000003a,2025-02-26T20:33:28+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,curl -L -v http://************:7080,success
b513c5a367bf0a0b000000000000003a,2025-02-26T20:33:32+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-02,************,exit,success
f55a21e967bf0a21000000000000003a,2025-02-26T20:33:43+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,curl -L -v http://************:7080,success
f55a21e967bf0a21000000000000003a,2025-02-26T20:33:52+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,cd /opt/uchome/,success
f55a21e967bf0a21000000000000003a,2025-02-26T20:33:53+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,ls,success
f55a21e967bf0a21000000000000003a,2025-02-26T20:34:01+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,./shutdown.sh ,success
f55a21e967bf0a21000000000000003a,2025-02-26T20:34:06+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,./startup.sh ,success
f55a21e967bf0a21000000000000003a,2025-02-26T20:34:12+08:00,cmd.Command,zengshengfa,**************,ningdun-DKEY-UC-01,************,curl -L -v http://************:7080,success
8d89c2de67bfc6400000000000000042,2025-02-27T09:57:09+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,crontab -l,success
8d89c2de67bfc6400000000000000042,2025-02-27T09:58:11+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,crontab -l,success
8d89c2de67bfc6400000000000000042,2025-02-27T09:58:19+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,crontab -l|grep kline,success
8d89c2de67bfc6400000000000000042,2025-02-27T09:58:28+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,cd /data/data-spider/,success
8d89c2de67bfc6400000000000000042,2025-02-27T09:58:34+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,bash scripts/market_kline_day.sh,success
16c75c6e67bfc6f5000000000000003a,2025-02-27T09:59:22+08:00,cmd.Command,zengshengfa,**************,snipe-it,************,lsb_release,success
16c75c6e67bfc6f5000000000000003a,2025-02-27T09:59:32+08:00,cmd.Command,zengshengfa,**************,snipe-it,************,lsb_release -a,success
8d89c2de67bfc6400000000000000042,2025-02-27T10:19:11+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,ls,success
8d89c2de67bfc6400000000000000042,2025-02-27T10:19:15+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,cd spiders/,success
8d89c2de67bfc6400000000000000042,2025-02-27T10:19:15+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,ls,success
8d89c2de67bfc6400000000000000042,2025-02-27T10:19:19+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,vim tasks_clickhouse.py ,success
8d89c2de67bfc6400000000000000042,2025-02-27T10:20:35+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,bash scripts/market_kline_day.sh,success
8d89c2de67bfc6400000000000000042,2025-02-27T10:20:38+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,cd ..,success
8d89c2de67bfc6400000000000000042,2025-02-27T10:20:39+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,bash scripts/market_kline_day.sh,success
8d89c2de67bfc6400000000000000042,2025-02-27T10:41:53+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,ls,success
8d89c2de67bfc6400000000000000042,2025-02-27T10:41:56+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,vim pyproject.toml ,success
8d89c2de67bfc6400000000000000042,2025-02-27T10:54:11+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,vim scripts/market_kline_day.sh,success
8d89c2de67bfc6400000000000000042,2025-02-27T10:54:18+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,source .env,success
8d89c2de67bfc6400000000000000042,2025-02-27T10:54:19+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,vim scripts/market_kline_day.sh,success
8d89c2de67bfc6400000000000000042,2025-02-27T10:54:25+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,$PYTHON_PATH  manager.py task  --task=market_kline_day,success
8d89c2de67bfc6400000000000000042,2025-02-27T10:54:40+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,vim .env ,success
8d89c2de67bfc6400000000000000042,2025-02-27T10:55:15+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,source /data/venv/bin/activate,success
8d89c2de67bfc6400000000000000042,2025-02-27T10:55:30+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,pip install --upgrade yfinance,success
8d89c2de67bfc6400000000000000042,2025-02-27T10:58:33+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,$PYTHON_PATH  manager.py task  --task=market_kline_day,success
2a940fcf67bffa470000000000000042,2025-02-27T13:38:22+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,cd /data/data-spider/,success
2a940fcf67bffa470000000000000042,2025-02-27T13:38:35+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,crontab -l|grep market_k,success
2a940fcf67bffa470000000000000042,2025-02-27T13:38:51+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,souece /data/venv/bin/activate,success
2a940fcf67bffa470000000000000042,2025-02-27T13:38:58+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,source /data/venv/bin/activate,success
2a940fcf67bffa470000000000000042,2025-02-27T13:39:48+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,vim scripts/market_kline_day.sh ,success
2a940fcf67bffa470000000000000042,2025-02-27T13:40:06+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,vim spiders/tasks_clickhouse.py ,success
2a940fcf67bffa470000000000000042,2025-02-27T13:40:20+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,$PYTHON_PATH  manager.py task  --task=market_kline_day,success
2a940fcf67bffa470000000000000042,2025-02-27T13:40:28+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,source .env,success
2a940fcf67bffa470000000000000042,2025-02-27T13:40:30+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,$PYTHON_PATH  manager.py task  --task=market_kline_day,success
2a940fcf67bffa470000000000000042,2025-02-27T14:06:14+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,git status,success
2a940fcf67bffa470000000000000042,2025-02-27T14:06:22+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,git checkout spiders/tasks_clickhouse.py,success
2a940fcf67bffa470000000000000042,2025-02-27T14:06:25+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,git pull,success
2a940fcf67bffa470000000000000042,2025-02-27T14:06:33+08:00,cmd.Command,chenzili,**************,data-research-test-01,************,$PYTHON_PATH  manager.py task  --task=market_kline_day,success
ce01e4ac67c0873d000000000000003a,2025-02-27T23:39:41+08:00,cmd.Command,zengshengfa,*************,snipe-it,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '.';cd '.';cd '.';pwd;ls -la,success
91d0040967c08710000000000000003a,2025-02-28T08:09:41+08:00,cmd.Command,zengshengfa,*************,snipe-it,************,exit,success
ee07b8f967c119e90000000000000019,2025-02-28T10:05:36+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
ee07b8f967c119e90000000000000019,2025-02-28T10:05:52+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use proamsdwdb;,success
ee07b8f967c119e90000000000000019,2025-02-28T10:05:59+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
ee07b8f967c119e90000000000000019,2025-02-28T10:05:59+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_state in(4,3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
ee07b8f967c119e90000000000000019,2025-02-28T10:06:00+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,group by f_currency;,success
ee07b8f967c119e90000000000000019,2025-02-28T10:23:59+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
ee07b8f967c119e90000000000000019,2025-02-28T10:24:00+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
7381b8e067c11e93000000000000001e,2025-02-28T10:25:28+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,nslookup,success
7381b8e067c11e93000000000000001e,2025-02-28T10:25:36+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,ping www.google.com,success
7381b8e067c11e93000000000000001e,2025-02-28T10:25:43+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,nslookup www.google.com,success
7381b8e067c11e93000000000000001e,2025-02-28T10:25:48+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,dig www.google.com,success
7381b8e067c11e93000000000000001e,2025-02-28T10:25:59+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,curl cip.cc,success
7381b8e067c11e93000000000000001e,2025-02-28T10:32:09+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,exit,success
7352928867c1202f000000000000001e,2025-02-28T10:32:22+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,curl cip.cc,success
7352928867c1202f000000000000001e,2025-02-28T10:32:35+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,exit,success
150e8cde67c125d7000000000000001e,2025-02-28T10:56:26+08:00,cmd.Command,liulonglong,*************,yigeyun-vpn-node-bj,************,curl cip.cc,success
150e8cde67c125d7000000000000001e,2025-02-28T10:56:44+08:00,cmd.Command,liulonglong,*************,yigeyun-vpn-node-bj,************,exit,success
d583158067c125f2000000000000001e,2025-02-28T10:56:54+08:00,cmd.Command,liulonglong,*************,yigeyun-vpn-node-bj-02,************,curl cip.cc,success
d583158067c125f2000000000000001e,2025-02-28T10:57:13+08:00,cmd.Command,liulonglong,*************,yigeyun-vpn-node-bj-02,************,exit,success
cd3237c867c1260f000000000000001e,2025-02-28T10:57:22+08:00,cmd.Command,liulonglong,*************,yigeyun-vpn-node-sz-01,************,curl cip.cc,success
cd3237c867c1260f000000000000001e,2025-02-28T10:57:40+08:00,cmd.Command,liulonglong,*************,yigeyun-vpn-node-sz-01,************,exit,success
17b622b867c12629000000000000001e,2025-02-28T10:57:47+08:00,cmd.Command,liulonglong,*************,yigeyun-vpn-node-sz-02,************,curl cip.cc,success
17b622b867c12629000000000000001e,2025-02-28T10:58:04+08:00,cmd.Command,liulonglong,*************,yigeyun-vpn-node-sz-02,************,exit,success
815d6f0767c15037000000000000001e,2025-02-28T13:57:15+08:00,cmd.Command,liulonglong,*************,yigeyun-vpn-node-sz-01,************,curl cip.cc,success
815d6f0767c15037000000000000001e,2025-02-28T13:57:54+08:00,cmd.Command,liulonglong,*************,yigeyun-vpn-node-sz-01,************,curl https://ipinfo.io,success
815d6f0767c15037000000000000001e,2025-02-28T13:58:33+08:00,cmd.Command,liulonglong,*************,yigeyun-vpn-node-sz-01,************,exit,success
29bc582467c1508f000000000000001e,2025-02-28T13:58:42+08:00,cmd.Command,liulonglong,*************,yigeyun-vpn-node-sz-02,************,curl cip.cc,success
29bc582467c1508f000000000000001e,2025-02-28T13:58:52+08:00,cmd.Command,liulonglong,*************,yigeyun-vpn-node-sz-02,************,exit,success
4fa9095967c150a1000000000000001e,2025-02-28T13:59:00+08:00,cmd.Command,liulonglong,*************,yigeyun-vpn-node-bj,************,curl cip.cc,success
4fa9095967c150a1000000000000001e,2025-02-28T13:59:08+08:00,cmd.Command,liulonglong,*************,yigeyun-vpn-node-bj,************,exit,success
8e870f0e67c150b1000000000000001e,2025-02-28T13:59:16+08:00,cmd.Command,liulonglong,*************,yigeyun-vpn-node-bj-02,************,curl cip.cc,success
8e870f0e67c150b1000000000000001e,2025-02-28T13:59:46+08:00,cmd.Command,liulonglong,*************,yigeyun-vpn-node-bj-02,************,exit,success
126c9b8c67c150df000000000000001e,2025-02-28T14:00:02+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,curl cip.cc,success
126c9b8c67c150df000000000000001e,2025-02-28T14:00:18+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,ping www.google.com,success
126c9b8c67c150df000000000000001e,2025-02-28T14:00:22+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,curl cip.cc,success
126c9b8c67c150df000000000000001e,2025-02-28T14:00:39+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,curl https://ipinfo.io,success
126c9b8c67c150df000000000000001e,2025-02-28T14:00:47+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,exit,success
58d2e3cf67c1511d000000000000001e,2025-02-28T14:01:04+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-2,************,curl cip.cc,success
58d2e3cf67c1511d000000000000001e,2025-02-28T14:01:09+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-2,************,curl https://ipinfo.io,success
01922e5967c1527c0000000000000039,2025-02-28T14:07:02+08:00,cmd.Command,zouwanqiang,**************,dbadmin,************,telnet database-1.cluster-cpw86c8ac93x.ap-northeast-1.rds.amazonaws.com 3306,success
58d2e3cf67c1511d000000000000001e,2025-02-28T14:07:18+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-2,************,exit,success
f616277667c15a1f000000000000001e,2025-02-28T14:39:38+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,ping *************,success
f616277667c15a1f000000000000001e,2025-02-28T14:39:49+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,telnet ************* 5114,success
f616277667c15a1f000000000000001e,2025-02-28T14:39:53+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,telnet ************* 2117,success
f616277667c15a1f000000000000001e,2025-02-28T14:39:58+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,telnet ************* 10000,success
f616277667c15a1f000000000000001e,2025-02-28T14:40:08+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,telnet ************* 2117,success
f616277667c15a1f000000000000001e,2025-02-28T14:40:19+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,telnet ************* 2115,success
f616277667c15a1f000000000000001e,2025-02-28T14:41:33+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,exit,success
b9b250d967c15aa1000000000000001e,2025-02-28T14:41:47+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-2,************,telnet ************* 2117,success
b9b250d967c15aa1000000000000001e,2025-02-28T14:42:33+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-2,************,sudo yum update -y,success
b9b250d967c15aa1000000000000001e,2025-02-28T14:45:13+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-2,************,sudo yum install -y telnet,success
b9b250d967c15aa1000000000000001e,2025-02-28T14:45:45+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-2,************,telnet sudo yum install -y telnet,success
b9b250d967c15aa1000000000000001e,2025-02-28T14:45:58+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-2,************,telnet ************* 2117,success
b9b250d967c15aa1000000000000001e,2025-02-28T14:46:04+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-2,************,telnet ************* 2115,success
b9b250d967c15aa1000000000000001e,2025-02-28T14:46:08+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-2,************,telnet ************* 10000,success
b9b250d967c15aa1000000000000001e,2025-02-28T14:46:09+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-2,************,^C,success
b9b250d967c15aa1000000000000001e,2025-02-28T14:46:21+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-2,************,telnet 54.219.244.145 10000,success
b9b250d967c15aa1000000000000001e,2025-02-28T14:46:23+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-2,************,^C,success
b9b250d967c15aa1000000000000001e,2025-02-28T14:46:28+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-2,************,telnet 54.219.************,success
b9b250d967c15aa1000000000000001e,2025-02-28T14:46:36+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-2,************,telnet 54.219.************,success
b9b250d967c15aa1000000000000001e,2025-02-28T14:47:20+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-2,************,ping 52.52.79.1,success
b9b250d967c15aa1000000000000001e,2025-02-28T14:47:37+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-2,************,exit,success
22a7d46c67c15c0e000000000000001e,2025-02-28T14:47:52+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,telnet 54.219.************,success
22a7d46c67c15c0e000000000000001e,2025-02-28T14:48:03+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,telnet 54.219.************,success
22a7d46c67c15c0e000000000000001e,2025-02-28T14:51:06+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,telnet 54.219.244.145 10000,success
22a7d46c67c15c0e000000000000001e,2025-02-28T14:51:17+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,ping 52.52.79.1,success
22a7d46c67c15c0e000000000000001e,2025-02-28T14:54:18+08:00,cmd.Command,liulonglong,*************,yige-vpn-gateway-1,************,exit,success
