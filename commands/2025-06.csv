SessionID,EventTime,EventType,<PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,Result
f2122483683bc6a********00000001e,2025-06-01T11:18:59+08:00,cmd.<PERSON>,liu<PERSON><PERSON>,61.93.78.241,yigeyun-vpn-node-bj,172.29.20.26,ping 172.28.1.228,success
f2122483683bc6a********00000001e,2025-06-01T11:20:49+08:00,cmd.Command,liulonglong,61.93.78.241,yigeyun-vpn-node-bj,172.29.20.26,exit,success
df63ad5a683bc716********0000001e,2025-06-01T11:21:05+08:00,cmd.Command,liulonglong,61.93.78.241,yigeyun-vpn-node-sz-01,172.30.1.129,ping 172.28.1.228,success
df63ad5a683bc716********0000001e,2025-06-01T11:22:53+08:00,cmd.<PERSON>,l<PERSON><PERSON><PERSON>,61.93.78.241,yigeyun-vpn-node-sz-01,172.30.1.129,exit,success
d5bbb5ef683facc1********00000019,2025-06-04T10:17:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
d5bbb5ef683facc1********00000019,2025-06-04T10:18:03+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use proamsdwdb;,success
d5bbb5ef683facc1********00000019,2025-06-04T10:18:14+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_currency, ceil(sum(f_amount*1.5)) as amount from t_withdraw_virtual w ,success
d5bbb5ef683facc1********00000019,2025-06-04T10:18:14+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_state in(4,3,6,8)  and from_unixtime(f_updated_at/1000) > '2022-11-01',success
d5bbb5ef683facc1********00000019,2025-06-04T10:18:15+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,group by f_currency;,success
d5bbb5ef683facc1********00000019,2025-06-04T10:23:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_user_id, f_currency, f_amount, f_state, from_unixtime(f_updated_at/1000) as f_date ,success
d5bbb5ef683facc1********00000019,2025-06-04T10:23:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,from t_withdraw_virtual  ,success
d5bbb5ef683facc1********00000019,2025-06-04T10:23:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,where f_state in (4,3,6,8),success
d5bbb5ef683facc1********00000019,2025-06-04T10:23:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********, and from_unixtime(f_updated_at/1000) > '2025-05-11',success
d5bbb5ef683facc1********00000019,2025-06-04T10:23:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********, and f_currency in('btc','eth','usdt') ,success
d5bbb5ef683facc1********00000019,2025-06-04T10:23:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********, and from_unixtime(f_updated_at/1000) > '2025-05order by f_currency;,success
d5bbb5ef683facc1********00000019,2025-06-04T10:27:46+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,show tables;,success
d5bbb5ef683facc1********00000019,2025-06-04T10:28:10+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,desc t_account_user_info;,success
d5bbb5ef683facc1********00000019,2025-06-04T10:31:31+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_id, f_user_id from t_account_user_info where f_id in(1198882, 1780068, ********, ********, 1612590, ********, 9790600, 3399108, 4162490, 3399108, 2275323, 2173962);,success
d5bbb5ef683facc1********00000019,2025-06-04T10:31:52+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,desc t_user_info;,success
d5bbb5ef683facc1********00000019,2025-06-04T10:32:21+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_id, f_user_id from tuser_info where f_id in(1198882, 1780068, ********, ********, 1612590, ********, 9790600, 3399108, 4162490, 3399108, 2275323, 2173962);,success
d5bbb5ef683facc1********00000019,2025-06-04T10:52:24+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
d5bbb5ef683facc1********00000019,2025-06-04T10:52:26+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
3d2b85486840483********00000003a,2025-06-04T21:20:48+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; echo $$;ps -ef,success
a59bd6e868404831********0000003a,2025-06-04T21:20:49+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
050f11fc68404832********0000003a,2025-06-04T21:20:50+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '.';pwd;ls -la,success
47355de26840484********00000003a,2025-06-04T21:21:04+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
840b57db68404841********0000003a,2025-06-04T21:21:05+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/root/';cd '/root/update';cd '/root/update';pwd;ls -la,success
0d955c7c68404851********0000003a,2025-06-04T21:21:21+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
8d49e5b968404857********0000003a,2025-06-04T21:21:27+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/root/update/';cd '/root/update/';cd '/root/update/';pwd;ls -la,success
55e524c16840486********00000003a,2025-06-04T21:21:36+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
26dbc0376840486a********0000003a,2025-06-04T21:21:46+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
0be5143268404875********0000003a,2025-06-04T21:21:57+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
56f503f568404889********0000003a,2025-06-04T21:22:17+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/root/update/';cd '/root/update/';cd '/root/update/';pwd;ls -la,success
45f60afc6840488b********0000003a,2025-06-04T21:22:19+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/root/update/';cd '/root/update/20250604/';cd '/root/update/20250604/';pwd;ls -la,success
9578028a684048a5********0000003a,2025-06-04T21:22:45+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
1311eba1684048a6********0000003a,2025-06-04T21:22:46+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/root/update/20250604/';cd '/root/update';cd '/root/update';pwd;ls -la,success
511ed369684048a9********0000003a,2025-06-04T21:22:49+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
c98be102684048aa********0000003a,2025-06-04T21:22:50+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/root/update/';cd '/opt';cd '/opt';pwd;ls -la,success
fe3c49dd684048ba********0000003a,2025-06-04T21:23:06+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
0524bb07684048bf********0000003a,2025-06-04T21:23:11+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
a2480567684048c1********0000003a,2025-06-04T21:23:13+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
c281cdd7684048d2********0000003a,2025-06-04T21:23:30+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
87aeff86684048d7********0000003a,2025-06-04T21:23:35+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
2b4d1ae1684048d9********0000003a,2025-06-04T21:23:37+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
e2f53df0684048dc********0000003a,2025-06-04T21:23:40+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
547a4a4f684048dd********0000003a,2025-06-04T21:23:41+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/opt/';cd '/opt';cd '/opt';pwd;ls -la,success
9d61f30d684048f7********0000003a,2025-06-04T21:24:07+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
1b5d591e684048f8********0000003a,2025-06-04T21:24:08+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/opt/';cd '/root';cd '/root';pwd;ls -la,success
10ad4ab4684048f9********0000003a,2025-06-04T21:24:09+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
396e44a1684048fa********0000003a,2025-06-04T21:24:10+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/root/';cd '/root/update';cd '/root/update';pwd;ls -la,success
b4f7623d684048fd********0000003a,2025-06-04T21:24:13+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
ca05408d684048fe********0000003a,2025-06-04T21:24:14+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/root/update/';cd '/root/update/20250604';cd '/root/update/20250604';pwd;ls -la,success
209d1bb268404905********0000003a,2025-06-04T21:27:21+08:00,file.Upload,zengshengfa,*************,ningdun-DKEY-AM-test,************,am-v8.5.zip,success
645b2df8684049c4********0000003a,2025-06-04T21:27:32+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
57659c32684049c7********0000003a,2025-06-04T21:27:35+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
66b18a15684049cd********0000003a,2025-06-04T21:27:41+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
d52d79a8684049bc********0000003a,2025-06-04T21:28:17+08:00,file.Upload,zengshengfa,*************,ningdun-DKEY-AM-test,************,uc-v8.5.zip,success
f9baf93f68404a16********0000003a,2025-06-04T21:28:54+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
2c1d85a7684049f2********0000003a,2025-06-04T21:31:18+08:00,file.Upload,zengshengfa,*************,ningdun-DKEY-AM-test,************,am-nds3.3.0 (2).zip,success
6f5bf16068404aa8********0000003a,2025-06-04T21:31:43+08:00,file.Upload,zengshengfa,*************,ningdun-DKEY-AM-test,************,DKEYAMPrivilegedOperationService-1.0.jar,success
28cd770a68404ac1********0000003a,2025-06-04T21:31:55+08:00,file.Upload,zengshengfa,*************,ningdun-DKEY-AM-test,************,maintainer-v8.5.zip,success
7c04018568404acc********0000003a,2025-06-04T21:31:56+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/root/update/20250604/';cd '/root/update/20250604/';cd '/root/update/20250604/';pwd;ls -la,success
9b9fef4868404ae********00000003a,2025-06-04T21:32:16+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
1fd9bf7268404aef********0000003a,2025-06-04T21:32:31+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
6575649868404afe********0000003a,2025-06-04T21:32:46+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
4e98760d68404b0a********0000003a,2025-06-04T21:32:58+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
2ef86c4068404b13********0000003a,2025-06-04T21:33:07+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
6804b46668404b16********0000003a,2025-06-04T21:33:10+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
664ded6868404b17********0000003a,2025-06-04T21:33:11+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/root/update/20250604/';cd '/root/update/20250604/3.3';cd '/root/update/20250604/3.3';pwd;ls -la,success
674c894768404b1a********0000003a,2025-06-04T21:33:14+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
f2c7015968404b26********0000003a,2025-06-04T21:33:26+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
c74666a068404b29********0000003a,2025-06-04T21:33:29+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
57a7f1a768404b2a********0000003a,2025-06-04T21:33:30+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/root/update/20250604/3.3/';cd '/opt';cd '/opt';pwd;ls -la,success
1fe0c22268404b2c********0000003a,2025-06-04T21:33:32+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
d0d4460768404b2d********0000003a,2025-06-04T21:33:33+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
0120420768404b2e********0000003a,2025-06-04T21:33:34+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/opt/';cd '/opt/amhome';cd '/opt/amhome';pwd;ls -la,success
6152ff1268404b2e********0000003a,2025-06-04T21:33:34+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
e860474368404b34********0000003a,2025-06-04T21:33:40+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
a9d28e9468404b57********0000003a,2025-06-04T21:34:15+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
ffe8d69668404b67********0000003a,2025-06-04T21:34:31+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
9cc6731768404b6b********0000003a,2025-06-04T21:34:35+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
7072142268404b73********0000003a,2025-06-04T21:34:43+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
1ab3dc5d68404b78********0000003a,2025-06-04T21:34:48+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
2ee14b3568404b7c********0000003a,2025-06-04T21:34:52+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
2ad19f1268404b8b********0000003a,2025-06-04T21:35:07+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
71cf143868404b99********0000003a,2025-06-04T21:35:21+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
b2db318668404b9a********0000003a,2025-06-04T21:35:22+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
e41e701f68404b9e********0000003a,2025-06-04T21:35:26+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
ca0e287068404ba********00000003a,2025-06-04T21:35:28+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
39e5d45668404ba1********0000003a,2025-06-04T21:35:29+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
b11ae26a68404ba3********0000003a,2025-06-04T21:35:31+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
8fd577eb68404ba4********0000003a,2025-06-04T21:35:32+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
e085751d68404ba6********0000003a,2025-06-04T21:35:34+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
9656e56268404baa********0000003a,2025-06-04T21:35:38+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
9155db7568404bb********00000003a,2025-06-04T21:35:44+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
4baae21e68404bb4********0000003a,2025-06-04T21:35:48+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
2d14e6bb68404bb9********0000003a,2025-06-04T21:35:53+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
eeaeff4568404bbc********0000003a,2025-06-04T21:35:56+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
7fa649e368404bc1********0000003a,2025-06-04T21:36:01+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
9b69ee6168404bcd********0000003a,2025-06-04T21:36:13+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
e9c2116668404bd3********0000003a,2025-06-04T21:36:19+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
eef8717f68404bd7********0000003a,2025-06-04T21:36:23+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
6cf992e868404be2********0000003a,2025-06-04T21:36:34+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
6531f29368404bea********0000003a,2025-06-04T21:36:42+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
e6bfe06e68404c04********0000003a,2025-06-04T21:37:08+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
6287596668404c05********0000003a,2025-06-04T21:37:09+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
f90615dc68404c13********0000003a,2025-06-04T21:37:23+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
70444f4e68404c2e********0000003a,2025-06-04T21:37:50+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
07abb0f468404c31********0000003a,2025-06-04T21:37:53+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
77f925b068404c36********0000003a,2025-06-04T21:37:58+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
7fd2798e68404c37********0000003a,2025-06-04T21:37:59+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/opt/amhome/';cd '/opt/amhome/tomcat';cd '/opt/amhome/tomcat';pwd;ls -la,success
807baad668404c3b********0000003a,2025-06-04T21:38:03+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/opt/amhome/tomcat/';cd '/opt/amhome';cd '/opt/amhome';pwd;ls -la,success
4be8183268404c3b********0000003a,2025-06-04T21:38:03+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
aa97058168404c3f********0000003a,2025-06-04T21:38:07+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/opt/amhome/';cd '/opt/amhome/am';cd '/opt/amhome/am';pwd;ls -la,success
1659ed6e68404c3f********0000003a,2025-06-04T21:38:07+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
4f55a84968404c4b********0000003a,2025-06-04T21:38:19+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
94cabc9f68404c4c********0000003a,2025-06-04T21:38:20+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
ab20d3a868404c57********0000003a,2025-06-04T21:38:31+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
9631e05568404cce********0000003a,2025-06-04T21:40:30+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
941f668568404d05********0000003a,2025-06-04T21:41:25+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
7f94f15668404d06********0000003a,2025-06-04T21:41:26+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
c1da7a1168404d17********0000003a,2025-06-04T21:41:43+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
3864c70d68404d28********0000003a,2025-06-04T21:42:00+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
d9040e8e68404d64********0000003a,2025-06-04T21:43:00+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
cb3b208868404d6f********0000003a,2025-06-04T21:43:11+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
f4196b5468404d7********00000003a,2025-06-04T21:43:12+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
be9750b268404d79********0000003a,2025-06-04T21:43:21+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
5834907e68404dec********0000003a,2025-06-04T21:45:16+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
b3e3fd2c68404ded********0000003a,2025-06-04T21:45:17+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
6e2cc99b68404dee********0000003a,2025-06-04T21:45:18+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/opt/amhome/am/';cd '/opt/amhome';cd '/opt/amhome';pwd;ls -la,success
4ed53d7468404df1********0000003a,2025-06-04T21:45:21+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
cecee8e568404df3********0000003a,2025-06-04T21:45:23+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/opt/amhome/';cd '/opt';cd '/opt';pwd;ls -la,success
a189667e68404dfb********0000003a,2025-06-04T21:45:31+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
962dd80a68404dfc********0000003a,2025-06-04T21:45:32+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
e2f6a73068404dff********0000003a,2025-06-04T21:45:35+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
07082f4168404e0e********0000003a,2025-06-04T21:45:50+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
4432e76b68404e1b********0000003a,2025-06-04T21:46:03+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
d7a9c25668404e34********0000003a,2025-06-04T21:46:28+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
567b3a1c68404e3a********0000003a,2025-06-04T21:46:34+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
5d69eae268404e46********0000003a,2025-06-04T21:46:46+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
de087bfa68404e54********0000003a,2025-06-04T21:47:00+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
9b292b1a68404e5d********0000003a,2025-06-04T21:47:09+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
c830e62768404e66********0000003a,2025-06-04T21:47:18+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
3c23410a68404e68********0000003a,2025-06-04T21:47:20+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
d816f82268404e85********0000003a,2025-06-04T21:47:49+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
8ba2fb7968404e8c********0000003a,2025-06-04T21:47:56+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
09d8791f68404e8f********0000003a,2025-06-04T21:47:59+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
bdc00ff068404e93********0000003a,2025-06-04T21:48:03+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
0911b1f968404e9b********0000003a,2025-06-04T21:48:11+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
16a219d468404efe********0000003a,2025-06-04T21:49:50+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
fefb7fe868404f04********0000003a,2025-06-04T21:49:56+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
2652315268404f19********0000003a,2025-06-04T21:50:17+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
6642bc2f68404f1b********0000003a,2025-06-04T21:50:19+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
8b7bf3e668404f2d********0000003a,2025-06-04T21:50:37+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
3cea222768404f3e********0000003a,2025-06-04T21:50:54+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
d7a57d0168404f3f********0000003a,2025-06-04T21:50:55+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
4811809f68404f47********0000003a,2025-06-04T21:51:03+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
ca1b3af768404f4a********0000003a,2025-06-04T21:51:06+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
e55595f068404f52********0000003a,2025-06-04T21:51:14+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
6b6d499868404f58********0000003a,2025-06-04T21:51:20+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
07949f5368404f5b********0000003a,2025-06-04T21:51:23+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
9df4495268404f5c********0000003a,2025-06-04T21:51:24+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
1bfc35e168404f62********0000003a,2025-06-04T21:51:30+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
52375e5d68404f69********0000003a,2025-06-04T21:51:37+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
a6500f3968404f78********0000003a,2025-06-04T21:51:52+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
c35a187d68404f7a********0000003a,2025-06-04T21:51:54+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
02c8a86668404f7c********0000003a,2025-06-04T21:51:56+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
5df8e09068404f7d********0000003a,2025-06-04T21:51:57+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
d2f4726d68404f8********00000003a,2025-06-04T21:52:00+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
27f8049068404f81********0000003a,2025-06-04T21:52:01+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/opt/';cd '/opt/uchome';cd '/opt/uchome';pwd;ls -la,success
ca3e89dc68404f87********0000003a,2025-06-04T21:52:07+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
7f7022ec68404f8b********0000003a,2025-06-04T21:52:11+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
a3982fdf68404f96********0000003a,2025-06-04T21:52:22+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
c3baffa368404fa5********0000003a,2025-06-04T21:52:37+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
d4cc4d6968404fb2********0000003a,2025-06-04T21:52:50+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
1a2ea9f468404fc2********0000003a,2025-06-04T21:53:06+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
5a85b48268404fca********0000003a,2025-06-04T21:53:14+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
957f178f68404fcc********0000003a,2025-06-04T21:53:16+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
c268a77168404fd2********0000003a,2025-06-04T21:53:22+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
fbc97a6968404fd3********0000003a,2025-06-04T21:53:23+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
d6a8789968404fdc********0000003a,2025-06-04T21:53:32+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
046f0dbe68404fdd********0000003a,2025-06-04T21:53:33+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
de2f33b668404fe********00000003a,2025-06-04T21:53:36+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
a3fe809968404fe7********0000003a,2025-06-04T21:53:43+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
29f05f58684050ab********0000003a,2025-06-04T21:56:59+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
6784c330684050ac********0000003a,2025-06-04T21:57:00+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/opt/uchome/';cd '/root/update/20250604';cd '/root/update/20250604';pwd;ls -la,success
085750d7684050b6********0000003a,2025-06-04T21:57:11+08:00,file.Upload,zengshengfa,*************,ningdun-DKEY-AM-test,************,dkey-idp-1.0.jar,success
587e3658684050b8********0000003a,2025-06-04T21:57:12+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/root/update/20250604/';cd '/root/update/20250604/';cd '/root/update/20250604/';pwd;ls -la,success
322ee9dd684050e8********0000003a,2025-06-04T21:58:00+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
1e9a86c5684050ef********0000003a,2025-06-04T21:58:07+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
db6bb24268405104********0000003a,2025-06-04T21:58:28+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
033332966840510c********0000003a,2025-06-04T21:58:36+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
a2599f8b6840510d********0000003a,2025-06-04T21:58:37+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
7ea5421468405111********0000003a,2025-06-04T21:58:41+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
bef8a50768405113********0000003a,2025-06-04T21:58:43+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
772a433768405116********0000003a,2025-06-04T21:58:46+08:00,cmd.Command,zengshengfa,*************,ningdun-DKEY-AM-test,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; pwdx 1732045; ls -lad /proc/1732045/cwd,success
cf4c0bf468424b46********00000019,2025-06-06T09:58:49+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
cf4c0bf468424b46********00000019,2025-06-06T09:59:09+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use proamsdwdb;,success
cf4c0bf468424b46********00000019,2025-06-06T09:59:47+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use proamsdwdb;,success
cf4c0bf468424b46********00000019,2025-06-06T09:59:52+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,show tables,success
cf4c0bf468424b46********00000019,2025-06-06T09:59:53+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,;,success
cf4c0bf468424b46********00000019,2025-06-06T10:01:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use hbg_trade; ,success
cf4c0bf468424b46********00000019,2025-06-06T10:01:09+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,show tables;,success
cf4c0bf468424b46********00000019,2025-06-06T10:01:26+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,desc exchange_rate;,success
cf4c0bf468424b46********00000019,2025-06-06T10:01:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select * from exchange_rate limit 10;,success
cf4c0bf468424b46********00000019,2025-06-06T10:03:32+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,show create exchange_rate;,success
cf4c0bf468424b46********00000019,2025-06-06T10:03:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,show table create exchange_rate;,success
cf4c0bf468424b46********00000019,2025-06-06T10:04:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,show create table exchange_rate;,success
cf4c0bf468424b46********00000019,2025-06-06T10:07:29+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select distinct f_day from t_account_manage_fee_snapshot;,success
cf4c0bf468424b46********00000019,2025-06-06T10:10:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,desc t_account_manage_fee_snapshot;,success
cf4c0bf468424b46********00000019,2025-06-06T10:12:20+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(*) from t_account_manage_fee_snapshot where f_day = ********;,success
cf4c0bf468424b46********00000019,2025-06-06T10:15:31+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,create table t_account_manage_fee_snapshot_tag0501 as select * from t_account_manage_fee_snapshot where f_day = ********;,success
cf4c0bf468424b46********00000019,2025-06-06T10:16:08+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,show create table t_account_manage_fee_snapshot;,success
cf4c0bf468424b46********00000019,2025-06-06T10:17:03+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,  KEY `idx_state` (`f_state`),,success
cf4c0bf468424b46********00000019,2025-06-06T10:17:03+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,  `f_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',,success
cf4c0bf468424b46********00000019,2025-06-06T10:17:03+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,  `f_day` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '月份',,success
cf4c0bf468424b46********00000019,2025-06-06T10:17:03+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,  `f_child` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否 子账户 1是 0否',,success
cf4c0bf468424b46********00000019,2025-06-06T10:17:03+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,  `f_parent_uid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '父账号uid,child 为1时有效',,success
cf4c0bf468424b46********00000019,2025-06-06T10:17:03+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,  `f_currency` varchar(20) NOT NULL DEFAULT '' COMMENT '甯佺',,success
cf4c0bf468424b46********00000019,2025-06-06T10:17:03+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,  `f_user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '鐢ㄦ埛id',,success
cf4c0bf468424b46********00000019,2025-06-06T10:17:03+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,  `f_uid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'uid',,success
cf4c0bf468424b46********00000019,2025-06-06T10:17:03+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,CREATE TABLE `t_account_manage_fee_snapshot_tag0501` (,success
cf4c0bf468424b46********00000019,2025-06-06T10:17:03+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,  `f_balance` decimal(36,18) unsigned NOT NULL DEFAULT '0.****************00' COMMENT '蹇収浣欓',,success
cf4c0bf468424b46********00000019,2025-06-06T10:17:03+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,  `f_created_at` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '鍒涘缓鏃堕棿',,success
cf4c0bf468424b46********00000019,2025-06-06T10:17:03+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,  `f_updated_at` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '淇敼鏃堕棿',,success
cf4c0bf468424b46********00000019,2025-06-06T10:17:03+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,  `f_file_num` varchar(20) NOT NULL DEFAULT '' COMMENT '鏁版嵁瀵瑰攕3文件编号',,success
cf4c0bf468424b46********00000019,2025-06-06T10:17:03+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,  `f_state` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '0 初始化 1成功 2失败',,success
cf4c0bf468424b46********00000019,2025-06-06T10:17:03+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,  `f_scale` decimal(36,18) unsigned NOT NULL DEFAULT '0.****************00' COMMENT '管理费率',,success
cf4c0bf468424b46********00000019,2025-06-06T10:17:03+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,  `f_amount` decimal(36,18) unsigned DEFAULT '0.****************00' COMMENT '正常应该收取的金额',,success
cf4c0bf468424b46********00000019,2025-06-06T10:17:03+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,  `f_send_state` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '发送状态 0初始化 1成功 2失败',,success
cf4c0bf468424b46********00000019,2025-06-06T10:17:03+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,  `f_type` tinyint(4) unsigned NOT NULL DEFAULT '1' COMMENT '类型 1收取 2退还',,success
cf4c0bf468424b46********00000019,2025-06-06T10:17:03+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,  PRIMARY KEY (`f_id`),,success
cf4c0bf468424b46********00000019,2025-06-06T10:17:03+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,  UNIQUE KEY `uniq_userId_day_type_currency` (`f_user_id`,`f_day`,`f_type`,`f_currency`),,success
cf4c0bf468424b46********00000019,2025-06-06T10:17:03+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,  KEY `idx_sendState` (`f_send_state`),,success
cf4c0bf468424b46********00000019,2025-06-06T10:17:03+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,  `f_account_type` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '璐︽埛绫诲瀷 2 浜ゆ槗璐︽埛',,success
cf4c0bf468424b46********00000019,2025-06-06T10:17:03+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,  KEY `idx_createdAt` (`f_created_at`),success
cf4c0bf468424b46********00000019,2025-06-06T10:17:09+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='账户管理费快照总表tag0501';,success
cf4c0bf468424b46********00000019,2025-06-06T10:17:50+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into `t_account_manage_fee_snapshot_tag0501` select * from t_account_manage_fee_snapshot where f_day = ********;,success
cf4c0bf468424b46********00000019,2025-06-06T10:23:49+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,show table exchange_rate;,success
cf4c0bf468424b46********00000019,2025-06-06T10:23:58+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,show tables;,success
cf4c0bf468424b46********00000019,2025-06-06T10:24:06+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,desc exchange_rate;,success
cf4c0bf468424b46********00000019,2025-06-06T10:27:24+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select * from exchange_rate where currcy_date = 'hpoint';,success
cf4c0bf468424b46********00000019,2025-06-06T10:27:32+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select * from exchange_rate where currcy_date = 'hbpoint';,success
cf4c0bf468424b46********00000019,2025-06-06T10:27:55+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select * from exchange_rate where currcy_type = 'hbpoint';,success
cf4c0bf468424b46********00000019,2025-06-06T10:32:18+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into `exchange_rate`('rate_date', 'currcy_type', 'usdt_rate') values('********', 'sylo', 0.00042000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:34:53+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate (rate_date, currcy_type, usdt_rate) values('********', 'sylo', 0.00042000 );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'velo', 0.01245000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'wallet', 0.01457400 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'music', 0.01166000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'soph', 0.05293000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'cfx', 0.07622000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'kct', 0.00073100 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'baby', 0.06525400 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'swarms', 0.02220000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'sc', 0.00329100 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'zig', 0.10034100  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'bome', 0.00181400 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'memefi', 0.00130100 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'polc', 0.00072300 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'tst', 0.04070000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ladys', 0.00000004 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'magic', 0.13110000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'mlk', 0.15730000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'elf', 0.22350000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'waxl', 0.31835000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'prcl', 0.08510000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'game', 0.00330300 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'lunc', 0.00005833 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ldo', 0.84390000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'alice', 0.41680000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'gork', 0.00971900 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'eigen', 1.45370000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'egp', 0.91800000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'tremp', 0.02210000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'torn', 9.28700000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'aave', 260.46080000 );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ankr', 0.01664300 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'jst', 0.03332000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ai', 0.13870000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'akt', 1.28630000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'dio', 0.00336900 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'pbux', 0.00319000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'tomi', 0.00179000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'zeus', 0.14960000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'wifi', 0.00558900 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'vra', 0.00148100  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'tru', 0.03653800 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'wsdm', 0.00270900 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'tronbull', 0.00609000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'cere', 0.00146300 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'dia', 0.40640000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'doge', 0.18929700 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'dym', 0.28190000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'zen', 11.09230000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'rad', 0.69620000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'sd', 0.49090000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'eurr', 1.14370000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'reef', 0.00040500 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'sns', 0.00278300 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'obol', 0.15220000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'wojak', 0.00036303 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'lat', 0.00399600 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'suncat', 0.00142000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ssv', 9.83570000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'flr', 0.01760000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ena', 0.31057000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'boson', 0.10810000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'arg', 0.85490000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'init', 0.70660000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'csix', 0.00600200 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'brise', 0.00000005 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'fitfi', 0.00158600 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'crts', 0.00023588 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'bico', 0.10070000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'kernel', 0.14660000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ponke', 0.13191000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'beth', 2589.85000000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'mode', 0.00306000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'aitech', 0.04976000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'htx', 0.00000193 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'polyx', 0.13830000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'slerf', 0.07330000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'dc', 0.00007900 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'mavia', 0.15910000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'pengu', 0.00987000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'amp', 0.00407000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'jup', 0.49060000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'hot', 0.00098300 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'glm', 0.23590000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'pizza', 0.25290000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'vsys', 0.00026080 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'nft', 0.00000041 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'gummy', 0.00123100 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'bat', 0.13150000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'acs', 0.00126500 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'rez', 0.01080000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'omni', 2.19840000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'eurq', 1.14400000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'snt', 0.02820100 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'floki', 0.00008153 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'xmr', 326.85000000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'zkp', 0.00905000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'chr', 0.08698100 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'xcad', 0.04030000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'dydx', 0.55650000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'mana', 0.27510000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ox', 0.00072000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'om', 0.31160000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'alch', 0.13444000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'nym', 0.04010000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'etc', 17.06400000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'sei', 0.19930000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'vic', 0.17500000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'mubi', 0.00494000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'kmd', 0.09280000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'tbull', 0.00303000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'aioz', 0.35130000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'cyber', 1.17290000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'pols', 0.21960000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'aqt', 1.07580000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'mog', 0.00000086 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'hat', 0.00268000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'maga', 0.00000914 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'silly', 0.00148000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'mnt', 0.65770000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'dora', 0.02830000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'icx', 0.13830000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'unibot', 3.68100000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'nil', 0.41930000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'dck', 0.00936100 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'dot', 4.01880000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'bigtime', 0.06168000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'grok', 0.00272900 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'super', 0.64560000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'solo', 0.25355000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'link', 13.71260000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'stg', 0.17870000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'lrc', 0.09200000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'adp', 0.00161100 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'rei', 0.01806400 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'meme', 0.00183600 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'rdnt', 0.03280000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'kt', 0.00042700 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'sora', 0.00039200 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'tava', 0.01198000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'cat', 0.00000708  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'pippin', 0.01778000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'xvs', 5.99690000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'rock', 0.00118718 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'avl', 0.17871000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'trx', 0.27442300 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'mfer', 0.00800000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'lpt', 8.45740000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'virtual', 1.73960000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'hft', 0.05620000 );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'orbs', 0.01928600 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'mbox', 0.05160000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'grt', 0.09314700 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'hgg', 0.00115400 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'banana', 21.06590000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'epik', 0.00227800 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'bbf', 0.00616400 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'krrx', 0.04040000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'cvc', 0.12150000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'bone', 0.26730000 );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'moodeng', 0.19116000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'al', 0.09690000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'pnut', 0.25240000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'zec', 50.19000000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'saros', 0.19815300 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'win', 0.00005224 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'movr', 6.05990000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'beer', 0.00000342 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'steem', 0.13530000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'vlx', 0.00289000 );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'griffain', 0.05530000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'puffer', 0.19840000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'utk', 0.02870000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ronin', 0.56570000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'xec', 0.00002155 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'pci', 0.06671000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'bld', 0.01380000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'syrup', 0.38540000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'xcn', 0.01496100 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'people', 0.02115300  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'egld', 15.49620000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'lf', 0.00047900 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'tnsr', 0.12800000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'aurora', 0.08000000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'flux', 0.22160000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ghst', 0.38410000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ustc', 0.01170000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'sushi', 0.66360000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'sxp', 0.17970000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'cake', 2.39900000 );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'near', 2.44160000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'trx3s', 0.******** 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'dusk', 0.06690000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'clv', 0.02550000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'coq', 0.00000061 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ava', 0.55360000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'spell', 0.00052500 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'shell', 0.16930000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'dyp', 0.00580000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'forth', 2.41800000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'fet', 0.76640000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'gmx', 15.40410000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'tusd', 0.99813000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'pooh', 0.00000001 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'pendle', 4.08670000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'eth', 2589.91000000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'xprt', 0.05750000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'me', 0.88850000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'pyusd', 0.99860000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'haedal', 0.12860000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'plu', 0.67430000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'elizacto', 0.00043100 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'degen', 0.00437500 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'xfi', 0.09990000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'xno', 0.00007900 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ai16z', 0.21690000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'sun', 0.01930800 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'trump', 10.70000000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'psg', 1.95440000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'gas', 2.95000000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'nkn', 0.02838600 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'hpos10i', 0.07149400 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'rif', 0.00245000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'gno', 128.33570000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'wat', 0.00003230 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'drift', 0.54090000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'btc', 104538.55000000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'omg', 0.20960000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'imx', 0.53740000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'op', 0.63940000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'dbr', 0.01378000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'goat', 0.11900000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'navx', 0.04869500 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ethw', 1.42370000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'fis', 0.******** 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'tut', 0.******** 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'bank', 0.******** 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'xzk', 0.******** 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'blast', 0.******** 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ape', 0.********  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'merl', 0.******** 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'pond', 0.00895000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'bal', 1.06740000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'iota', 0.18050000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'voxel', 0.05540000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'mtl', 0.73540000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'alt', 0.02666000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'pda', 0.00680000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'wmtx', 0.16730000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'taiko', 0.52950000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'pyr', 1.02080000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'mx', 2.64650000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'fanc', 0.00406000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'awe', 0.05413000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'scrt', 0.38170000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'arpa', 0.02139600 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'looks', 0.01340000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'tlos', 0.05156000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'bfc', 0.03241000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'celo', 0.31670000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'dash', 21.62000000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'xdc', 0.05950500 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'trb', 49.08330000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'red', 0.33370000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ach', 0.02113700 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'pha', 0.13119000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'mew', 0.00312200 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'zent', 0.00871000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:40+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'alu', 0.02162300 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'nexo', 1.24140000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'pstake', 0.06520000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'bb', 0.11430000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'gods', 0.13690000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'kima', 0.10000000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'grift', 0.00922000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'strx', 0.32739000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ace', 0.53680000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'order', 0.08300000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'zil', );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'maneki', 0.00136700 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'edgen', 0.01520000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'mapo', 0.00457400 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'gala', 0.01629300 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'naka', 0.42150000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'usdj', 1.10320000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ethfi', 1.11930000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'klv', 0.00267700 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'azero', 0.02640000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,ie_rate(rate_dinsert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'mkr', 1822.79000000 );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'brett', 0.04914000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'myth', 0.13970000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'swell', 0.00912000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ar', 6.38900000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'prq', 0.02300000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'moonpig', 0.02131000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'lrds', 0.13030000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'bch', 402.97000000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'cgpt', 0.10816000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'route', 0.01041000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'aic', 0.10819000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'la', 1.31830000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'donkey', 0.00213300 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'usd1', 0.99980000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'xvg', 0.00615100 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'bera', 2.39800000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'dao', 0.13170000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'sys', 0.04180000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'well', 0.03396500  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'cho', 0.00581000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'eurt', 1.13790000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ctx', 1.67950000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'mev', 0.00281600 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'rats', 0.00002182 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ogn', 0.05700000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'velodrome', 0.05050000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'kaito', 1.65740000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'prom', 5.33890000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'mbx', 0.18490000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'apt', 4.80720000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'mother', 0.01009000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'id', 0.17900000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'loka', 0.05700000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'metis', 17.82630000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'vr', 0.00279304 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'zrc', 0.02747000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'rain', 0.00003900 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'acx', 0.19360000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'beamx', 0.00649500  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ghibli', 0.00239300 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'idex', 0.02340000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'dop', 0.00024960 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'eul', 8.53330000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ban', 0.05624000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'luna', 0.17060000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'phb', 0.50410000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'slf', 0.15560000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'avaai', 0.03893000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ondo', 0.82060300  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ela', 1.45000000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'rab', 0.00169900 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'oas', 0.01326200 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'btf', 0.00538800 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'blur', 0.08900000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'bubble', 0.00066800 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'neiro', 0.00102000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'neur', 0.00191000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'raca', 0.00007240 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'shrap', 0.00454000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'usdp', 1.00680000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'req', 0.14030000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'gme', 0.00202600 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'scr', 0.28820000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'lqty', 0.97030000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'bmt', 0.12340000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'pixfi', 0.00053000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'strk', 0.13370000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ctsi', 0.06270000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'zk', 0.06770000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'neiroeth', 0.08950000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'moca', 0.08418000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'push', 0.03330000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'badger', 0.99270000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'mplx', 0.13810000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'arkm', 0.52000000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 's', 0.38890000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'inj', 12.20770000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'powr', 0.16350000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'iost', 0.00352800  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ygg', 0.18340000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'wwy', 0.00023100 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ray', 2.17590000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'steth', 2589.62000000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'atom', 4.30310000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'cspr', 0.01170000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'rsr', 0.00667100 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'fct2', 0.02868900 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'gt', 19.05730000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'dai', 1.00010000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'crv', 0.64530000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'pyth', 0.11451800 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'eliza', 0.00259000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'swftc', 0.01208890 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'broccoli', 0.02661600 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'suku', 0.03050000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ctc', 0.65510000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'bsv', 33.37370000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'nano', 0.98530000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'popcat', 0.34260000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', '1inch', 0.21040800 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ab', 0.00863600 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ethf', 0.00928700 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'gtai', 0.13550000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'yfi', 5168.06000000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'sfund', 0.67820000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'zeta', 0.21770000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'fartcoin', 0.95925000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'bebe', 0.00004099  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'fio', 0.01537200 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'jasmy', 0.01414600 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'aergo', 0.13069000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'chz', 0.03889000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'arty', 0.13090000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'pixel', 0.04370000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'dep', 0.00122800 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'sparklet', 0.02650000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'enj', 0.07560000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'myro', 0.02068700  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ordi', 8.76000000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'clore', 0.01805000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'bull', 0.00136700 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'rpk', 0.00161000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'layer', 0.77550000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'zerebro', 0.03120000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'io', 0.77540000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'cru', 0.07120000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'bonk', 0.00001543 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'xem', 0.00700000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'lester', 0.00096000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'storj', 0.26450000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 't', 0.01490000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'f', 0.01062000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'xen', 0.00000004 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'aidoge', 0.******** 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ertha', 0.00057810 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'coti', 0.05800000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'flz', 1.47550000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'flow', 0.36540000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ept', 0.00758800 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ip', 4.05080000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'neo', 6.02000000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ath', 0.05045000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'sand', 0.27417000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'moov', 0.00114000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'fhe', 0.06999000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'c98', 0.05010000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'kava', 0.42590000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'rlc', 1.05140000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'nuls', 0.06050000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'fight', 0.00079500 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'mask', 2.92350000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'tlm', 0.00479000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'a8', 0.12020000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'gns', 1.61000000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'vrtx', 0.03700000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'zrx', 0.23940000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'vinu', 0.00000002 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'num', 0.02230000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ever', 0.01017000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'npt', 0.07540000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'mon', 0.02250000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'wlkn', 0.00039700 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'rune', 1.57100000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'lvn', 0.02099000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'bio', 0.06359000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'aca', 0.02910000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'synt', 0.01515000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'nxpc', 1.27670000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'fuse', 0.01065000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:42+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'propy', 0.90750000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'bsw', 0.02500600 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'cta', 0.03670000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'zerolend', 0.00005000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'mong', 0.******** 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'euroc', 1.11960000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'xlm', 0.26505500 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'adx', 0.09630000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'rpl', 5.81120000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'epic', 1.14520000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'wld', 1.10800000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'osmo', 0.20390000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'avail', 0.03235000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'lwa', 0.02397000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'carv', 0.32590000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'xtz', 0.57300000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'uos', 0.05160000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'sto', 0.09422000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'solv', 0.04298000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'usdc', 0.99950000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'trac', 0.39420000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'g', 0.01345000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'dka', 0.01589700 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'sbd', 0.83700000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'render', 3.78700000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ont', 0.13530000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'kan', 0.00064400 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'safe', 0.48280000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'wif', 0.85820000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'neirocto', 0.00042964 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'wxt', 0.00290100 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'obt', 0.00946000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'buzz', 0.00683000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'b2', 0.49180000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'root', 0.00475000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'pundix', 0.32070000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'troll', 0.******** 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'plume', 0.12919000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'wst', 1.06251100  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ice', 0.00517500 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'xrp', 2.19250000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'grass', 1.84740000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'nyan', 0.00399000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'btt', 0.00000070 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'smole', 0.00003130 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'vet', 0.02377900 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'aeth', 0.99990000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'gmt', 0.05000000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'michi', 0.03180000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'blue', 0.12020000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'elon', 0.00000013 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'gamebase', 0.03040000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'billy', 0.00324000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'aevo', 0.10070000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'saga', 0.28400000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'joe', 0.16250000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'astr', 0.02730500 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'spore', 0.00058000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'xaut', 3348.10000000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'usdd', 0.99999000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'waxp', 0.02172900 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'a', 0.61380000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'fxs', 2.85630000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'skyai', 0.03825200 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'logx', 0.01110000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'woo', 0.07830000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'lunasol', 0.01818000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'jto', 1.73720000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'lsk', 0.43050000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'like', 0.01534300 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'poolx', 0.40917000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'portal', 0.04400000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'avax', 19.99050000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'hive', 0.23680000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'chillguy', 0.05996000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'gearbox', 0.00500900 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ctxc', 0.08030000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'arb', 0.35240000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ada', 0.67415000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'l3', 0.06616000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'fil', 2.52020000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'moodengeth', 0.00003346 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'atm', 1.05540000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'elx', 0.09260000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'wnxm', 58.60000000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'uro', 0.00228000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'melania', 0.31970000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'boba', 0.08948000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'agld', 0.87470000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'e1e2', 0.******** 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'wan', 0.11510000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'blz', 0.03420000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'axs', 2.47910000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'sovrn', 0.01220000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'mln', 8.19600000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ftt', 1.03650000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'caw', 0.00000005 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'one', 0.01175600  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'syn', 0.15410000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'fury', 0.02117000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'rtf', 0.03538000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'trx3l', 0.******** 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'w', 0.07540000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'nest', 0.00016300 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'waves', 1.07930000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'arc', 0.03960000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'uni', 6.35160000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'masa', 0.02029000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'xyo', 0.01081000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'kaia', 0.10830000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'dexe', 10.08200000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'quick', 0.02235800 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ens', 21.37060000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'cow', 0.39280000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'deso', 4.03100000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ckb', 0.00405300 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'knc', 0.33410000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'manta', 0.24140000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'top', 0.00015700 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'tt', 0.00272000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'cvx', 2.62700000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'auction', 10.43680000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'cxt', 0.03240000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'mnde', 0.11704000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'evmos', 0.00388000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'uxlink', 0.39190000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'wen', 0.00003960 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'shib', 0.00001279  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ltc', 87.54000000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'qtum', 2.04850000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'hunt', 0.25140000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'soon', 0.29480000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'etn', 0.00169400 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'xch', 10.97250000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'og', 4.74150000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'bake', 0.11000000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'cetus', 0.12870000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'glmr', 0.07750000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'mbl', 0.00225800 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'swap', 0.08530000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'sca', 0.13570000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'grokcoin', 0.00019400 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'babydoge', 0.******** 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ufd', 0.02562300 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'paal', 0.10760000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'lvva', 0.00326800 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'skl', 0.02110500 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'xr', 0.03047000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'oxt', 0.05570000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'mina', 0.20580000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'wbt', 31.26950000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'firo', 0.66550000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'szn', 0.00040500 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'zro', 2.21400000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'icp', 5.09000000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'algo', 0.19070000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'juv', 1.03960000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'orca', 2.47350000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'mcrt', 0.00058990 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'omnia', 0.01620000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'usdr', 1.******** 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ksm', 16.14090000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'gomining', 0.43271000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'bnt', 0.64710000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'hippo', 0.00214900 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'move', 0.14170000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'xeta', 0.00062000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'iotx', 0.02148200  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'sis', 0.05900000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'uma', 1.14100000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'luce', 0.00688000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'tia', 2.16250000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'bnb', 659.26000000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'pol', 0.21240000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'parti', 0.23808000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'actsol', 0.04966000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'df', 0.04160000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'erg', 0.90220000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'spa', 0.01532800 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ardr', 0.08990000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'snx', 0.68030000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'aixbt', 0.18210000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'dcr', 15.03550000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'lmwr', 0.08510000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'sundog', 0.05450000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'xter', 0.19692000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'house', 0.03556800 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'perp', 0.24580000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'pepe', 0.00001157 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'sui', 3.13030000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ca', 0.52240000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'comp', 44.25000000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'hbar', 0.16754900 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'sweat', 0.00299300 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'rvn', 0.01621300 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ton', 3.24260000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'lljeffy', 0.00807000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'tenet', 0.00049200  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'xion', 1.22700000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'sats', 0.00000004 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'mvl', 0.00334300 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'dgb', 0.01538500 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'bananas31', 0.00600400 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'theta', 0.75900000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'mubarak', 0.04147000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'mxc', 0.00386000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'usdq', 0.99990000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'api3', 0.70980000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'fud', 7.27320000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'rss3', 0.04570000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'xpla', 0.04650000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'band', 0.66680000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'msn', 0.03040000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'sol', 150.89770000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'audio', 0.06830000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'dodo', 0.04270000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'cbk', 0.49330000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ali', 0.00588500 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'turbo', 0.00407016 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'zcx', 0.03560000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'lai', 0.00078400 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'dechat', 0.04150000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'vine', 0.03650000 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'ren', 0.01075800 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'dark', 0.00626300 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'core', 0.63782800 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'zkl', 0.02751000  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:45+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'usdt', 1.******** 	);,success
cf4c0bf468424b46********00000019,2025-06-06T10:35:50+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into exchange_rate(rate_date, currcy_type, usdt_rate) values('********', 'hbpoint', 1.********  );,success
cf4c0bf468424b46********00000019,2025-06-06T10:36:33+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(*) from exchange_rate where rate_date = '********';,success
cf4c0bf468424b46********00000019,2025-06-06T10:37:05+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,desc t_account_manage_fee_snapshot;,success
cf4c0bf468424b46********00000019,2025-06-06T10:46:52+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,`f_balance` decimal(36,18) unsigned NOT NULL DEFAULT '0.****************00' COMMENT '快照余额',success
cf4c0bf468424b46********00000019,2025-06-06T10:46:52+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,`f_uid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'uid',,success
cf4c0bf468424b46********00000019,2025-06-06T10:46:52+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,CREATE TABLE `t_account_usdts_tag0501` (,success
cf4c0bf468424b46********00000019,2025-06-06T10:47:00+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='快照余额tag0501';,success
cf4c0bf468424b46********00000019,2025-06-06T10:48:05+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into t_account_usdts_tag0501 select f_uid, sum(f_balance * rates.usdt_rate) as f_balance from t_account_manage_fee_snapshot_tag0501 tags inner join ,success
cf4c0bf468424b46********00000019,2025-06-06T10:48:05+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(select currency_type, usdt_rate from exchange_rate where rate_date = '********') rates ON tags.f_currency = rates.currency_type,success
cf4c0bf468424b46********00000019,2025-06-06T10:48:08+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,group by f_uid;,success
cf4c0bf468424b46********00000019,2025-06-06T10:48:53+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into t_account_usdts_tag0501 select f_uid, sum(f_balance * rates.usdt_rate) as f_balance from t_account_manage_fee_snapshot_tag0501 tags inner join ,success
cf4c0bf468424b46********00000019,2025-06-06T10:48:53+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(select currcy_type, usdt_rate from exchange_rate where rate_date = '********') rates ON tags.f_currency = rates.currcy_type,success
cf4c0bf468424b46********00000019,2025-06-06T10:48:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,group by f_uid;,success
cf4c0bf468424b46********00000019,2025-06-06T10:49:54+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_uid from t_account_usdts_tag0501 where f_balance > 10000;,success
cf4c0bf468424b46********00000019,2025-06-06T10:50:32+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select * from t_account_usdts_tag0501 limit 5;,success
cf4c0bf468424b46********00000019,2025-06-06T10:52:30+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select * from exchange_rate where rate_date = '********' and currcy_type in ('btc', 'eth', 'usdt');,success
cf4c0bf468424b46********00000019,2025-06-06T10:53:35+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,show tables;,success
cf4c0bf468424b46********00000019,2025-06-06T10:55:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,desc t_account_manage_fee_snapshot_stat;,success
cf4c0bf468424b46********00000019,2025-06-06T10:57:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(*) from t_account_manage_fee_snapshot where f_day like '202505%' and f_state = 1;,success
cf4c0bf468424b46********00000019,2025-06-06T11:01:25+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select count(*) from t_account_manage_fee_snapshot where f_day ='********' and f_state = 1;,success
cf4c0bf468424b46********00000019,2025-06-06T11:05:29+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into `t_account_manage_fee_snapshot_tag0501` select * from t_account_manage_fee_snapshot where f_day = ********;,success
cf4c0bf468424b46********00000019,2025-06-06T11:06:29+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,CREATE TABLE `t_account_usdts_tag05` (,success
cf4c0bf468424b46********00000019,2025-06-06T11:06:29+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,`f_uid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'uid',,success
cf4c0bf468424b46********00000019,2025-06-06T11:06:29+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,`f_balance` decimal(36,18) unsigned NOT NULL DEFAULT '0.****************00' COMMENT '快照余额',success
cf4c0bf468424b46********00000019,2025-06-06T11:06:32+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='快照余额tag05';,success
cf4c0bf468424b46********00000019,2025-06-06T11:06:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(select currcy_type, usdt_rate from exchange_rate where rate_date = '********') rates ON tags.f_currency = rates.currcy_type,success
cf4c0bf468424b46********00000019,2025-06-06T11:06:38+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,insert into t_account_usdts_tag05 select f_uid, sum(f_balance * rates.usdt_rate) as f_balance from t_account_manage_fee_snapshot_tag0501 tags inner join ,success
cf4c0bf468424b46********00000019,2025-06-06T11:06:41+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,(select currcy_type, usdt_rate from exchange_rate where rate_date = '********') rates ON tags.f_currency = rates.currcy_tygroup by f_uid;,success
cf4c0bf468424b46********00000019,2025-06-06T11:07:44+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_uid from t_account_usdts_tag05 where f_balance > 10000;,success
cf4c0bf468424b46********00000019,2025-06-06T11:29:28+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,show tables;,success
cf4c0bf468424b46********00000019,2025-06-06T11:30:37+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use proamsdwdb;,success
cf4c0bf468424b46********00000019,2025-06-06T11:30:43+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,show tables;,success
cf4c0bf468424b46********00000019,2025-06-06T11:31:00+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,desc t_account_user_info;,success
cf4c0bf468424b46********00000019,2025-06-06T11:36:48+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use otc_user;,success
cf4c0bf468424b46********00000019,2025-06-06T11:37:20+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select distinct is_real_bind from user;,success
cf4c0bf468424b46********00000019,2025-06-06T11:38:39+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,use hbg_trade; ,success
cf4c0bf468424b46********00000019,2025-06-06T11:42:53+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_uid from t_account_usdts_tag05 where f_balance > 10000 and f_uid not in(select id from otc_user.user where is_eeal_bind = 1);,success
cf4c0bf468424b46********00000019,2025-06-06T11:43:00+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_uid from t_account_usdts_tag05 where f_balance > 10000 and f_uid not in(select id from otc_user.user where is_real_bind = 1);,success
cf4c0bf468424b46********00000019,2025-06-06T11:49:15+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select id from otc_user.user where id = *********;,success
cf4c0bf468424b46********00000019,2025-06-06T11:49:33+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,desc otc_user.user;,success
cf4c0bf468424b46********00000019,2025-06-06T11:49:47+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select id from otc_user.user where show_id = *********;,success
cf4c0bf468424b46********00000019,2025-06-06T11:50:19+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_uid from t_account_usdts_tag05 where f_balance > 10000 and f_uid not in(select show_id from otc_user.user where is_real_bind = 1);,success
cf4c0bf468424b46********00000019,2025-06-06T11:51:10+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select id from otc_user.user where show_id = *********;,success
cf4c0bf468424b46********00000019,2025-06-06T11:58:50+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_uid from t_account_usdts_tag05 where f_balance > 10000 and f_uid in(select show_id from otc_user.user where is_real_bind = 0 or is_real_bind is null);,success
cf4c0bf468424b46********00000019,2025-06-06T12:00:58+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select distinct auth_level from otc_user.user_identity_auth;,success
cf4c0bf468424b46********00000019,2025-06-06T12:04:16+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,select f_uid from t_account_usdts_tag05 where f_balance > 10000 and f_uid not in(select user_id from otc_user.user_identity_auth where auth_level in(1,2,3));,success
cf4c0bf468424b46********00000019,2025-06-06T12:08:47+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
cf4c0bf468424b46********00000019,2025-06-06T12:08:50+08:00,cmd.Command,xiexinyu,*************,miniglobal-devops,***********,exit,success
2fdac47768426adb********00000019,2025-06-06T12:13:21+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,mysql -u superdba -P 3614 -p -h master-3614.docsl.com,success
2fdac47768426adb********00000019,2025-06-06T12:14:51+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,use hbg_trade; ,success
2fdac47768426adb********00000019,2025-06-06T12:16:24+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********, select f_uid from t_account_usdts_tag05 where f_balance > 10000 and f_uid not in (select f_user_id from huobi_platform_kyc.t_user_kyc_auth where f_auth_level > 0);,success
2fdac47768426adb********00000019,2025-06-06T12:17:49+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,select * from huobi_platform_kyc.t_user_kyc_auth where f_user_id = *********;,success
2fdac47768426adb********00000019,2025-06-06T12:18:04+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,desc huobi_platform_kyc.t_user_kyc_auth;,success
2fdac47768426adb********00000019,2025-06-06T12:18:18+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,select * from huobi_platform_kyc.t_user_kyc_auth where f_uid = *********;,success
2fdac47768426adb********00000019,2025-06-06T12:18:57+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********, select f_uid from t_account_usdts_tag05 where f_balance > 10000 and f_uid not in (select f_uid from huobi_platform_kyc.t_user_kyc_auth where f_auth_level > 0);,success
2fdac47768426adb********00000019,2025-06-06T12:25:12+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,select f_uid from t_account_usdts_tag05 where f_balance > 10000 ,success
2fdac47768426adb********00000019,2025-06-06T12:25:12+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,and f_uid not in (select f_uid from huobi_platform_kyc.t_user_kyc_auth where f_auth_level > 0) ,success
2fdac47768426adb********00000019,2025-06-06T12:25:13+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,and f_uid not in(select user_id from otc_user.user_identity_auth where auth_level in(1,2,3)); ,success
2fdac47768426adb********00000019,2025-06-06T12:27:57+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,select f_uid from t_account_usdts_tag05 where f_balance > 10000 ,success
2fdac47768426adb********00000019,2025-06-06T12:27:57+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,and f_uid not in (select f_uid from huobi_platform_kyc.t_user_kyc_auth where f_auth_level > 0) ,success
2fdac47768426adb********00000019,2025-06-06T12:27:59+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,and f_uid in(select show_id from otc_user.user where is_real_bind = 0 or is_real_bind is null);,success
2fdac47768426adb********00000019,2025-06-06T12:32:11+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,use huobi_platform_kyc；,success
2fdac47768426adb********00000019,2025-06-06T12:32:14+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,use huobi_platform_kyc；,success
2fdac47768426adb********00000019,2025-06-06T12:32:18+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,use huobi_platform_kyc;,success
2fdac47768426adb********00000019,2025-06-06T12:32:22+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,show tables;,success
2fdac47768426adb********00000019,2025-06-06T12:36:21+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,use hbg_trade; ,success
2fdac47768426adb********00000019,2025-06-06T12:36:33+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,select f_uid from t_account_usdts_tag05 where f_balance > 10000 ,success
2fdac47768426adb********00000019,2025-06-06T12:36:33+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,and f_uid not in (select f_uid from huobi_platform_kyc.t_user_kyc_auth where f_auth_level  in(1,2,3)) ,success
2fdac47768426adb********00000019,2025-06-06T12:36:34+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,and f_uid in(select show_id from otc_user.user where is_real_bind = 0);,success
2fdac47768426adb********00000019,2025-06-06T12:38:50+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,select f_uid from t_account_usdts_tag05 where f_balance > 10000 ,success
2fdac47768426adb********00000019,2025-06-06T12:38:50+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,and f_uid not in (select f_uid from huobi_platform_kyc.t_user_kyc_auth where f_auth_level  in(1,2,3)) ,success
2fdac47768426adb********00000019,2025-06-06T12:38:50+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,and f_uid not in(select show_id from otc_user.user where is_real_bind = 1);,success
2fdac47768426adb********00000019,2025-06-06T12:39:25+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,select f_uid from t_account_usdts_tag05 where f_balance > 10000 ,success
2fdac47768426adb********00000019,2025-06-06T12:39:25+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,and f_uid not in (select f_uid from huobi_platform_kyc.t_user_kyc_auth where f_auth_level  in(1,2,3)) ,success
2fdac47768426adb********00000019,2025-06-06T12:39:26+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,and f_uid not in(select show_id from otc_user.user where is_real_bind = 1);,success
2fdac47768426adb********00000019,2025-06-06T12:42:24+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,select f_uid from t_account_usdts_tag05 where f_balance > 10000 ,success
2fdac47768426adb********00000019,2025-06-06T12:42:24+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,and f_uid not in (select f_uid from huobi_platform_kyc.t_user_kyc_auth where f_auth_level  in(1,2,3)) ,success
2fdac47768426adb********00000019,2025-06-06T12:42:27+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,and f_uid in(select show_id from otc_user.user where is_real_bind = 0);,success
2fdac47768426adb********00000019,2025-06-06T12:45:08+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,select distinct real_bind from otc_user.user;,success
2fdac47768426adb********00000019,2025-06-06T12:46:37+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,select f_uid from t_account_usdts_tag05 where f_balance > 10000 ,success
2fdac47768426adb********00000019,2025-06-06T12:46:37+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,and f_uid not in (select f_uid from huobi_platform_kyc.t_user_kyc_auth where f_auth_level  in(1,2,3)) ,success
2fdac47768426adb********00000019,2025-06-06T12:46:39+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,and f_uid in(select show_id from otc_user.user where real_bind = 0);,success
2fdac47768426adb********00000019,2025-06-06T12:50:08+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,select f_uid from t_account_usdts_tag05 where f_balance > 10000 ,success
2fdac47768426adb********00000019,2025-06-06T12:50:08+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,and f_uid not in (select f_uid from huobi_platform_kyc.t_user_kyc_auth where f_auth_level  in(1,2,3)) ,success
2fdac47768426adb********00000019,2025-06-06T12:50:09+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,and f_uid in(select show_id from otc_user.user where real_bind = 0 or is_real_bind = 0);,success
2fdac47768426adb********00000019,2025-06-06T12:53:06+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,select f_uid from t_account_usdts_tag05 where f_balance > 10000 ,success
2fdac47768426adb********00000019,2025-06-06T12:53:06+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,and f_uid not in (select f_uid from huobi_platform_kyc.t_user_kyc_auth where f_auth_level  in(1,2,3)) ,success
2fdac47768426adb********00000019,2025-06-06T12:53:07+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,and f_uid in(select show_id from otc_user.user where real_bind = 0 or is_real_bind = 0 or (real_bind is null and is_real_bind is null));,success
2fdac47768426adb********00000019,2025-06-06T13:01:26+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,select f_uid from t_account_usdts_tag05 where f_balance > 10000 ,success
2fdac47768426adb********00000019,2025-06-06T13:01:26+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,and f_uid not in(select user_id from otc_user.user_identity_auth where auth_level in(1,2,3)) ,success
2fdac47768426adb********00000019,2025-06-06T13:01:27+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,and f_uid in(select show_id from otc_user.user where real_bind = 0 or is_real_bind = 0 or (real_bind is null and is_real_bind is null));,success
2fdac47768426adb********00000019,2025-06-06T13:22:37+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,exit,success
2fdac47768426adb********00000019,2025-06-06T13:22:38+08:00,cmd.Command,xiexinyu,42.200.83.193,miniglobal-devops,***********,exit,success
de86e365684681b5********00000046,2025-06-09T14:39:49+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '.';cd '.';cd '.';pwd;ls -la,success
eed45e9c68468201********00000046,2025-06-09T14:41:34+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,cd /data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib,success
eed45e9c68468201********00000046,2025-06-09T14:42:13+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,ll,success
805b29256846824f********00000046,2025-06-09T14:42:23+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '.';cd '/root/';cd '/root/';pwd;ls -la,success
0edec0c868468254********00000046,2025-06-09T14:42:28+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/root/';cd '/root/../';cd '/root/../';pwd;ls -la,success
9da8ed04684682af********00000046,2025-06-09T14:43:59+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/';cd '/data/';cd '/data/';pwd;ls -la,success
cfe73c29684682b2********00000046,2025-06-09T14:44:02+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/data/';cd '/data/monitor/';cd '/data/monitor/';pwd;ls -la,success
22b32192684682b8********00000046,2025-06-09T14:44:08+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/data/monitor/';cd '/data/monitor/e10-server0/';cd '/data/monitor/e10-server0/';pwd;ls -la,success
3a60b7d1684682bb********00000046,2025-06-09T14:44:11+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/data/monitor/e10-server0/';cd '/data/monitor/e10-server0/weaver-secondev-service/';cd '/data/monitor/e10-server0/weaver-secondev-service/';pwd;ls -la,success
c8eee442684682c2********00000046,2025-06-09T14:44:18+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/data/monitor/e10-server0/weaver-secondev-service/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/';pwd;ls -la,success
0d41f78f684682c4********00000046,2025-06-09T14:44:20+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/';pwd;ls -la,success
b7b20a36684682c6********00000046,2025-06-09T14:44:22+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/';pwd;ls -la,success
74376e4b684682ca********00000046,2025-06-09T14:44:26+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';pwd;ls -la,success
bd0490b468468351********0000003a,2025-06-09T14:46:43+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ll,success
7c41db9b68468387********00000046,2025-06-09T14:47:35+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';pwd;ls -la,success
6f0f2f2d68468387********00000046,2025-06-09T14:47:35+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; rm -f -r '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/_secondev-shenglinwanze.jar',success
bd0490b468468351********0000003a,2025-06-09T14:48:03+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,crontab -l,success
c8b8fe8668469a4b********00000046,2025-06-09T16:24:43+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';pwd;ls -la,success
1c358c9168469a5b********00000046,2025-06-09T16:24:59+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; rm -f -r '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/_secondev-shenglinwanze.jar',success
8060aed968469a5c********00000046,2025-06-09T16:25:00+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';pwd;ls -la,success
eed45e9c68468201********00000046,2025-06-09T16:30:29+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,ll,success
85dbeed968469c0c********00000046,2025-06-09T16:32:12+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,while true; do sleep 1;head -v -n 8 /proc/meminfo; head -v -n 2 /proc/stat /proc/version /proc/uptime /proc/loadavg /proc/sys/fs/file-nr /proc/sys/kernel/hostname; tail -v -n 32 /proc/net/dev;echo '==> /proc/df <==';df -l;echo '==> /proc/who <==';who;echo '==> /proc/end <==';echo '##Moba##'; done,success
b321e6b168469ccc********00000046,2025-06-09T16:35:24+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';pwd;ls -la,success
4e43832068469cd5********00000046,2025-06-09T16:35:33+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; rm -f -r '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/_secondev-shenglinwanze.jar',success
14a2b08e68469cd5********00000046,2025-06-09T16:35:33+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';pwd;ls -la,success
4815190168469cde********00000046,2025-06-09T16:35:42+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';pwd;ls -la,success
45ff3b7968469cf********000000046,2025-06-09T16:36:00+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS; rm -f -r '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/_secondev-workflow.jar',success
00519c4668469cf1********00000046,2025-06-09T16:36:01+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';pwd;ls -la,success
0396104868469cf7********00000046,2025-06-09T16:36:07+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';pwd;ls -la,success
803bb69c68469d04********00000046,2025-06-09T16:36:20+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';pwd;ls -la,success
3b6721a268469d05********00000046,2025-06-09T16:36:21+08:00,file.Download,fwsys,*************,oa-E10-db,************,_secondev-restful-sync.jar,success
44b4590468469d06********00000046,2025-06-09T16:36:22+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';pwd;ls -la,success
e26cda9868469d8a********00000046,2025-06-09T16:38:34+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';pwd;ls -la,success
eed45e9c68468201********00000046,2025-06-09T16:41:04+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,rm -rf _secondev-workflow.jar ,success
eed45e9c68468201********00000046,2025-06-09T16:41:11+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,rm -rf _secondev-shenglinwanze.jar ,success
1690012068469e69********00000046,2025-06-09T16:42:17+08:00,cmd.Command,fwsys,*************,oa-E10-db,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';cd '/data/monitor/e10-server0/weaver-secondev-service/webapps/ROOT/WEB-INF/lib/';pwd;ls -la,success
bd0490b468468351********0000003a,2025-06-09T17:30:55+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cd script/,success
bd0490b468468351********0000003a,2025-06-09T17:30:57+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ll,success
bd0490b468468351********0000003a,2025-06-09T17:35:19+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,touch send_teams_txt_acc.sh,success
bd0490b468468351********0000003a,2025-06-09T17:35:29+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,chmod +x send_teams_txt_acc.sh ,success
bd0490b468468351********0000003a,2025-06-09T17:38:02+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,      "contentType": "application/vnd.microsoft.card.adaptive",,success
bd0490b468468351********0000003a,2025-06-09T17:38:02+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,  "type": "message",,success
bd0490b468468351********0000003a,2025-06-09T17:38:02+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,  "attachments": [,success
bd0490b468468351********0000003a,2025-06-09T17:38:02+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,    {,success
bd0490b468468351********0000003a,2025-06-09T17:38:02+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,-d '{,success
bd0490b468468351********0000003a,2025-06-09T17:38:02+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,      "content": {,success
bd0490b468468351********0000003a,2025-06-09T17:38:02+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "type": "AdaptiveCard",,success
bd0490b468468351********0000003a,2025-06-09T17:38:02+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "body": [,success
bd0490b468468351********0000003a,2025-06-09T17:38:02+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,          {,success
bd0490b468468351********0000003a,2025-06-09T17:38:02+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            "type": "TextBlock",,success
bd0490b468468351********0000003a,2025-06-09T17:38:02+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,            "text": "这里是你的消息内容",success
bd0490b468468351********0000003a,2025-06-09T17:38:02+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,-H "Content-Type: application/json" \,success
bd0490b468468351********0000003a,2025-06-09T17:38:02+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        ],,success
bd0490b468468351********0000003a,2025-06-09T17:38:02+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "$schema": "http://adaptivecards.io/schemas/adaptive-card.json",,success
bd0490b468468351********0000003a,2025-06-09T17:38:02+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,        "version": "1.0",success
bd0490b468468351********0000003a,2025-06-09T17:38:02+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,      },success
bd0490b468468351********0000003a,2025-06-09T17:38:02+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,    },success
bd0490b468468351********0000003a,2025-06-09T17:38:02+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,  ],success
bd0490b468468351********0000003a,2025-06-09T17:38:02+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,          },success
bd0490b468468351********0000003a,2025-06-09T17:38:02+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,}',success
bd0490b468468351********0000003a,2025-06-09T17:40:31+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,vi send_teams_txt_acc.sh ,success
bd0490b468468351********0000003a,2025-06-09T17:40:50+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,vi send_teams_txt_acc.sh ,success
bd0490b468468351********0000003a,2025-06-09T17:41:17+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,./send_teams_txt_acc.sh ,success
bd0490b468468351********0000003a,2025-06-09T17:41:22+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,./send_teams_txt_acc.sh 123,success
bd0490b468468351********0000003a,2025-06-09T17:52:49+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,touch filter_list.txt,success
bd0490b468468351********0000003a,2025-06-09T17:52:56+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,vi filter_list.txt ,success
bd0490b468468351********0000003a,2025-06-09T17:55:54+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ll,success
bd0490b468468351********0000003a,2025-06-09T17:55:59+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,touch check_mailnickname.sh,success
bd0490b468468351********0000003a,2025-06-09T17:56:10+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,chmod +x check_mailnickname.sh ,success
bd0490b468468351********0000003a,2025-06-09T17:56:23+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,vi check_mailnickname.sh ,success
bd0490b468468351********0000003a,2025-06-09T17:59:24+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ll,success
bd0490b468468351********0000003a,2025-06-09T17:59:32+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,vi check_mailnickname.sh ,success
bd0490b468468351********0000003a,2025-06-09T18:00:36+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,./check_mailnickname.sh ,success
bd0490b468468351********0000003a,2025-06-09T18:00:58+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cat check_mailnickname.sh ,success
bd0490b468468351********0000003a,2025-06-09T18:03:33+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,vi check_mailnickname.sh ,success
bd0490b468468351********0000003a,2025-06-09T18:03:47+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,./check_mailnickname.sh ,success
bd0490b468468351********0000003a,2025-06-09T18:08:15+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,vi check_mailnickname.sh ,success
bd0490b468468351********0000003a,2025-06-09T18:10:08+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,./check_mailnickname.sh ,success
bd0490b468468351********0000003a,2025-06-09T18:23:13+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ll,success
bd0490b468468351********0000003a,2025-06-09T18:23:22+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,vi send_teams_txt_acc.sh ,success
bd0490b468468351********0000003a,2025-06-09T18:25:13+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,pwd,success
bd0490b468468351********0000003a,2025-06-09T18:26:40+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,crontab -l,success
bd0490b468468351********0000003a,2025-06-09T18:26:59+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,crontab -e,success
bd0490b468468351********0000003a,2025-06-09T18:27:34+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,crontab -l,success
bd0490b468468351********0000003a,2025-06-09T18:32:41+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,ll,success
bd0490b468468351********0000003a,2025-06-09T18:32:50+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cat check_mailnickname.log ,success
bd0490b468468351********0000003a,2025-06-09T18:33:10+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,vi check_mailnickname.sh ,success
bd0490b468468351********0000003a,2025-06-09T18:33:50+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,vi check_mailnickname.sh ,success
bd0490b468468351********0000003a,2025-06-09T18:33:56+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cat check_mailnickname.log ,success
bd0490b468468351********0000003a,2025-06-09T18:33:57+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cat check_mailnickname.log ,success
bd0490b468468351********0000003a,2025-06-09T18:33:59+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cat check_mailnickname.log ,success
bd0490b468468351********0000003a,2025-06-09T18:34:03+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cat check_mailnickname.log ,success
bd0490b468468351********0000003a,2025-06-09T18:34:04+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cat check_mailnickname.log ,success
bd0490b468468351********0000003a,2025-06-09T18:34:05+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cat check_mailnickname.log ,success
bd0490b468468351********0000003a,2025-06-09T18:34:05+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cat check_mailnickname.log ,success
bd0490b468468351********0000003a,2025-06-09T18:34:06+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cat check_mailnickname.log ,success
bd0490b468468351********0000003a,2025-06-09T18:34:07+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cat check_mailnickname.log ,success
bd0490b468468351********0000003a,2025-06-09T18:34:08+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cat check_mailnickname.log ,success
bd0490b468468351********0000003a,2025-06-09T18:34:09+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cat check_mailnickname.log ,success
bd0490b468468351********0000003a,2025-06-09T18:34:11+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cat check_mailnickname.log ,success
bd0490b468468351********0000003a,2025-06-09T18:34:12+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cat check_mailnickname.log ,success
bd0490b468468351********0000003a,2025-06-09T18:34:13+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cat check_mailnickname.log ,success
bd0490b468468351********0000003a,2025-06-09T18:34:14+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cat check_mailnickname.log ,success
bd0490b468468351********0000003a,2025-06-09T18:34:15+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cat check_mailnickname.log ,success
bd0490b468468351********0000003a,2025-06-09T18:34:55+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cat check_mailnickname.log ,success
bd0490b468468351********0000003a,2025-06-09T18:34:57+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cat check_mailnickname.log ,success
bd0490b468468351********0000003a,2025-06-09T18:34:58+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cat check_mailnickname.log ,success
bd0490b468468351********0000003a,2025-06-09T18:34:59+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cat check_mailnickname.log ,success
bd0490b468468351********0000003a,2025-06-09T18:35:00+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cat check_mailnickname.log ,success
bd0490b468468351********0000003a,2025-06-09T18:35:01+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cat check_mailnickname.log ,success
bd0490b468468351********0000003a,2025-06-09T18:35:17+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cat check_mailnickname.log ,success
bd0490b468468351********0000003a,2025-06-09T18:36:23+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cat check_mailnickname.log ,success
bd0490b468468351********0000003a,2025-06-09T18:38:29+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cat check_mailnickname.log ,success
bd0490b468468351********0000003a,2025-06-09T18:39:33+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,cat check_mailnickname.log ,success
3d6891cb6846ba31********0000003a,2025-06-09T18:40:49+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,unset LANG LANGUAGE LC_CTYPE LC_COLLATE LC_MONETARY LC_NUMERIC LC_TIME LC_MESSAGES LC_ALL HUMAN_BLOCKS LS_COLORS;cd '.';cd '.';cd '.';pwd;ls -la,success
a6c5e5ad6846ba31********0000003a,2025-06-09T18:40:49+08:00,cmd.Command,zengshengfa,**************,zabbix-server,************,while true; do sleep 1;head -v -n 8 /proc/meminfo; head -v -n 2 /proc/stat /proc/version /proc/uptime /proc/loadavg /proc/sys/fs/file-nr /proc/sys/kernel/hostname; tail -v -n 32 /proc/net/dev;echo '==> /proc/df <==';df -l;echo '==> /proc/who <==';who;echo '==> /proc/end <==';echo '##Moba##'; done,success
3e5f65606853e0f****************d,2025-06-19T18:05:39+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,ls,success
3e5f65606853e0f****************d,2025-06-19T18:05:43+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,date,success
3e5f65606853e0f****************d,2025-06-19T18:07:43+08:00,cmd.Command,avenir_user01,**************,miniglobal-devops,***********,ll,success
