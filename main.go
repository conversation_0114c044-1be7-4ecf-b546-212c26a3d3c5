package main

import (
	"context"
	"crypto/ed25519"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"log"
	"net/url"
	"strings"
	"time"
)

const (
	pubKey     = "7a9d139499cc76e4a777d07e4eddbb25252dd7c9017e15464975a1cc42110cff"
	privateKey = "e38efa909191da2db2edfbd5d9acab8a32b3d90b1de088cf03f6836a23c73d4c"
)

func GenerateSignature(ctx context.Context, apiKey, apiSecret, method, rawUrl string, requestBody string) (headers map[string]string, err error) {
	// 解码私钥
	privateKey, err := hex.DecodeString(apiSecret)
	if err != nil {
		return nil, err
	}

	u, err := url.Parse(rawUrl)
	if err != nil {
		return nil, err
	}
	// 构造请求体
	timestamp := fmt.Sprintf("%d", time.Now().UnixMilli())

	message := fmt.Sprintf("%s|%s|%s|%s|%s", strings.ToUpper(method), u.Path, timestamp, u.Query().Encode(), requestBody)

	// 计算两次 SHA256 哈希
	hash1 := sha256.Sum256([]byte(message))
	hash2 := sha256.Sum256(hash1[:])
	doubleHash := hash2[:]

	// 使用私钥签名

	signature := ed25519.Sign(ed25519.NewKeyFromSeed(privateKey), doubleHash)
	signatureHex := hex.EncodeToString(signature)

	// 设置请求头
	headers = make(map[string]string)
	headers["X-Api-Signature"] = signatureHex
	headers["X-Api-Timestamp"] = timestamp
	headers["X-Api-Key"] = apiKey

	return headers, nil
}

// GenerateKey 生成ed25519密钥对并以16进制编码打印
func GenerateKey() {
	// 生成ed25519密钥对
	publicKey, privateKey, err := ed25519.GenerateKey(rand.Reader)
	if err != nil {
		log.Fatalf("生成密钥失败: %v", err)
	}

	// 将密钥转换为16进制编码
	publicKeyHex := hex.EncodeToString(publicKey)
	privateKeyHex := hex.EncodeToString(privateKey.Seed())

	// 打印密钥
	fmt.Println("=== Ed25519 密钥对 ===")
	fmt.Printf("公钥 (Public Key): %s\n", publicKeyHex)
	fmt.Printf("私钥 (Private Key): %s\n", privateKeyHex)
	fmt.Println()

	// 打印密钥长度信息
	fmt.Printf("公钥长度: %d 字节 (%d 十六进制字符)\n", len(publicKey), len(publicKeyHex))
	fmt.Printf("私钥长度: %d 字节 (%d 十六进制字符)\n", len(privateKey), len(privateKeyHex))
	fmt.Println()

	// 验证密钥对是否有效
	testMessage := []byte("test message for verification")
	signature := ed25519.Sign(privateKey, testMessage)
	isValid := ed25519.Verify(publicKey, testMessage, signature)

	fmt.Printf("密钥对验证: %v\n", isValid)
	if isValid {
		fmt.Println("✅ 密钥对生成成功且有效!")
	} else {
		fmt.Println("❌ 密钥对验证失败!")
	}
}

// Your initialization function
func main() {
	// 生成并打印ed25519密钥对
	GenerateKey()
}
