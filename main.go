package main

import (
	"encoding/json"
	"fmt"
	"github.com/IBM/sarama"
	"io"
	"log"
	"os"
)

func main() {
	// 示例：使用新的方法将文件发送到Kafka
	config := &KafkaConfig{
		Brokers: []string{"10.2.37.45:9092", "10.2.37.235:9092", "10.2.37.103:9092"}, // 根据您的Kafka集群配置修改
	}

	err := SendFileToKafkaWithConfig("./bastion_log_new", "log_test", config)
	if err != nil {
		log.Fatalf("发送文件到Kafka失败: %v", err)
	}
	log.Println("文件成功发送到Kafka")
}

// KafkaConfig Kafka配置结构体
type KafkaConfig struct {
	Brokers []string // Kafka broker地址列表
}

// SendFileToKafkaWithConfig 读取包含JSON数组的文件并将内容发送到Kafka topic (使用自定义配置)
func SendFileToKafkaWithConfig(filename, topic string, kafkaConfig *KafkaConfig) error {
	// 创建Kafka producer配置
	config := sarama.NewConfig()
	config.Producer.Return.Successes = true
	config.Producer.RequiredAcks = sarama.WaitForAll
	config.Producer.Retry.Max = 3

	// 创建Kafka producer
	producer, err := sarama.NewSyncProducer(kafkaConfig.Brokers, config)
	if err != nil {
		return fmt.Errorf("创建Kafka producer失败: %w", err)
	}
	defer producer.Close()

	// 读取文件
	file, err := os.Open(filename)
	if err != nil {
		return fmt.Errorf("打开文件失败: %w", err)
	}
	defer file.Close()

	data, err := io.ReadAll(file)
	if err != nil {
		return fmt.Errorf("读取文件失败: %w", err)
	}

	// 解析JSON数组
	var jsonArray []json.RawMessage
	err = json.Unmarshal(data, &jsonArray)
	if err != nil {
		return fmt.Errorf("解析JSON数组失败: %w", err)
	}

	// 发送每个JSON对象到Kafka
	for i, jsonObj := range jsonArray {
		message := &sarama.ProducerMessage{
			Topic: topic,
			Value: sarama.StringEncoder(jsonObj),
		}

		partition, offset, err := producer.SendMessage(message)
		if err != nil {
			return fmt.Errorf("发送消息到Kafka失败 (索引 %d): %w", i, err)
		}

		log.Printf("消息成功发送到Kafka - Topic: %s, Partition: %d, Offset: %d", topic, partition, offset)
	}

	log.Printf("总共发送了 %d 条消息到topic: %s", len(jsonArray), topic)
	return nil
}
