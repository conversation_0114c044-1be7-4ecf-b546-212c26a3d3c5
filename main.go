package main

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	"gopkg.in/twindagger/httpsig.v1"
)

const (
	JumpServerHost       = "https://sec-jumpserver.yorkapp.com"
	CommandLogApi        = "/api/v1/terminal/commands/?command_storage_id=%s&order=%s&date_from=%s&date_to=%s&offset=%d&limit=%d"
	LoginLogApi          = "/api/v1/audits/login-logs/?order=%s&date_from=%s&date_to=%s&offset=%d&limit=%d"
	PasswordChangeLogApi = "/api/v1/audits/password-change-logs/?order=%s&date_from=%s&date_to=%s&offset=%d&limit=%d"
	OperateLogApi        = "/api/v1/audits/operate-logs/?order=%s&date_from=%s&date_to=%s&offset=%d&limit=%d"
	AccessKey            = "xxx"
	AccessSecret         = "xxx"
	CommandStorageID     = "fd1bd8de-080a-45c1-ba26-44f2163d1670" // 命令存储的storageID，右上角设置->存储设置->命令存储
)

func Sign(r *http.Request) error {
	headers := []string{"(request-target)", "date"}
	signer, err := httpsig.NewRequestSigner(AccessKey, AccessSecret, "hmac-sha256")
	if err != nil {
		return err
	}
	return signer.SignRequest(r, headers, nil)
}

type Result struct {
	Count    int64         `json:"count"`
	Next     *string       `json:"next"`
	Previous *string       `json:"previous"`
	Results  []interface{} `json:"results"`
}

func DoGetRequest(method, api string) (res []byte, err error) {
	req, err := http.NewRequest(method, fmt.Sprintf("%s%s", JumpServerHost, api), nil)
	if err != nil {
		return nil, err
	}
	if err = Sign(req); err != nil {
		return nil, err
	}
	httpResp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer httpResp.Body.Close()
	return io.ReadAll(httpResp.Body)
}

// GetCommands 获取terminal command日志
// Parameters:
//   - from: 起始毫秒时间戳
//   - to: 结束毫秒时间戳
func GetCommands(from, to int64) (logs []interface{}, err error) {
	dateFrom := url.QueryEscape(time.UnixMilli(from).Format("2006-01-02T15:04:05.999Z07:00"))
	dateTo := url.QueryEscape(time.UnixMilli(to).Format("2006-01-02T15:04:05.999Z07:00"))
	for offset, limit := 0, 50; ; offset += limit {
		api := fmt.Sprintf(CommandLogApi, CommandStorageID, "-timestamp", dateFrom, dateTo, offset, limit)
		body, err := DoGetRequest(http.MethodGet, api)
		if err != nil {
			return logs, err
		}
		res := &Result{}
		if err := json.Unmarshal(body, res); err != nil {
			return logs, err
		}
		logs = append(logs, res.Results...)
		if len(res.Results) < limit {
			break
		}
	}
	return logs, nil
}

// GetLoginLogs 获取login日志
// Parameters:
//   - from: 起始毫秒时间戳
//   - to: 结束毫秒时间戳
func GetLoginLogs(from, to int64) (logs []interface{}, err error) {
	dateFrom := url.QueryEscape(time.UnixMilli(from).Format("2006-01-02T15:04:05.999Z07:00"))
	dateTo := url.QueryEscape(time.UnixMilli(to).Format("2006-01-02T15:04:05.999Z07:00"))
	for offset, limit := 0, 50; ; offset += limit {
		api := fmt.Sprintf(LoginLogApi, "-timestamp", dateFrom, dateTo, offset, limit)
		body, err := DoGetRequest(http.MethodGet, api)
		if err != nil {
			return logs, err
		}
		res := &Result{}
		if err := json.Unmarshal(body, res); err != nil {
			return logs, err
		}
		logs = append(logs, res.Results...)
		if len(res.Results) < limit {
			break
		}
	}
	return logs, nil
}

// GetPasswordChangeLogs 获取改密日志
// Parameters:
//   - from: 起始毫秒时间戳
//   - to: 结束毫秒时间戳
func GetPasswordChangeLogs(from, to int64) (logs []interface{}, err error) {
	dateFrom := url.QueryEscape(time.UnixMilli(from).Format("2006-01-02T15:04:05.999Z07:00"))
	dateTo := url.QueryEscape(time.UnixMilli(to).Format("2006-01-02T15:04:05.999Z07:00"))
	for offset, limit := 0, 50; ; offset += limit {
		api := fmt.Sprintf(PasswordChangeLogApi, "-timestamp", dateFrom, dateTo, offset, limit)
		body, err := DoGetRequest(http.MethodGet, api)
		if err != nil {
			return logs, err
		}
		res := &Result{}
		if err := json.Unmarshal(body, res); err != nil {
			return logs, err
		}
		logs = append(logs, res.Results...)
		if len(res.Results) < limit {
			break
		}
	}
	return logs, nil
}

// GetOperateLogs 获取改密日志
// Parameters:
//   - from: 起始毫秒时间戳
//   - to: 结束毫秒时间戳
func GetOperateLogs(from, to int64) (logs []interface{}, err error) {
	dateFrom := url.QueryEscape(time.UnixMilli(from).Format("2006-01-02T15:04:05.999Z07:00"))
	dateTo := url.QueryEscape(time.UnixMilli(to).Format("2006-01-02T15:04:05.999Z07:00"))
	for offset, limit := 0, 50; ; offset += limit {
		api := fmt.Sprintf(OperateLogApi, "-timestamp", dateFrom, dateTo, offset, limit)
		body, err := DoGetRequest(http.MethodGet, api)
		if err != nil {
			return logs, err
		}
		res := &Result{}
		if err := json.Unmarshal(body, res); err != nil {
			return logs, err
		}
		logs = append(logs, res.Results...)
		if len(res.Results) < limit {
			break
		}
	}
	return logs, nil
}

// Your initialization function
func main() {
	logs, err := GetOperateLogs(time.Now().Add(-time.Hour*24*60).UnixMilli(), time.Now().UnixMilli())
	fmt.Println(len(logs), err)
}
