package main

import (
	"encoding/json"
	"fmt"
	"io"
	"os"
	"time"
)

func main() {
	f, err := os.Open("./bastion_log")
	if err != nil {
		panic(err)
	}
	defer f.Close()
	data, err := io.ReadAll(f)
	if err != nil {
		fmt.Println(err)
	}
	messages := make([]*Msg, 0)
	err = json.Unmarshal(data, &messages)
	if err != nil {
		panic(err)
	}
	parsedMessages := make([]*ParsedMsg, 0)
	for _, msg := range messages {
		parsedMessages = append(parsedMessages, &ParsedMsg{
			Source:       "log_service",
			EventType:    msg.OperateName,
			Content:      msg.OperateName,
			Region:       "cn-hongkong",
			OwnerID:      "5358279038055445",
			LogLevel:     "0",
			Result:       msg.Result,
			UserName:     msg.UserName,
			UserClientIp: msg.ClientIp,
			UserID:       msg.UserId,
			Topic:        "bastionhost",
			InstanceID:   "bastionhost_std_intl-sg-5r53bokcn01",
			Timestamp:    time.Unix(int64(msg.Time), 0).Format(time.RFC3339),
		})
	}
	newData, err := json.Marshal(parsedMessages)
	if err != nil {
		panic(err)
	}
	f2, err := os.OpenFile("./bastion_log_new", os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		panic(err)
	}
	defer f2.Close()
	_, err = f2.Write(newData)
	if err != nil {
		panic(err)
	}
}

type Msg struct {
	OperateName  string `json:"OperateName"`
	UserName     string `json:"UserName"`
	RequestId    string `json:"RequestId"`
	UserOriginId string `json:"UserOriginId"`
	UserId       string `json:"UserId"`
	Time         int    `json:"Time"`
	ClientIp     string `json:"ClientIp"`
	Result       string `json:"Result"`
}

type ParsedMsg struct {
	Source       string `json:"source"`
	EventType    string `json:"event_type"`
	Content      string `json:"content"`
	Region       string `json:"region"`
	OwnerID      string `json:"owner_id"`
	LogLevel     string `json:"log_level"`
	Result       string `json:"result"`
	UserName     string `json:"user_name"`
	Topic        string `json:"topic"`
	Timestamp    string `json:"@timestamp"`
	UserClientIp string `json:"user_client_ip"`
	UserID       string `json:"user_id"`
	InstanceID   string `json:"instance_id"`
}
