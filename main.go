package main

import (
	"encoding/csv"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"

	"github.com/IBM/sarama"
)

func main() {
	// 示例：使用新的方法将CSV文件发送到Kafka
	config := &KafkaConfig{
		Brokers: []string{"10.2.37.45:9092", "10.2.37.235:9092", "10.2.37.103:9092"}, // 根据您的Kafka集群配置修改
	}

	err := SendCSVFilesToKafka("./commands", "log_test", config)
	if err != nil {
		log.Fatalf("发送CSV文件到Kafka失败: %v", err)
	}
	log.Println("CSV文件成功发送到Kafka")
}

// KafkaConfig Kafka配置结构体
type KafkaConfig struct {
	Brokers []string // Kafka broker地址列表
}

// SendFileToKafkaWithConfig 读取包含JSON数组的文件并将内容发送到Kafka topic (使用自定义配置)
func SendFileToKafkaWithConfig(filename, topic string, kafkaConfig *KafkaConfig) error {
	// 创建Kafka producer配置
	config := sarama.NewConfig()
	config.Producer.Return.Successes = true
	config.Producer.RequiredAcks = sarama.WaitForAll
	config.Producer.Retry.Max = 3

	// 创建Kafka producer
	producer, err := sarama.NewSyncProducer(kafkaConfig.Brokers, config)
	if err != nil {
		return fmt.Errorf("创建Kafka producer失败: %w", err)
	}
	defer producer.Close()

	// 读取文件
	file, err := os.Open(filename)
	if err != nil {
		return fmt.Errorf("打开文件失败: %w", err)
	}
	defer file.Close()

	data, err := io.ReadAll(file)
	if err != nil {
		return fmt.Errorf("读取文件失败: %w", err)
	}

	// 解析JSON数组
	var jsonArray []json.RawMessage
	err = json.Unmarshal(data, &jsonArray)
	if err != nil {
		return fmt.Errorf("解析JSON数组失败: %w", err)
	}

	// 发送每个JSON对象到Kafka
	for i, jsonObj := range jsonArray {
		message := &sarama.ProducerMessage{
			Topic: topic,
			Value: sarama.StringEncoder(jsonObj),
		}

		partition, offset, err := producer.SendMessage(message)
		if err != nil {
			return fmt.Errorf("发送消息到Kafka失败 (索引 %d): %w", i, err)
		}

		log.Printf("消息成功发送到Kafka - Topic: %s, Partition: %d, Offset: %d", topic, partition, offset)
	}

	log.Printf("总共发送了 %d 条消息到topic: %s", len(jsonArray), topic)
	return nil
}

// CSVRecord CSV记录结构体
type CSVRecord struct {
	SessionID    string `csv:"SessionID"`
	EventTime    string `csv:"EventTime"`
	EventType    string `csv:"EventType"`
	Username     string `csv:"Username"`
	SourceIP     string `csv:"SourceIP"`
	HostName     string `csv:"HostName"`
	HostAddress  string `csv:"HostAddress"`
	EventContent string `csv:"EventContent"`
	Result       string `csv:"Result"`
}

// JSONRecord 转换后的JSON记录结构体
type JSONRecord struct {
	SessionID       string `json:"session_id"`
	Timestamp       string `json:"@timestamp"`
	EventType       string `json:"event_type"`
	UserName        string `json:"user_name"`
	UserClientIP    string `json:"user_client_ip"`
	ResourceName    string `json:"resource_name"`
	ResourceAddress string `json:"resource_address"`
	Content         string `json:"content"`
	Result          string `json:"result"`
	Source          string `json:"source"`
	Region          string `json:"region"`
	OwnerID         string `json:"owner_id"`
	InstanceID      string `json:"instance_id"`
}

// SendCSVFilesToKafka 读取指定目录下的所有CSV文件，转换为JSON并发送到Kafka
func SendCSVFilesToKafka(csvDir, topic string, kafkaConfig *KafkaConfig) error {
	// 创建Kafka producer配置
	config := sarama.NewConfig()
	config.Producer.Return.Successes = true
	config.Producer.RequiredAcks = sarama.WaitForAll
	config.Producer.Retry.Max = 3

	/*	// 创建Kafka producer
		producer, err := sarama.NewSyncProducer(kafkaConfig.Brokers, config)
		if err != nil {
			return fmt.Errorf("创建Kafka producer失败: %w", err)
		}
		defer producer.Close()*/

	// 读取目录下所有CSV文件
	csvFiles, err := filepath.Glob(filepath.Join(csvDir, "*.csv"))
	if err != nil {
		return fmt.Errorf("读取CSV文件列表失败: %w", err)
	}

	if len(csvFiles) == 0 {
		return fmt.Errorf("在目录 %s 中没有找到CSV文件", csvDir)
	}

	log.Printf("找到 %d 个CSV文件", len(csvFiles))

	var allRecords []JSONRecord
	totalProcessed := 0

	// 处理每个CSV文件
	for _, csvFile := range csvFiles {
		log.Printf("正在处理文件: %s", csvFile)

		records, err := processCSVFile(csvFile)
		if err != nil {
			log.Printf("处理文件 %s 失败: %v", csvFile, err)
			continue
		}

		allRecords = append(allRecords, records...)
		totalProcessed += len(records)
		log.Printf("文件 %s 处理完成，记录数: %d", csvFile, len(records))
	}

	log.Printf("总共处理了 %d 条记录", totalProcessed)

	// 发送所有记录到Kafka
	//for i, record := range allRecords {
	//jsonData, err := json.Marshal(record)
	//if err != nil {
	//	return fmt.Errorf("序列化JSON失败 (索引 %d): %w", i, err)
	//}

	//message := &sarama.ProducerMessage{
	//	Topic: topic,
	//	Value: sarama.StringEncoder(jsonData),
	//}

	//partition, offset, err := producer.SendMessage(message)
	//if err != nil {
	//	return fmt.Errorf("发送消息到Kafka失败 (索引 %d): %w", i, err)
	//}
	//
	//if i%100 == 0 { // 每100条记录打印一次日志
	//	log.Printf("已发送 %d/%d 条消息到Kafka - Topic: %s, Partition: %d, Offset: %d",
	//		i+1, len(allRecords), topic, partition, offset)
	//}
	//}

	//log.Printf("总共发送了 %d 条消息到topic: %s", len(allRecords), topic)
	return nil
}

// processCSVFile 处理单个CSV文件，返回转换后的JSON记录
func processCSVFile(filename string) ([]JSONRecord, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, fmt.Errorf("打开文件失败: %w", err)
	}
	defer file.Close()

	reader := csv.NewReader(file)

	// 读取表头
	headers, err := reader.Read()
	if err != nil {
		return nil, fmt.Errorf("读取CSV表头失败: %w", err)
	}

	// 验证表头是否包含所需字段
	expectedHeaders := []string{"SessionID", "EventTime", "EventType", "Username", "SourceIP", "HostName", "HostAddress", "EventContent", "Result"}
	headerMap := make(map[string]int)
	for i, header := range headers {
		headerMap[header] = i
	}

	fmt.Println(headers)

	for _, expected := range expectedHeaders {
		if _, exists := headerMap["SessionID"]; !exists {
			return nil, fmt.Errorf("CSV文件缺少必需的列: %s", expected)
		}
	}

	var records []JSONRecord
	lineNumber := 1 // 从1开始，因为第0行是表头

	// 读取数据行
	for {
		row, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			log.Printf("读取第 %d 行时出错: %v", lineNumber+1, err)
			lineNumber++
			continue
		}

		// 检查行的列数是否正确
		if len(row) != len(headers) {
			log.Printf("第 %d 行列数不匹配，跳过", lineNumber+1)
			lineNumber++
			continue
		}

		// 转换为JSON记录
		record := JSONRecord{
			SessionID:       strings.TrimSpace(row[headerMap["SessionID"]]),
			Timestamp:       strings.TrimSpace(row[headerMap["EventTime"]]),
			EventType:       strings.TrimSpace(row[headerMap["EventType"]]),
			UserName:        strings.TrimSpace(row[headerMap["Username"]]),
			UserClientIP:    strings.TrimSpace(row[headerMap["SourceIP"]]),
			ResourceName:    strings.TrimSpace(row[headerMap["HostName"]]),
			ResourceAddress: strings.TrimSpace(row[headerMap["HostAddress"]]),
			Content:         strings.TrimSpace(row[headerMap["EventContent"]]),
			Result:          strings.TrimSpace(row[headerMap["Result"]]),
			// 固定字段
			Source:     "log_service",
			Region:     "cn-hongkong",
			OwnerID:    "5358279038055445",
			InstanceID: "bastionhost_std_intl-sg-5r53bokcn01",
		}

		records = append(records, record)
		lineNumber++
	}

	return records, nil
}
